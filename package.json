{"name": "altapplabs", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "DISABLE_ESLINT_PLUGIN=true next build", "build:analyze": "ANALYZE=true next build", "start": "NODE_ENV=production node server.js", "lint": "next lint", "analyze": "node scripts/analyze-bundle.js", "perf": "npm run build && npm run analyze"}, "dependencies": {"@emotion/react": "^11.11.4", "@emotion/styled": "^11.11.0", "@fortawesome/fontawesome-free": "^6.5.1", "@mui/icons-material": "^5.15.12", "@mui/material": "^5.15.12", "@mui/styled-engine-sc": "^6.0.0-alpha.17", "mailersend": "^2.2.0", "mdb-react-ui-kit": "^7.2.0", "next": "14.1.3", "next-images": "^1.8.5", "node-html-parser": "^6.1.12", "react": "^18", "react-alice-carousel": "^2.8.0", "react-dom": "^18", "react-feather": "^2.0.10", "react-hot-toast": "^2.4.1", "react-icons": "^5.0.1", "react-spinners": "^0.13.8", "styled-components": "^6.1.8"}, "devDependencies": {"@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "@types/react-slick": "^0.23.13", "eslint": "^8", "eslint-config-next": "14.1.3", "typescript": "^5"}}