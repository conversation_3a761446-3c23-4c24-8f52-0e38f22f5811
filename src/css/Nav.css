.topNav{
    background:var(--bgColor);
    height: 80px;
    z-index: 99;
    width:100%;
    max-width:100vw;
    position: fixed;
padding:10px;
left:0 !important;
}
.navButtons button{
    border-radius:30px;
color:var(--textColor);
font-weight: bolder;
font-size: 12px;
}
.navButtons button:hover{
    color:white;
    background: rgba(255, 255, 255, 0.329) !important;
}
.sideMenuContainer{
    width:100%;
    height: 100%;
    background:rgba(5, 6, 10, 0.514);
    position:fixed;
    left:0;
    top:0;
    z-index:9999;
}
.sideMenuContainer .sideMenu{
    background:var(--bgColor);
    box-shadow:1px 1px 30px 0px rgb(0, 0, 0);
    width:60%;
    height:100%;
    animation-name:openMenu;
    animation-duration:1s;
    overflow:auto;
    padding:20px;
}
@keyframes openMenu{
    0%{
        transform:translate(-150vw,0)
    }
    100%{
        transform:translate(0vw,0)
    }
}

@keyframes closeMenu{
    0%{
        transform:translate(0,0)
    }
    100%{
        transform:translate(-150vw,0)
    }
}
.sideMenuContent button{ 
 width:100%;
 border-radius: 1px;
 margin-left:-28px; 
 text-align: start;
 border-bottom: 1px solid lightgrey;
 padding:10px 16px; 
 color:var(--textColor);
}
.sideMenuContent button:hover{
 border-left:1px solid white; 
background:rgba(101, 101, 160, 0.603);
border-radius: 5px;
}