import { useRouter } from 'next/router';
import { useEffect, useState } from 'react';
import Head from 'next/head';
import WhiteBold from '@/utils/WhiteBold';
import PText from '@/utils/PText2';
import { ArrowLeft, ExternalLink, Calendar, Users, DollarSign } from 'react-feather';
import <PERSON> from 'next/link';

interface CaseStudy {
  title: string;
  description: string;
  challenge: string;
  solution: string;
  results: string[];
  technologies: string[];
  duration: string;
  teamSize: string;
  budget: string;
  category: string;
  image: string;
  liveUrl?: string;
}

const caseStudies: Record<string, CaseStudy> = {
  'metabloqs': {
    title: 'MetaVerse Ecosystem',
    description: 'Revolutionary Web 3.0 Metaverse platform featuring realistic graphics, real-world locations, and authentic identities.',
    challenge: 'Creating a scalable metaverse platform that could handle thousands of concurrent users while maintaining realistic graphics and seamless Web3 integration.',
    solution: 'Implemented a distributed architecture using Unity 3D, ReadyPlayerMe SDK, and custom blockchain infrastructure to create an immersive virtual world.',
    results: [
      '10,000+ active users within first month',
      '500+ virtual events hosted',
      '1M+ NFT transactions processed',
      '99.9% uptime achieved',
      '$2M+ in virtual real estate sales'
    ],
    technologies: ['Unity 3D', 'Solidity', 'Node.js', 'AWS', 'IPFS', 'ReadyPlayerMe'],
    duration: '8 months',
    teamSize: '12 developers',
    budget: '$500K+',
    category: 'Metaverse & Gaming',
    image: 'https://blockchaintechs.io/wp-content/uploads/2022/12/Metaverse-Ecosystem-2-1.webp',
    liveUrl: 'https://metabloqs.com'
  },
  'fitmint': {
    title: 'FitMint - Move to Earn',
    description: 'Move to Earn cryptocurrency application that incentivizes physical activity through smartwatch integration.',
    challenge: 'Building a reliable fitness tracking system that prevents cheating while providing fair crypto rewards for physical activity.',
    solution: 'Developed advanced motion detection algorithms with smartwatch integration and blockchain-based reward distribution system.',
    results: [
      '50,000+ active users',
      '1M+ workouts tracked',
      '$500K+ in rewards distributed',
      '4.8/5 app store rating',
      'Partnership with 3 major fitness brands'
    ],
    technologies: ['React Native', 'Solidity', 'Node.js', 'MongoDB', 'AWS', 'WearOS'],
    duration: '6 months',
    teamSize: '8 developers',
    budget: '$300K+',
    category: 'Move-to-Earn & Fitness',
    image: 'https://blockchaintechs.io/wp-content/uploads/2022/08/Metaplexar-Investment-Dashboard-Portfolio-Performance-300x237.webp',
    liveUrl: 'https://fitmint.io'
  },
  'nft-auction': {
    title: 'NFT Auction Platform',
    description: 'Premium NFT auction platform for raising capital through community-driven investment.',
    challenge: 'Creating a transparent and secure auction system that builds trust between investors and project creators.',
    solution: 'Built smart contracts with time-locked auctions, automated escrow, and transparent fund distribution mechanisms.',
    results: [
      '$5M+ raised for projects',
      '1,000+ successful auctions',
      '20,000+ registered bidders',
      'Zero security incidents',
      '95% user satisfaction rate'
    ],
    technologies: ['Solidity', 'React', 'Web3.js', 'IPFS', 'Node.js', 'PostgreSQL'],
    duration: '5 months',
    teamSize: '6 developers',
    budget: '$250K+',
    category: 'NFT & Marketplace',
    image: 'https://blockchaintechs.io/wp-content/uploads/2022/08/ICO-Vesting-and-Swapping-768x521.webp',
    liveUrl: 'https://nftauction.com'
  },
  'bt-swap': {
    title: 'BT Swap - Multichain DEX',
    description: 'Hybrid multichain decentralized exchange with AMM, yield farming, and cross-chain interoperability.',
    challenge: 'Building a secure DEX that could operate across multiple blockchains while maintaining liquidity and preventing MEV attacks.',
    solution: 'Implemented Uniswap-based AMM with custom cross-chain bridges and advanced anti-MEV protection mechanisms.',
    results: [
      '$50M+ total value locked (TVL)',
      '100,000+ active traders',
      '500+ trading pairs supported',
      '99.99% uptime achieved',
      'Zero major security incidents'
    ],
    technologies: ['Solidity', 'React', 'Web3.js', 'Node.js', 'AWS', 'Chainlink'],
    duration: '10 months',
    teamSize: '18 developers',
    budget: '$800K+',
    category: 'DEX & Trading',
    image: 'https://blockchaintechs.io/wp-content/uploads/2022/08/BT-Swap-Yield-Farming.webp',
    liveUrl: 'https://btswap.finance'
  },
  'fantompad': {
    title: 'FantomPAD - ICO Launchpad',
    description: 'Primary launch platform for Fantom network with anti-snipe protection and multisig security.',
    challenge: 'Creating a secure launchpad that prevents bot manipulation while ensuring fair token distribution.',
    solution: 'Developed advanced anti-snipe algorithms with tiered access system and multisig wallet integration.',
    results: [
      '$25M+ raised for projects',
      '50+ successful launches',
      '30,000+ investors onboarded',
      'Zero bot manipulation incidents',
      '98% project success rate'
    ],
    technologies: ['Solidity', 'React', 'Node.js', 'MongoDB', 'AWS', 'Fantom'],
    duration: '7 months',
    teamSize: '14 developers',
    budget: '$600K+',
    category: 'ICO & Launchpad',
    image: 'https://blockchaintechs.io/wp-content/uploads/2022/08/Fantompad-Projects-Listing.webp',
    liveUrl: 'https://fantompad.com'
  },
  'doxazo': {
    title: 'DOXAZO - Staking & Swapping',
    description: 'Crypto staking platform with flexible APY and integrated token swapping using UniSwap API.',
    challenge: 'Building a user-friendly staking platform that offers competitive yields while maintaining security.',
    solution: 'Created smart contracts with dynamic APY calculation and integrated UniSwap for seamless token swapping.',
    results: [
      '$10M+ total staked value',
      '15,000+ active stakers',
      '25% average APY delivered',
      '1M+ swap transactions',
      '4.9/5 user satisfaction'
    ],
    technologies: ['Solidity', 'React', 'Web3.js', 'UniSwap API', 'Node.js', 'AWS'],
    duration: '5 months',
    teamSize: '8 developers',
    budget: '$350K+',
    category: 'DeFi & Staking',
    image: 'https://blockchaintechs.io/wp-content/uploads/2022/08/Doxaza-Dashboard-1024x681.webp',
    liveUrl: 'https://doxazo.com'
  },
  'lendspot': {
    title: 'Lendspot - NFT Lending',
    description: 'DeFi platform for lending crypto against NFT collateral with rarity-based pricing.',
    challenge: 'Creating a fair valuation system for NFTs while protecting lenders from market volatility.',
    solution: 'Implemented AI-powered rarity analysis with OpenSea API integration and dynamic interest rates.',
    results: [
      '$5M+ in loans facilitated',
      '2,000+ NFTs used as collateral',
      '95% loan repayment rate',
      '500+ active lenders',
      'Support for 10+ NFT collections'
    ],
    technologies: ['Solidity', 'React', 'OpenSea API', 'Python', 'AWS', 'IPFS'],
    duration: '8 months',
    teamSize: '12 developers',
    budget: '$500K+',
    category: 'DeFi & NFT',
    image: 'https://blockchaintechs.io/wp-content/uploads/2022/08/Lendspot-NFT-Lending-1.webp',
    liveUrl: 'https://lendspot.io'
  },
  'metabloqs-ecosystem': {
    title: 'MetaBloqs - Metaverse Platform',
    description: 'Web 3.0 Metaverse with real-world locations, 3D NFT marketplace, and ReadyPlayerMe avatars.',
    challenge: 'Building a scalable metaverse that combines real-world accuracy with blockchain functionality.',
    solution: 'Developed Unity-based 3D world with integrated NFT marketplace and cross-platform compatibility.',
    results: [
      '25,000+ registered users',
      '1,000+ virtual events hosted',
      '$3M+ in virtual real estate sales',
      '500+ businesses onboarded',
      '99.5% platform uptime'
    ],
    technologies: ['Unity 3D', 'Solidity', 'ReadyPlayerMe', 'AWS', 'IPFS', 'WebRTC'],
    duration: '12 months',
    teamSize: '20 developers',
    budget: '$1M+',
    category: 'Metaverse & Web3',
    image: 'https://blockchaintechs.io/wp-content/uploads/2022/12/Metaverse-Ecosystem-1-1.webp',
    liveUrl: 'https://metabloqs.com'
  },
  'cryptomax': {
    title: 'CryptoMax - CEX Platform',
    description: 'Enterprise-grade centralized exchange with spot, futures, options, and margin trading capabilities.',
    challenge: 'Building a high-frequency trading platform that could handle institutional volumes while maintaining security.',
    solution: 'Implemented microservices architecture with advanced matching engine and multi-signature cold storage.',
    results: [
      '$100M+ daily trading volume',
      '500,000+ registered users',
      '1000+ trading pairs',
      '99.99% uptime achieved',
      'SOC 2 Type II compliance'
    ],
    technologies: ['Java', 'React', 'PostgreSQL', 'Redis', 'AWS', 'Kubernetes'],
    duration: '14 months',
    teamSize: '22 developers',
    budget: '$1.5M+',
    category: 'CEX & Trading',
    image: 'https://blockchaintechs.io/wp-content/uploads/2022/08/Metaplexar-Investment-Dashboard-Portfolio-Performance-300x237.webp',
    liveUrl: 'https://cryptomax.exchange'
  },
  'yield-aggregator': {
    title: 'DeFi Yield Aggregator',
    description: 'Advanced yield farming platform that automatically finds and compounds the best opportunities across protocols.',
    challenge: 'Creating an automated system that could optimize yields while managing smart contract risks.',
    solution: 'Built AI-powered yield optimization engine with automated rebalancing and risk assessment algorithms.',
    results: [
      '$20M+ total value managed',
      '35% average APY achieved',
      '5,000+ active farmers',
      '50+ protocols integrated',
      'Zero fund losses to date'
    ],
    technologies: ['Solidity', 'Python', 'React', 'Web3.py', 'AWS', 'Chainlink'],
    duration: '9 months',
    teamSize: '12 developers',
    budget: '$650K+',
    category: 'DeFi & Trading',
    image: 'https://blockchaintechs.io/wp-content/uploads/2022/08/Metaplexar-DeFi-Dashboard.webp',
    liveUrl: 'https://yieldmax.finance'
  },
  'supply-chain': {
    title: 'BlockTrace Supply Chain',
    description: 'Blockchain-based supply chain management with IoT integration and real-time tracking.',
    challenge: 'Creating an immutable tracking system that could integrate with existing enterprise systems.',
    solution: 'Developed hybrid blockchain solution with IoT sensors and enterprise API integrations.',
    results: [
      '500+ companies onboarded',
      '1M+ products tracked',
      '99.9% traceability accuracy',
      '50% reduction in fraud',
      'ISO 27001 certification'
    ],
    technologies: ['Hyperledger', 'React', 'Node.js', 'IoT Sensors', 'AWS', 'MongoDB'],
    duration: '11 months',
    teamSize: '15 developers',
    budget: '$750K+',
    category: 'Supply Chain & IoT',
    image: 'https://blockchaintechs.io/wp-content/uploads/2022/08/Cobe-User-Dashboard-1024x566.webp',
    liveUrl: 'https://blocktrace.supply'
  }
};

export default function CaseStudyPage() {
  const router = useRouter();
  const { slug } = router.query;
  const [caseStudy, setCaseStudy] = useState<CaseStudy | null>(null);

  useEffect(() => {
    if (slug && typeof slug === 'string') {
      setCaseStudy(caseStudies[slug] || null);
    }
  }, [slug]);

  if (!caseStudy) {
    return (
      <div style={{ 
        minHeight: '100vh', 
        display: 'flex', 
        alignItems: 'center', 
        justifyContent: 'center',
        background: 'linear-gradient(135deg, #1a1a2e 0%, #16213e 100%)'
      }}>
        <div style={{ textAlign: 'center', color: '#ffffff' }}>
          <h1>Case Study Not Found</h1>
          <Link href="/Portfolio" style={{ color: '#00d46c', textDecoration: 'none' }}>
            ← Back to Portfolio
          </Link>
        </div>
      </div>
    );
  }

  return (
    <>
      <Head>
        <title>{caseStudy.title} - Case Study | AltAppLabs</title>
        <meta name="description" content={caseStudy.description} />
      </Head>
      
      <div style={{
        minHeight: '100vh',
        background: 'linear-gradient(135deg, #1a1a2e 0%, #16213e 100%)',
        padding: '40px 20px'
      }}>
        <div style={{ maxWidth: '1200px', margin: '0 auto' }}>
          {/* Back Button */}
          <Link href="/portfolio" style={{
            display: 'inline-flex',
            alignItems: 'center',
            gap: '8px',
            color: '#00d46c',
            textDecoration: 'none',
            marginBottom: '30px',
            fontSize: '16px',
            fontWeight: '600'
          }}>
            <ArrowLeft size={20} />
            Back to Portfolio
          </Link>

          {/* Header */}
          <div style={{
            background: 'rgba(255,255,255,0.05)',
            borderRadius: '20px',
            padding: '40px',
            marginBottom: '40px',
            backdropFilter: 'blur(10px)',
            border: '1px solid rgba(255,255,255,0.1)'
          }}>
            <div style={{ display: 'flex', gap: '20px', alignItems: 'center', marginBottom: '20px' }}>
              <span style={{
                background: '#00d46c',
                color: '#000',
                padding: '6px 16px',
                borderRadius: '20px',
                fontSize: '14px',
                fontWeight: 'bold'
              }}>
                {caseStudy.category}
              </span>
            </div>
            
            <WhiteBold style={{ fontSize: '48px', marginBottom: '20px', color: '#ffffff' }}>
              {caseStudy.title}
            </WhiteBold>
            
            <PText style={{ fontSize: '20px', lineHeight: '1.6', color: '#ffffff', marginBottom: '30px' }}>
              {caseStudy.description}
            </PText>

            {/* Project Stats */}
            <div style={{ display: 'flex', gap: '30px', flexWrap: 'wrap' }}>
              <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                <Calendar color='#00d46c' size={20} />
                <span style={{ color: '#ffffff', fontWeight: '600' }}>{caseStudy.duration}</span>
              </div>
              <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                <Users color='#00d46c' size={20} />
                <span style={{ color: '#ffffff', fontWeight: '600' }}>{caseStudy.teamSize}</span>
              </div>
              <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                <DollarSign color='#00d46c' size={20} />
                <span style={{ color: '#ffffff', fontWeight: '600' }}>{caseStudy.budget}</span>
              </div>
              {caseStudy.liveUrl && (
                <a
                  href={caseStudy.liveUrl}
                  target="_blank"
                  rel="noopener noreferrer"
                  style={{
                    display: 'flex',
                    alignItems: 'center',
                    gap: '8px',
                    background: 'linear-gradient(135deg, #00d46c, #00a855)',
                    color: '#000',
                    padding: '8px 16px',
                    borderRadius: '20px',
                    textDecoration: 'none',
                    fontSize: '14px',
                    fontWeight: '600'
                  }}
                >
                  <ExternalLink size={16} />
                  View Live
                </a>
              )}
            </div>
          </div>

          {/* Content Sections */}
          <div style={{ display: 'grid', gap: '30px' }}>
            {/* Challenge */}
            <section style={{
              background: 'rgba(255,255,255,0.05)',
              borderRadius: '20px',
              padding: '30px',
              backdropFilter: 'blur(10px)',
              border: '1px solid rgba(255,255,255,0.1)'
            }}>
              <WhiteBold style={{ fontSize: '24px', marginBottom: '15px', color: '#ffffff' }}>
                🎯 The Challenge
              </WhiteBold>
              <PText style={{ fontSize: '16px', lineHeight: '1.6', color: '#ffffff' }}>
                {caseStudy.challenge}
              </PText>
            </section>

            {/* Solution */}
            <section style={{
              background: 'rgba(255,255,255,0.05)',
              borderRadius: '20px',
              padding: '30px',
              backdropFilter: 'blur(10px)',
              border: '1px solid rgba(255,255,255,0.1)'
            }}>
              <WhiteBold style={{ fontSize: '24px', marginBottom: '15px', color: '#ffffff' }}>
                💡 Our Solution
              </WhiteBold>
              <PText style={{ fontSize: '16px', lineHeight: '1.6', color: '#ffffff' }}>
                {caseStudy.solution}
              </PText>
            </section>

            {/* Results */}
            <section style={{
              background: 'rgba(255,255,255,0.05)',
              borderRadius: '20px',
              padding: '30px',
              backdropFilter: 'blur(10px)',
              border: '1px solid rgba(255,255,255,0.1)'
            }}>
              <WhiteBold style={{ fontSize: '24px', marginBottom: '15px', color: '#ffffff' }}>
                🚀 Results Achieved
              </WhiteBold>
              <div style={{ display: 'grid', gap: '10px' }}>
                {caseStudy.results.map((result, index) => (
                  <div key={index} style={{
                    display: 'flex',
                    alignItems: 'center',
                    gap: '10px',
                    padding: '10px 0'
                  }}>
                    <div style={{
                      width: '8px',
                      height: '8px',
                      background: '#00d46c',
                      borderRadius: '50%'
                    }}></div>
                    <span style={{ color: '#ffffff', fontSize: '16px' }}>{result}</span>
                  </div>
                ))}
              </div>
            </section>

            {/* Technologies */}
            <section style={{
              background: 'rgba(255,255,255,0.05)',
              borderRadius: '20px',
              padding: '30px',
              backdropFilter: 'blur(10px)',
              border: '1px solid rgba(255,255,255,0.1)'
            }}>
              <WhiteBold style={{ fontSize: '24px', marginBottom: '15px', color: '#ffffff' }}>
                🛠️ Technologies Used
              </WhiteBold>
              <div style={{ display: 'flex', gap: '10px', flexWrap: 'wrap' }}>
                {caseStudy.technologies.map((tech, index) => (
                  <span key={index} style={{
                    background: 'rgba(0,212,108,0.2)',
                    color: '#00d46c',
                    padding: '6px 12px',
                    borderRadius: '15px',
                    fontSize: '14px',
                    fontWeight: '600'
                  }}>
                    {tech}
                  </span>
                ))}
              </div>
            </section>
          </div>
        </div>
      </div>
    </>
  );
}
