import { MailerS<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, Sender, Recipient } from "mailersend";


export default async function handler(req,res){
const {email,name,phoneNumber,budget,message}=req.query;
const mailerSend = new MailerSend({
  apiKey: process.env.MAIL_API_KEY,
});

const sentFrom = new Sender('<EMAIL>', name);
const html=`<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Email Content</title>
</head>
<body style="font-family: Arial, sans-serif; background-color: #f4f4f4; padding: 20px;">

    <div style="background-color: #fff; border-radius: 5px; box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1); padding: 20px; margin-bottom: 20px;">
        <h2 style="margin-top: 0;">Contact Information</h2>
        <p><strong>Name:</strong> ${name}</p>
        <p><strong>Email:</strong> ${email}</p>
        <p><strong>Phone Number:</strong> ${phoneNumber}</p>
        <p><strong>Budget:</strong> $${budget}</p>
        <p><strong>Message:</strong> ${message}</p>
    </div>

</body>
</html>
`
const recipients = [
  new Recipient('<EMAIL>', 'AltAPPLAP')
];

const emailParams = new EmailParams()
  .setFrom(sentFrom)
  .setTo(recipients)
  .setReplyTo(sentFrom)
  .setSubject("Email from ALT-APP-LAP website")
  .setHtml("<strong>This is the HTML content</strong>")
 // .setText("This is the text content");

const data=await mailerSend.email.send(emailParams);
res.status(200).json({data})

}