import React, { useEffect, useState, Suspense } from 'react'
import '@/app/globals.css'; // Import your global CSS styles here
import 'mdb-react-ui-kit/dist/css/mdb.min.css';
import "@fortawesome/fontawesome-free/css/all.min.css";
import '@/Components/index.css';
import Head from 'next/head';

import { AppProps } from 'next/app';
import { useRouter } from 'next/router';
import Loading from '@/Components/Loading';
import PerformanceOptimizer from '@/Components/PerformanceOptimizer';
import PerformanceSummary from '@/Components/PerformanceSummary';

// This default export is required for Next.js to recognize this file as the custom App component.
export default function MyApp({ Component, pageProps }: AppProps) {
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(false);
  const [initialLoad, setInitialLoad] = useState(true);

  useEffect(() => {
    const handleRouteChange = (url: string) => {
      setIsLoading(true);
    };

    const handleRouteComplete = (url: string) => {
      setIsLoading(false);
    };

    const handleRouteError = (err: any, url: string) => {
      setIsLoading(false);
    };

    // Hide initial loader after component mounts
    const timer = setTimeout(() => {
      setInitialLoad(false);
    }, 2000);

    // Listen for route changes
    router.events.on('routeChangeStart', handleRouteChange);
    router.events.on('routeChangeComplete', handleRouteComplete);
    router.events.on('routeChangeError', handleRouteError);

    return () => {
      clearTimeout(timer);
      router.events.off('routeChangeStart', handleRouteChange);
      router.events.off('routeChangeComplete', handleRouteComplete);
      router.events.off('routeChangeError', handleRouteError);
    };
  }, [router]);

  return (
    <>
      <Head>
        <title>AltAppLabs - Leading Blockchain Development Company</title>
        <meta name="description" content="AltAppLabs is a premier blockchain development company specializing in cryptocurrency exchanges, NFT platforms, DeFi solutions, and smart contract development." />
        <meta name="viewport" content="width=device-width, initial-scale=1, viewport-fit=cover" />
        <link rel="icon" href="/favicon.ico" />

        {/* PWA Manifest */}
        <link rel="manifest" href="/manifest.json" />
        <meta name="theme-color" content="#00d46c" />
        <meta name="apple-mobile-web-app-capable" content="yes" />
        <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent" />

        {/* Critical CSS inline for fastest loading */}
        <style dangerouslySetInnerHTML={{
          __html: `
            @keyframes spin { 0% { transform: rotate(0deg); } 100% { transform: rotate(360deg); } }
            body { margin: 0; background: #020b12; color: #fff; font-family: -apple-system, BlinkMacSystemFont, sans-serif; }
            .loading-overlay { position: fixed; top: 0; left: 0; right: 0; bottom: 0; background: #020b12; z-index: 9999; }
            img { max-width: 100%; height: auto; }
          `
        }} />

        {/* Preload critical resources */}
        <link rel="preload" href="/images/logo.png" as="image" />
        <link rel="dns-prefetch" href="//blockchaintechs.io" />
        <link rel="dns-prefetch" href="//fonts.googleapis.com" />

        {/* Open Graph / Facebook */}
        <meta property="og:type" content="website" />
        <meta property="og:url" content="https://altapplabs.com/" />
        <meta property="og:title" content="AltAppLabs - Leading Blockchain Development Company" />
        <meta property="og:description" content="Premier blockchain development services including crypto exchanges, NFT platforms, and DeFi solutions." />
        <meta property="og:image" content="/images/og-image.jpg" />

        {/* Twitter */}
        <meta property="twitter:card" content="summary_large_image" />
        <meta property="twitter:url" content="https://altapplabs.com/" />
        <meta property="twitter:title" content="AltAppLabs - Leading Blockchain Development Company" />
        <meta property="twitter:description" content="Premier blockchain development services including crypto exchanges, NFT platforms, and DeFi solutions." />
        <meta property="twitter:image" content="/images/og-image.jpg" />
      </Head>

      <PerformanceOptimizer />

      <div style={{ maxWidth: "100vw", width: "100vw" }}>
        {/* Initial page load loader */}
        {initialLoad && (
          <Loading
            fullScreen={true}
            text="AltAppLabs"
            variant="blockchain"
          />
        )}

        {/* Route change loader */}
        {isLoading && !initialLoad && (
          <Loading
            fullScreen={true}
            text="Loading Page..."
            variant="blockchain"
          />
        )}

        <Suspense fallback={<Loading fullScreen={true} text="Loading..." variant="minimal" />}>
          <Component {...pageProps} />
        </Suspense>

        {/* Performance monitoring in development */}
        <PerformanceSummary />
      </div>
    </>
  );
}
