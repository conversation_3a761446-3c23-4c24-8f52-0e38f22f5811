import { useState, useEffect } from 'react';

/**
 * Custom hook to handle window width in a way that prevents hydration mismatches
 * Returns both the width and a boolean indicating if we're on the client side
 */
export const useClientWidth = () => {
  const [width, setWidth] = useState(0);
  const [isClient, setIsClient] = useState(false);

  useEffect(() => {
    setIsClient(true);
    setWidth(window.innerWidth);

    const handleResize = () => {
      setWidth(window.innerWidth);
    };

    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  return { width, isClient };
};

export default useClientWidth;
