'use client'

import { useEffect } from 'react'

export default function GlobalError({
  error,
  reset,
}: {
  error: Error & { digest?: string }
  reset: () => void
}) {
  useEffect(() => {
    // Log the error to an error reporting service
    console.error('Global error:', error)
  }, [error])

  return (
    <html>
      <body style={{
        margin: 0,
        padding: 0,
        fontFamily: 'Roboto, sans-serif',
        backgroundColor: 'var(--bgColor, #020b12)',
        color: 'var(--textColor, #d7dde2)',
        minHeight: '100vh',
        display: 'flex',
        flexDirection: 'column',
        justifyContent: 'center',
        alignItems: 'center',
        textAlign: 'center'
      }}>
        <div style={{ maxWidth: '600px', padding: '40px 20px' }}>
          <h1 style={{
            fontSize: '3rem',
            fontWeight: 'bold',
            color: 'var(--green, #00d46c)',
            margin: '0 0 20px 0'
          }}>
            Something went wrong!
          </h1>
          
          <p style={{
            fontSize: '1.1rem',
            lineHeight: '1.6',
            margin: '0 0 40px 0',
            opacity: 0.8
          }}>
            We apologize for the inconvenience. An unexpected error has occurred. 
            Our team has been notified and is working to resolve this issue.
          </p>
          
          <div style={{ display: 'flex', gap: '20px', justifyContent: 'center', flexWrap: 'wrap' }}>
            <button
              onClick={reset}
              style={{
                backgroundColor: 'var(--green, #00d46c)',
                color: 'white',
                border: 'none',
                padding: '12px 24px',
                borderRadius: '6px',
                fontSize: '16px',
                fontWeight: '600',
                cursor: 'pointer',
                transition: 'background-color 0.3s ease'
              }}
              onMouseOver={(e) => {
                e.currentTarget.style.backgroundColor = 'var(--blue, #2096d4)';
              }}
              onMouseOut={(e) => {
                e.currentTarget.style.backgroundColor = 'var(--green, #00d46c)';
              }}
            >
              Try again
            </button>
            
            <button
              onClick={() => window.location.href = '/'}
              style={{
                border: '2px solid var(--green, #00d46c)',
                backgroundColor: 'transparent',
                color: 'var(--green, #00d46c)',
                padding: '10px 22px',
                borderRadius: '6px',
                fontSize: '16px',
                fontWeight: '600',
                cursor: 'pointer',
                transition: 'all 0.3s ease'
              }}
              onMouseOver={(e) => {
                e.currentTarget.style.backgroundColor = 'var(--green, #00d46c)';
                e.currentTarget.style.color = 'white';
              }}
              onMouseOut={(e) => {
                e.currentTarget.style.backgroundColor = 'transparent';
                e.currentTarget.style.color = 'var(--green, #00d46c)';
              }}
            >
              Go Home
            </button>
          </div>
          
          {process.env.NODE_ENV === 'development' && (
            <details style={{ marginTop: '40px', textAlign: 'left' }}>
              <summary style={{ cursor: 'pointer', marginBottom: '10px', textAlign: 'center' }}>
                Error Details (Development Only)
              </summary>
              <pre style={{
                backgroundColor: '#1a1a1a',
                padding: '15px',
                borderRadius: '4px',
                overflow: 'auto',
                fontSize: '12px',
                color: '#ff6b6b',
                whiteSpace: 'pre-wrap'
              }}>
                {error.message}
                {error.stack && '\n\nStack trace:\n' + error.stack}
              </pre>
            </details>
          )}
        </div>
      </body>
    </html>
  )
}
