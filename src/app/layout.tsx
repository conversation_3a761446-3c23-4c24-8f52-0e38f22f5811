import type { Metadata } from "next";
import 'mdb-react-ui-kit/dist/css/mdb.min.css'
import "@fortawesome/fontawesome-free/css/all.min.css"
import { Roboto } from "next/font/google";
import '@/app/globals.css';
import StructuredData from '@/Components/StructuredData';
import PerformanceMonitor from '@/Components/PerformanceMonitor';

const roboto = Roboto({
  weight: ["300", "400", "500", "700"],
  subsets: ["latin"],
  display: 'swap',
});

export const metadata: Metadata = {
  title: {
    default: "AltAppLabs - Leading Blockchain & Crypto Development Company",
    template: "%s | AltAppLabs"
  },
  description: "Transform your crypto markets with innovative blockchain solutions. Expert development in DeFi, NFTs, smart contracts, and automated trading systems.",
  keywords: ["blockchain development", "crypto trading bots", "DeFi development", "NFT marketplace", "smart contracts", "cryptocurrency exchange", "metaverse development"],
  authors: [{ name: "AltAppLabs" }],
  creator: "AltAppLabs",
  publisher: "AltAppLabs",
  formatDetection: {
    email: false,
    address: false,
    telephone: false,
  },
  metadataBase: new URL('https://altapplabs.com'),
  alternates: {
    canonical: '/',
  },
  openGraph: {
    title: "AltAppLabs - Leading Blockchain & Crypto Development Company",
    description: "Transform your crypto markets with innovative blockchain solutions. Expert development in DeFi, NFTs, smart contracts, and automated trading systems.",
    url: 'https://altapplabs.com',
    siteName: 'AltAppLabs',
    images: [
      {
        url: '/images/og-image.jpg',
        width: 1200,
        height: 630,
        alt: 'AltAppLabs - Blockchain Development Company',
      },
    ],
    locale: 'en_US',
    type: 'website',
  },
  twitter: {
    card: 'summary_large_image',
    title: "AltAppLabs - Leading Blockchain & Crypto Development Company",
    description: "Transform your crypto markets with innovative blockchain solutions. Expert development in DeFi, NFTs, smart contracts, and automated trading systems.",
    images: ['/images/og-image.jpg'],
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      'max-video-preview': -1,
      'max-image-preview': 'large',
      'max-snippet': -1,
    },
  },
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <head>
        <link href="/images/icon.jpg" rel="icon" type="image/jpeg" sizes="32x32" />
        <link href="/images/icon.jpg" rel="apple-touch-icon" sizes="180x180" />
        <meta name="theme-color" content="#00d46c" />
        <meta name="msapplication-TileColor" content="#00d46c" />
        <link rel="preconnect" href="https://fonts.googleapis.com" />
        <link rel="preconnect" href="https://fonts.gstatic.com" crossOrigin="anonymous" />
      </head>
      <body className={roboto.className}>
        <StructuredData />
        <PerformanceMonitor />
        <noscript>
          <div style={{
            position: 'fixed',
            top: 0,
            left: 0,
            width: '100%',
            height: '100%',
            backgroundColor: 'rgba(0,0,0,0.8)',
            color: 'white',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            zIndex: 9999,
            textAlign: 'center',
            padding: '20px'
          }}>
            <div>
              <h1>JavaScript Required</h1>
              <p>This website requires JavaScript to function properly. Please enable JavaScript in your browser settings.</p>
            </div>
          </div>
        </noscript>
        {children}
      </body>
    </html>
  );
}
