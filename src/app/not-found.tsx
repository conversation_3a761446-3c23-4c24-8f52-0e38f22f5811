'use client'

import Link from 'next/link';

export default function NotFound() {
  return (
    <div style={{
      minHeight: '100vh',
      backgroundColor: 'var(--bgColor)',
      color: 'var(--textColor)',
      display: 'flex',
      flexDirection: 'column',
      justifyContent: 'center',
      alignItems: 'center',
      textAlign: 'center',
      padding: '20px'
    }}>
      <div style={{ maxWidth: '600px' }}>
        <h1 style={{
          fontSize: '6rem',
          fontWeight: 'bold',
          color: 'var(--green)',
          margin: '0 0 20px 0',
          lineHeight: '1'
        }}>
          404
        </h1>
        
        <h2 style={{
          fontSize: '2rem',
          fontWeight: '600',
          margin: '0 0 20px 0',
          color: 'var(--textColor)'
        }}>
          Page Not Found
        </h2>
        
        <p style={{
          fontSize: '1.1rem',
          lineHeight: '1.6',
          margin: '0 0 40px 0',
          color: 'var(--textColor)',
          opacity: 0.8
        }}>
          The page you are looking for might have been removed, had its name changed, 
          or is temporarily unavailable.
        </p>
        
        <div style={{ display: 'flex', gap: '20px', justifyContent: 'center', flexWrap: 'wrap' }}>
          <Link
            href="/"
            className="btn-primary"
          >
            Go Home
          </Link>

          <Link
            href="/Portfolio"
            className="btn-secondary"
          >
            View Portfolio
          </Link>
        </div>
        
        <div style={{ marginTop: '60px' }}>
          <h3 style={{
            fontSize: '1.2rem',
            fontWeight: '600',
            margin: '0 0 20px 0',
            color: 'var(--textColor)'
          }}>
            Popular Pages
          </h3>
          
          <div style={{ display: 'flex', flexDirection: 'column', gap: '10px', alignItems: 'center' }}>
            <Link href="/CryptoDevelopment" style={{ color: 'var(--blue)', textDecoration: 'none' }}>
              Crypto Development
            </Link>
            <Link href="/DAppsDevelopment" style={{ color: 'var(--blue)', textDecoration: 'none' }}>
              DApps Development
            </Link>
            <Link href="/NFTTokenDevelopment" style={{ color: 'var(--blue)', textDecoration: 'none' }}>
              NFT Development
            </Link>
            <Link href="/smartContract" style={{ color: 'var(--blue)', textDecoration: 'none' }}>
              Smart Contracts
            </Link>
            <Link href="/contactUs" style={{ color: 'var(--blue)', textDecoration: 'none' }}>
              Contact Us
            </Link>
          </div>
        </div>
      </div>

      <style jsx>{`
        .btn-primary {
          background-color: var(--green);
          color: white;
          padding: 12px 24px;
          border-radius: 6px;
          text-decoration: none;
          font-weight: 600;
          transition: background-color 0.3s ease;
          display: inline-block;
        }

        .btn-primary:hover {
          background-color: var(--blue);
        }

        .btn-secondary {
          border: 2px solid var(--green);
          color: var(--green);
          padding: 10px 22px;
          border-radius: 6px;
          text-decoration: none;
          font-weight: 600;
          transition: all 0.3s ease;
          display: inline-block;
          background-color: transparent;
        }

        .btn-secondary:hover {
          background-color: var(--green);
          color: white;
        }
      `}</style>
    </div>
  );
}
