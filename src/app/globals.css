.Indexbody{
  overflow-y: auto !important;
  overflow-x:hidden !important;
  max-width:100vw;

}
body{
  width:100vw;
max-width:100vw;
margin:0 auto !important;
display:block !important; 
}
@media (min-width:900px) {
  .Indexbody{
  padding:0px 50px;
  }
}
:root {
--dark:rgba(22,19,30);
--yellow:rgba(248,143,1);
--darkYellow:rgba(85,53,18);
--darkerYellow:rgba(23,21,22);
--green:rgba(0,212,108);
--blueText:rgb(12,54,98);
--blue:rgba(32,150,212);
--bgColor:rgba(2,11,18);
--white:rgba(255,255,255);
--textColor:rgb(215, 221, 226);
}
a{
  color:inherit !important;
}
/* Add more specific styles for components if needed */
.appIconText{
background: linear-gradient(90deg,white,var(--yellow));
background-clip: text;

-moz-background-clip: text;
color:transparent;
-webkit-background-clip: text;
}

*[class*=padding-16]{
  padding:16px !important;
}
@media (max-width:800px) {
    *[class*=m-block]{
      display: block !important;
      flex-flow: row wrap !important;
    }
    *[class*=m-block] > *{
      width:96% !important;
      margin-top:10px !important;
    }
    h1,h2,h3,h4{
      padding:16px !important;
    }
    *[class*=m-fullWidth]{
min-width: 100%;
max-width: 100%;
width:100%;
margin: 0 !important;
    }
    .weOffer .offerItem{
  margin-top:16px !important;
}
h3{
  font-size:25px !important;
}
h3 *{
  font-size:25px !important;
}
}

