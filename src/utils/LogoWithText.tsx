import Image from 'next/image'
import { useRouter } from 'next/router';
import React from 'react'

interface Props{
    text:string,
    size?:number,
    textStyle?:any,
    smallTextStyle?:any
}
const LogoWithText:React.FC<Props>=({text,size=50,textStyle={},smallTextStyle={}})=>{
    const route=useRouter();
    return (
        <div className='d-flex align-items-center' onClick={()=>{
            route.push("/")
        }}>
            <Image style={{borderRadius:10}} src='/images/icon.jpg' alt='' width={size} height={size} className='appIcon'/>
           
            {text && <b style={{padding:10,fontSize:'large',lineHeight:1}} className='appIconText'><span style={{...textStyle}}>
                {text}</span>
                
                {/* <br/> <i style={{fontSize:'smaller',...smallTextStyle}}>Tech</i> */}
                
                </b>
                
                
                }
        </div>
    )
}

export default LogoWithText