.coloredHeaderContainer{
margin:0 auto;
display:flex;
align-items: center;
justify-content: center;
}

.coloredHeader{
margin:0 auto;
max-width: 80%;
min-height:80px;
width:95%;

background: linear-gradient(90deg,var(--bgColor),rgba(3, 155, 3, 0.253),var(--bgColor));

border: 3px solid transparent;
    border-image: linear-gradient(90deg,var(--bgColor),var(--blue), var(--green),var(--blue), var(--bgColor));
    border-image-slice: 1;
    overflow: hidden;

}

.coloredHeader h2{
overflow-Wrap: break-word;
box-Sizing:border-box;
padding: 10px;
margin: 0px;
min-height: 70px;
text-align: center;

font-size: 50px!important;
color: rgb(0, 212, 108);
font-Weight: 700;
font: 18px / 38px Rubik;
text-decoration-skip: center;

font-weight: bolder;
font-family: system-ui, -apple-system, BlinkMacSystemFont, '<PERSON><PERSON><PERSON> UI', <PERSON><PERSON>, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;


}

@media screen and (max-width:750px) {
    .coloredHeader h2{
font-size: 30px!important;
min-height: 50px !important;

    }

    .coloredHeader{
min-height: 70px !important;
    }

}