import { emailLink } from '@/Components/fixed/sideContact';
import { WhatsApp } from '@mui/icons-material';
import { IconButton } from '@mui/material';
import Link from 'next/link';
import React from 'react'
import { Mail } from 'react-feather';


const MessageIcon:React.FC=()=>{
    return  (
        <div>
<Link href={emailLink}>
            <IconButton  style={{borderRadius:"50%",background:"var(--yellow)"}} >
            <Mail style={{color:'white'}}/>
            </IconButton>
            </Link>
        </div>
    )
}

export default MessageIcon;