import Image from "next/image";
import React from "react";
interface Props{
    img:string
}
const TechnologyItem:React.FC<Props>=({img})=>{
  return (
    <>
      <div
        className="swiper-slide swiper-slide-duplicate"
        aria-label="1 of 14"
        aria-roledescription="slide"
        role="group"
        style={{
          boxSizing: "border-box",
          flexShrink: 0,
          height: "100%",
          position: "relative",
          borderStyle: "solid",
          borderWidth: "0px",
          overflow: "hidden",
          willChange: "transform",
          transitionDuration: "0.5s",
          transitionProperty:
            "border, background, transform, -webkit-transform",
          textAlign: "center",
          width: "132.222px",
        }}
      >
        <figure
          className="swiper-slide-inner"
          style={{
            boxSizing: "border-box",
            lineHeight: "inherit",
            margin: "0px",
          }}
        >
          <Image
            className="swiper-slide-image"
            height={132}
            width={132}
            alt="Ethereum"
            src={img}
            style={{
              userSelect: "none",
              verticalAlign: "top",
              boxSizing: "border-box",
              border: "none",
              borderRadius: "0px",
              height: "auto",
              maxWidth: "100%",
              boxShadow: "none",
              outline: "none",
              pointerEvents: "none",
            }}
          />
        </figure>
      </div>
  
    </>
  );
}
export default TechnologyItem;