import React from "react";
interface Props{
    children:any
}
const Bold:React.FC<Props>=({children})=>{
  return (
    <>
      <h3
        className="elementor-heading-title elementor-size-default"
        style={{
          overflowWrap: "break-word",
          boxSizing: "border-box",
          padding: "0px",
          margin: "0px",
          lineHeight: "30px",
          color: "#333333",
          fontFamily: "Rubik, sans-serif",
          fontSize: "24px",
          fontWeight: 600,
          textAlign: "center",
        }}
      >
{children}        
      </h3>
      
    </>
  );
}
export default Bold;