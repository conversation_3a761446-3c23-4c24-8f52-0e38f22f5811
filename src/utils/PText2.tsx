import React from 'react'
interface Props{
children:any,
style?:any
}
const PText:React.FC<Props>=({children,style={}})=>{
    return (
<div style={{padding:10,
      margin:"0 auto",
      }}>
      <p
        style={{
          margin: "0px 0px 18px",
          boxSizing: "border-box",
          padding: "0px",

          marginBottom: "0px",
          color: style.color || "white", // White text for dark background
          fontFamily: "Roboto,Inter, sans-serif",
          fontWeight: 300,
          ...style
        }}
      >
        {children}
      </p>
    </div>
    )
}

export default PText;