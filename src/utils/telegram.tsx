import { telegramLink } from '@/Components/fixed/sideContact';
import { Telegram, WhatsApp } from '@mui/icons-material';
import { IconButton } from '@mui/material';
import Link from 'next/link';
import React from 'react'


const TelegramIcon:React.FC=()=>{
    return  (
        <div>
<Link href={telegramLink}>
            <IconButton  style={{borderRadius:"50%",background:"var(--blue)"}} >
            <Telegram style={{color:'white'}}/>
            </IconButton>
            </Link>
        </div>
    )
}

export default TelegramIcon;