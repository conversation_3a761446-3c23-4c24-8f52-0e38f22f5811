import Image from 'next/image';
import React from 'react';

interface ResponsiveImageProps {
  src: string;
  alt?: string;
  width?: number;
  height?: number;
  containerSize: number | string;
}

const ResponsiveImage: React.FC<ResponsiveImageProps> = ({ src, alt='', width = 500, height = 500, containerSize }) => {
  return (
    <div style={{ position: 'relative', width: containerSize, height: containerSize,maxWidth:'90vw',margin:"0 auto" }}>
      <div style={{ position: 'absolute', top: 0, left: 0, width: '100%', height: '100%' }}>
        <Image
          layout="fill"
          objectFit="contain"
          src={src}
          alt={alt}
          // width={width}
          // height={height}
        />
      </div>
    </div>
  );
};

export default ResponsiveImage;
