import React from 'react'
import "@/utils/styles.css";
interface Props{
    children:any,
    style?:any,
    id?:string
}
const ColoredHeader:React.FC<Props>=({children,style={},id})=>{
    return (
        <>
        <div className='coloredHeaderContainer d-flex justify-content-center'>
        <div className='coloredHeader'>

<h2 id={id} style={{fontWeight:"bolder",...style}}>{children}
</h2>
        </div>
</div>

        </>
    )
}

export default ColoredHeader;
