import React from "react";
interface Props{
  children:any
}
const PText:React.FC<Props>=({children})=>{
  return (
    <div style={{padding:10,
      margin:"0 auto",
      maxWidth:'90%'
      }}>
      <p
        style={{
          margin: "0px 0px 18px",
          lineHeight: 1.5,
          boxSizing: "border-box",
          padding: "0px",
          
          marginBottom: "0px",
          color: "#555555",
          fontFamily: "Inter, sans-serif",
          fontWeight: 300,
          font: "300 18px / 27px Inter, sans-serif",
          textAlign: "center",
        }}
      >
        {children}
      </p>
    </div>
  );
}

export default PText;