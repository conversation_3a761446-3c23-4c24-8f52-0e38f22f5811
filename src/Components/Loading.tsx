import React, { useEffect, useState } from 'react';

interface LoadingProps {
  text?: string;
  fullScreen?: boolean;
  variant?: 'blockchain' | 'crypto' | 'minimal';
}

const Loading: React.FC<LoadingProps> = ({
  text = 'AltAppLabs Loading...',
  fullScreen = false,
  variant = 'blockchain'
}) => {
  const [progress, setProgress] = useState(0);
  const [loadingText, setLoadingText] = useState('Initializing');

  useEffect(() => {
    const progressInterval = setInterval(() => {
      setProgress(prev => {
        if (prev >= 100) {
          clearInterval(progressInterval);
          return 100;
        }
        return prev + Math.random() * 15;
      });
    }, 200);

    const textStates = [
      'Initializing blockchain...',
      'Connecting to network...',
      'Loading smart contracts...',
      'Syncing data...',
      'Almost ready...'
    ];

    const textInterval = setInterval(() => {
      setLoadingText(textStates[Math.floor(Math.random() * textStates.length)]);
    }, 1500);

    return () => {
      clearInterval(progressInterval);
      clearInterval(textInterval);
    };
  }, []);

  const containerStyle: React.CSSProperties = fullScreen ? {
    position: 'fixed',
    top: 0,
    left: 0,
    width: '100%',
    height: '100%',
    background: 'linear-gradient(135deg, var(--bgColor) 0%, rgba(2, 11, 18, 0.95) 50%, var(--dark) 100%)',
    display: 'flex',
    flexDirection: 'column',
    justifyContent: 'center',
    alignItems: 'center',
    zIndex: 9999,
    overflow: 'hidden',
  } : {
    display: 'flex',
    flexDirection: 'column',
    justifyContent: 'center',
    alignItems: 'center',
    padding: '40px',
    minHeight: '200px',
    background: 'var(--bgColor)',
  };

  const MinimalLoader = () => (
    <div className="minimal-loader">
      <div className="minimal-spinner"></div>
      <style jsx>{`
        .minimal-loader {
          display: flex;
          align-items: center;
          justify-content: center;
          padding: 20px;
        }

        .minimal-spinner {
          width: 40px;
          height: 40px;
          border: 3px solid rgba(0, 212, 108, 0.2);
          border-top: 3px solid var(--green);
          border-radius: 50%;
          animation: spin 1s linear infinite;
        }

        @keyframes spin {
          0% { transform: rotate(0deg); }
          100% { transform: rotate(360deg); }
        }
      `}</style>
    </div>
  );

  const BlockchainLoader = () => (
    <div className="blockchain-loader">
      {/* Animated blockchain blocks */}
      <div className="blockchain-container">
        {[...Array(5)].map((_, i) => (
          <div key={i} className={`block block-${i + 1}`}>
            <div className="block-inner">
              <div className="block-hash"></div>
              <div className="block-data"></div>
            </div>
          </div>
        ))}
      </div>

      {/* Central logo animation */}
      <div className="logo-container">
        <div className="logo-ring ring-1"></div>
        <div className="logo-ring ring-2"></div>
        <div className="logo-ring ring-3"></div>
        <div className="logo-center">
          <span className="logo-text">A</span>
        </div>
      </div>

      {/* Progress bar */}
      <div className="progress-container">
        <div className="progress-bar">
          <div
            className="progress-fill"
            style={{ width: `${Math.min(progress, 100)}%` }}
          ></div>
        </div>
        <div className="progress-text">{Math.round(Math.min(progress, 100))}%</div>
      </div>

      <style jsx>{`
        .blockchain-loader {
          display: flex;
          flex-direction: column;
          align-items: center;
          gap: 40px;
        }

        .blockchain-container {
          display: flex;
          gap: 15px;
          align-items: center;
        }

        .block {
          width: 50px;
          height: 50px;
          background: linear-gradient(135deg, var(--green), var(--blue));
          border-radius: 8px;
          position: relative;
          animation: blockPulse 2s ease-in-out infinite;
          box-shadow: 0 0 20px rgba(0, 212, 108, 0.3);
        }

        .block-1 { animation-delay: 0s; }
        .block-2 { animation-delay: 0.2s; }
        .block-3 { animation-delay: 0.4s; }
        .block-4 { animation-delay: 0.6s; }
        .block-5 { animation-delay: 0.8s; }

        .block-inner {
          padding: 8px;
          height: 100%;
          display: flex;
          flex-direction: column;
          justify-content: space-between;
        }

        .block-hash {
          height: 3px;
          background: rgba(255, 255, 255, 0.8);
          border-radius: 2px;
          animation: dataFlow 1.5s ease-in-out infinite;
        }

        .block-data {
          height: 2px;
          background: rgba(255, 255, 255, 0.6);
          border-radius: 1px;
          animation: dataFlow 1.5s ease-in-out infinite 0.3s;
        }

        .logo-container {
          position: relative;
          width: 120px;
          height: 120px;
          display: flex;
          align-items: center;
          justify-content: center;
        }

        .logo-ring {
          position: absolute;
          border: 2px solid transparent;
          border-radius: 50%;
          animation: rotate 3s linear infinite;
        }

        .ring-1 {
          width: 120px;
          height: 120px;
          border-top-color: var(--green);
          border-right-color: var(--green);
          animation-duration: 2s;
        }

        .ring-2 {
          width: 90px;
          height: 90px;
          border-bottom-color: var(--blue);
          border-left-color: var(--blue);
          animation-duration: 1.5s;
          animation-direction: reverse;
        }

        .ring-3 {
          width: 60px;
          height: 60px;
          border-top-color: var(--yellow);
          border-bottom-color: var(--yellow);
          animation-duration: 1s;
        }

        .logo-center {
          width: 40px;
          height: 40px;
          background: linear-gradient(135deg, var(--green), var(--blue));
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          box-shadow: 0 0 30px rgba(0, 212, 108, 0.5);
          animation: pulse 2s ease-in-out infinite;
        }

        .logo-text {
          color: white;
          font-weight: bold;
          font-size: 20px;
          text-shadow: 0 0 10px rgba(255, 255, 255, 0.5);
        }

        .progress-container {
          width: 300px;
          text-align: center;
        }

        .progress-bar {
          width: 100%;
          height: 4px;
          background: rgba(255, 255, 255, 0.1);
          border-radius: 2px;
          overflow: hidden;
          margin-bottom: 10px;
        }

        .progress-fill {
          height: 100%;
          background: linear-gradient(90deg, var(--green), var(--blue), var(--green));
          background-size: 200% 100%;
          animation: progressShine 1.5s ease-in-out infinite;
          transition: width 0.3s ease;
        }

        .progress-text {
          color: var(--textColor);
          font-size: 14px;
          font-weight: 500;
        }

        @keyframes blockPulse {
          0%, 100% {
            transform: scale(1);
            box-shadow: 0 0 20px rgba(0, 212, 108, 0.3);
          }
          50% {
            transform: scale(1.1);
            box-shadow: 0 0 30px rgba(0, 212, 108, 0.6);
          }
        }

        @keyframes dataFlow {
          0%, 100% { opacity: 0.6; width: 70%; }
          50% { opacity: 1; width: 100%; }
        }

        @keyframes rotate {
          from { transform: rotate(0deg); }
          to { transform: rotate(360deg); }
        }

        @keyframes pulse {
          0%, 100% { transform: scale(1); }
          50% { transform: scale(1.1); }
        }

        @keyframes progressShine {
          0% { background-position: -200% 0; }
          100% { background-position: 200% 0; }
        }

        /* Floating particles background */
        .blockchain-loader::before {
          content: '';
          position: absolute;
          top: -50%;
          left: -50%;
          width: 200%;
          height: 200%;
          background-image:
            radial-gradient(2px 2px at 20px 30px, var(--green), transparent),
            radial-gradient(2px 2px at 40px 70px, var(--blue), transparent),
            radial-gradient(1px 1px at 90px 40px, var(--yellow), transparent),
            radial-gradient(1px 1px at 130px 80px, var(--green), transparent);
          background-repeat: repeat;
          background-size: 150px 150px;
          animation: float 20s linear infinite;
          opacity: 0.3;
          pointer-events: none;
        }

        @keyframes float {
          0% { transform: translate(0, 0) rotate(0deg); }
          33% { transform: translate(30px, -30px) rotate(120deg); }
          66% { transform: translate(-20px, 20px) rotate(240deg); }
          100% { transform: translate(0, 0) rotate(360deg); }
        }
      `}</style>
    </div>
  );

  return (
    <div style={containerStyle} role="status" aria-live="polite">
      {variant === 'blockchain' && <BlockchainLoader />}
      {variant === 'minimal' && <MinimalLoader />}

      <div style={{
        marginTop: '20px',
        textAlign: 'center',
        animation: 'fadeInOut 2s ease-in-out infinite'
      }}>
        <p style={{
          color: 'var(--textColor)',
          fontSize: '18px',
          fontWeight: '500',
          margin: '0 0 10px 0'
        }}>
          {text}
        </p>
        <p style={{
          color: 'var(--green)',
          fontSize: '14px',
          fontWeight: '400',
          margin: 0,
          opacity: 0.8
        }}>
          {loadingText}
        </p>
      </div>

      <style jsx>{`
        @keyframes fadeInOut {
          0%, 100% { opacity: 0.7; }
          50% { opacity: 1; }
        }
      `}</style>
    </div>
  );
};

export default Loading;
