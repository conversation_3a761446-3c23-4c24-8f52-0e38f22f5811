import ColoredHeader from '@/utils/coloredHeader';
import Bold from '@/utils/pBolder';
import React from 'react'
import Boxs from './discoverMeterverseBox';
import PText from '@/utils/pText';
import Image from 'next/image';

const DiscoverMeterverse:React.FC=()=>{
    return (
        <>
        <ColoredHeader>
        Discover the Metaverse Virtual World    
        </ColoredHeader>
        <br/><PText>Nowadays more people are turning to virtual environments and technology means of 
            communication like video and web conferencing & more.</PText><br/>
<div style={{maxWidth:'85%',margin:"0 auto"}} className='discoverMeterverse blockOnMobile d-flex align-items-center justify-content-center'>

<div>
    <Boxs title='MONETIZATION' text="It is a open source and users can build relevant metaverse project and generate passive income."
    src="/images/users.svg"/>
    <br/>
    
    <Boxs title='3D APPS' text="Metaverse offers a 3D immersive world to enhance their current digital experience."
    src="/images/apps.svg"/>

</div>

<div className='spaceManImage' style={{minHeight:"100%",position:"relative"}}>
    <Image objectFit='contain' 
    src='https://blockchaintechs.io/wp-content/uploads/2022/11/Discover-the-Metaverse-Virtual-World.webp' 
    fill alt=''/>
</div>

<div>
    <Boxs src='/images/ai.svg' title='ARTIFICIAL INTELLIGENCE' text='AI machines that have the ability characters, enhancing their interactions and actions.

'/><br/>
<Boxs src='/images/mobile.svg' text='Integrate the dApps into your Metaverse. It makes your business systems compatible' title='INTEGRATION'/>
</div>


</div>
        </>
    )
}
export default DiscoverMeterverse;