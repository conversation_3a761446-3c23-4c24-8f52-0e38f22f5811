import React from 'react';
import <PERSON><PERSON>arouse<PERSON> from 'react-alice-carousel';
import 'react-alice-carousel/lib/alice-carousel.css';
import BriefAppsItem from './briefAppItem';

interface TechCarouselProps {
}

const BriefAppsCarousel: React.FC<TechCarouselProps> = ({ }) => {
const AppsSumarry=[
    {
        image:"/images/theSealedNectar.webp",
        name:"The Sealed Nectar",
        subName:"Audio Book App"
    },
    {
        image:"/images/babymates.webp",
        name:"Babymates",
        subName:"Community App"
    },
    {
        image:"/images/weldpro.webp",
        name:"WeldPro",
        subName:"QR Scanning"
    },
    {
        image:"/images/forte.webp",
        name:"Forte",
        subName:"Healthcare"
    },
    {
        image:"/images/Next-Event-1.webp",
        name:"Next Event",
        subName:"Sports Event App"
    }
]


    return (
    <div style={{width:'90%',maxWidth:'100vw',overflow:'hidden',margin:'0 auto'}}><AliceCarousel 
autoPlay
autoPlayInterval={1500}
infinite
    responsive={{
        0: { items: 2 },
        600:{items:3},
        768: { items: 6 },
        1024: { items: 8 },
      }}
    mouseTracking 
    disableDotsControls
disableButtonsControls
    items={AppsSumarry.map((item:any,i:number) => <BriefAppsItem {...item} key={i}/>)} />
  </div>
  );
};

export default BriefAppsCarousel;
