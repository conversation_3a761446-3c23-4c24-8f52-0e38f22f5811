import React, { useState } from 'react'
import GradientBox from './gradientBox';
import GradientBoxUp from './gradientBoxUp';

const Boxs:React.FC=()=>{
const [width,setWidth]=useState(typeof window==='object'? window.innerWidth:0 );

    return (
        <div className='Boxs d-flex align-items-center justify-content-center' 
        style={{gap:20,margin:"0 auto"}}>
<GradientBox>
    <div className='text-center'>
<b>200+</b>
<p style={{fontWeight:700,textTransform:"uppercase"}}>Projects</p>
    </div>
</GradientBox>

{width > 700 ? 
<GradientBoxUp>
<div className='text-center'>
<b>50+</b>
<p style={{fontWeight:700,textTransform:"uppercase"}}>DEDICATED TEAM</p>
    </div>
</GradientBoxUp>:<GradientBox>
<div className='text-center'>
<b>50+</b>
<p style={{fontWeight:700,textTransform:"uppercase"}}>DEDICATED TEAM</p>
    </div>
    </GradientBox>
}

<GradientBox>
<div className='text-center'>
<b>100%</b>
<p style={{fontWeight:700,textTransform:"uppercase"}}>CLIENT SATISFACTION</p>
    </div>

    </GradientBox>
        </div>
    )
}

export default Boxs;