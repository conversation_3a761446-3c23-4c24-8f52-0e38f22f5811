import Bold from '@/utils/pBolder'
import PText from '@/utils/pText'
import { MDBBtn } from 'mdb-react-ui-kit'
import Image from 'next/image'
import React from 'react'
interface Props{
    title:string,
    isBig?:boolean,
    text:string,
    style:any,
    image:string,
    readMore:()=>void,
}
const BlogCard:React.FC<Props>=(prop)=>{
    const {title,text,image,readMore,style}=prop
    return (
        <>
        <div className={`blogCard ${prop.isBig ? 'large':''}`} style={{...style}}>
            <div className='img-container' style={{maxHeight:300}}>
<Image alt='' fill objectFit='cover' src={image}/>
</div>

<div className='blogCardTextContainer'>
<div style={{padding:23}}><Bold>
{title}
</Bold>
</div>
<br/>
<PText>
{text}
</PText>
<br/>
<div className='buttonContainer d-flex justify-content-end '>
<MDBBtn color='secondary' style={{background:"none",width:"100%",color:"var(--green)",border:"1px solid var(--green)",textAlign:"center",borderRadius:20}} onClick={()=>readMore()} size='lg'>
    Read more
</MDBBtn>
</div>


</div>

        </div>
        </>
    )
}


export default BlogCard;