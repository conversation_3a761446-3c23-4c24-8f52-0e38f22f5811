import ColoredHeader from '@/utils/coloredHeader';
import React, { useState } from 'react'
import TestimonialsCard from './testimonialsCard';

const Testimonials:React.FC=()=>{
const testimonialsList = [
  {
    image:"https://blockchaintechs.io/wp-content/uploads/2022/11/Hive.webp",
    title: "They quickly accommodated any changes and feedback",
    text:
      "So far, AltAppLabs has successfully delivered everything we asked for. They’ve been outstandingly efficient and swift, understanding the assignment well and being transparent about their capabilities.",
  },
  {image:"https://blockchaintechs.io/wp-content/uploads/2022/11/Cobe.webp",
    
    title: "They are professional and friendly at the same time.",
    text:
      "It is glad to work with AltAppLabs. They delivered the product exactly what we discussed; their timely response is appreciated. Good team to work with, so thankful in the ways they supported me in making my dream to reality. Good luck for your all upcoming works.",
  },
  {image:"https://blockchaintechs.io/wp-content/uploads/2022/11/Skippy.webp",
    
    title: "Their trustworthiness is also key to an ongoing seamless experience",
    text:
      "They lead a communicative process and meet all the deadlines to ensure top project management. Their trustworthiness is also key to an ongoing seamless experience.",
  },
  {image:"https://blockchaintechs.io/wp-content/uploads/2022/11/TheSealedNectarRound.webp",
    
    title: "They delivered the products with highest standard, on time.",
    text:
      "BT was engaged to develop some of the most products in crypto space such multi chain crypto exchange and payment gateway protocols. They delivered the products with highest standard, on time.",
  },
  {image:"https://blockchaintechs.io/wp-content/uploads/2022/11/CornercoRound.webp",
    
    title: "Genuine Developer, Excellent Communication has performed",
    text:
      "Genuine Developer, Excellent Communication has performed above and beyond expectations. I had limited budget and AltAppLabs was the only company  able to meet my budget yet. Thanks once Again.",
  },
];

const [currentIndex,setCurrentIndex]=useState(0);
const [length,setLength]=useState(testimonialsList.length);
const next=()=>{
if(currentIndex < length -1){
    setCurrentIndex(currentIndex+1)
}
else{
    setCurrentIndex(()=>0)
}
}
const prev=()=>{
if(currentIndex<1){
    setCurrentIndex(length-1);
}
else{
    setCurrentIndex(currentIndex-1);
}
}
    return (
        <>
        <div className='testimonials'>
<ColoredHeader>
{`Our Client's Testimonials`}
</ColoredHeader>
<br/><br/>
<TestimonialsCard length={length} currentIndex={currentIndex+1} nextClick={next} prevClick={prev} {...testimonialsList[currentIndex]}/>
        </div>
        </>
    )
}

export default Testimonials;