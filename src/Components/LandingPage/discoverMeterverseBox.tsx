import Bold from '@/utils/pBolder';
import PText from '@/utils/pText';
import Image from 'next/image';
import React from 'react'
interface Props{
src:string,
title:string,
text:string
}

const Boxs:React.FC<Props>=({src,title,text})=>{
    return (
        <div className='meterverseBox d-flex'>
            <Image src={src} width={50} height={50} alt=''/>
        <div>
            <p style={{paddingLeft:23}}><Bold>{title}</Bold></p>
            <PText>{text}</PText>
        </div>
        </div>
    )
}

export default Boxs;