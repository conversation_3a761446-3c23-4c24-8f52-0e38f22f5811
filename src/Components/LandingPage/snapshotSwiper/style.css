.slider2{
    max-width:400px;
    width:340px;
    min-height: 200px;
    border-radius:20px;
    box-shadow: 1px 1px 10px 0px rgb(12, 14, 22);
    padding:25px;
padding-top: 50px !important;
    background: #05141A;
    margin-left:10px;
    height: auto !important;
    position: relative;
}
@media (max-width:700px) {
    .slider2.right, .slider2.left{
        display: none !important;
    }
    .slider2.Active2{
        width:80% !important;
        max-width:80% !important;
        margin:0 auto !important;
    }
    
}
.slider2 h4{
    text-align: center;
    color:var(--green);
    font-weight: bolder;
}
.slider2 .text{
    text-align: center;
    color:rgb(243, 237, 237);
    font-weight: 460;
}
.slider2 img{
    background-color: #05141A;
width: 120px;
height:120px;
position: absolute;
border:5px solid var(--green);
border-radius:50%;
margin:0 auto;
display:block;
padding:20px;
transform:translate(80%,-90%);
}

@media (max-width:700px) {
    .slider2{
        margin: 0 !important;
    }
    /**remove left and right items**/
}
.slider2.Active2 img{
    width:180px;
    height: 180px;
}
.slider2.active{
    box-shadow: 1px 1px 30px 0px #155015a2 !important;
}
.slider2.left{
padding:10px;
    
}
.slider2.right{
padding:10px;

}
.slider2.left button, .slider2.right button{
    transform: translate(-0px,100px);
    animation-name: goDown;
}
.slider2.animate *{
    animation-duration:1s;
}
@keyframes goDown {
    0%{
    transform: translate(-0px,0px);
    }
100%{
    transform: translate(-0px,100px);
}
}


@keyframes flipRight2 {
    0%{
        transform: translate(100px,0px);
    }
    100%{
        transform:none
        }
}


@keyframes flipLeft2 {
    0%{
        transform: translate(50px,0px);
    }
    100%{
        transform:none;
    }
}


@keyframes Active2{
    0%{
        transform:translate(80px,0px);
    }
    100%{
        transform:translate(-0px,0px);
    }
}
/*go left animation*/

@keyframes flipRight2_ {
    0%{
        transform: translate(-50px,0px);
    }
    100%{
        transform:none;
    }
}


@keyframes flipLeft2_ {
    0%{
        transform: translate(-100px,0px);
    }
    100%{
        transform:none;
    }

}


@keyframes Active2_{
    0%{
        transform:translate(-80px,0px);
    }
    100%{
        transform:translate(-0px,0px);
    }
}