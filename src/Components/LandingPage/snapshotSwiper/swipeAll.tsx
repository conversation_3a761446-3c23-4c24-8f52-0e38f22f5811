import React, { useEffect, useMemo, useState } from 'react'
import Swipe<PERSON><PERSON><PERSON>eft from './swipeItemLeft';
import SwipeItem from './swipeItem';
import SwipeItemRight from './swipeItemRight';
import { IconButton } from '@mui/material';
import { FaAngleLeft, FaAngleRight } from 'react-icons/fa';

const SwipeAll: React.FC = () => {
    const [currentIndex, setCurrentIndex] = useState(0);



        const [servicesData,setServicesData] = useState([
            {
              title: "NFT STAKING PLATFORM",
              text: "Individuals can earn rewards based on the Annual Percentage Yield (APY)",
              image: "https://blockchaintechs.io/wp-content/uploads/2022/11/Indigena.webp" // Empty image property
            },
            {
              title: "NFT LENDING & BORROWING",
              text: "Get crypto loans against NFTs and lend crypto to generate additional income.",
              image: "https://blockchaintechs.io/wp-content/uploads/2022/11/Metaplexar.webp" // Empty image property
            },
            {
              title: "RECRUITMENT PLATFORM",
              text: "New Gen Recruitment Platform with Candidates and Recruiters",
              image: "https://blockchaintechs.io/wp-content/uploads/2022/11/PelXP.webp" // Empty image property
            },
            {
              title: "ESCROW PAYMENT PLATFORM",
              text: "The PayPal of crypto aims on providing a safe and secure P2P Payment",
              image: "https://blockchaintechs.io/wp-content/uploads/2022/11/BT-Swap.webp" // Empty image property
            },
            {
              title: "METAVERSE ECOSYSTEM",
              text: "Metabloqs will serve as the canvas for a metaverse that is grounded in reality",
              image: "https://blockchaintechs.io/wp-content/uploads/2022/11/Doxa-House.webp" // Empty image property
            },
            {
              title: "APPARELS MARKETPLACE",
              text: "Apparels marketplace with fiat and crypto payment gateway integrated.",
              image: "https://blockchaintechs.io/wp-content/uploads/2022/11/Uniplay.webp" // Empty image property
            },
            {
              title: "DECENTRALIZED CRYPTO EXCHANGE",
              text: "BT Swap is Hybrid multichain decentralized exchange platform with AMM",
              image: "https://blockchaintechs.io/wp-content/uploads/2022/11/MyTaxManRound.webp" // Empty image property
            },
            {
              title: "CRYPTO STAKING & SWAPPING",
              text: "A staking platform with your own token and earning profits by staking",
              image: "https://blockchaintechs.io/wp-content/uploads/2022/11/ExpeditedRound.webp" // Empty image property
            },
            {
              title: "SOCIAL NETWORKING APP",
              text: "Crypto powered real time Audio & Video social networking platform",
              image: "https://blockchaintechs.io/wp-content/uploads/2022/11/BT-Swap.webp" // Empty image property
            },
            {
              title: "TRADITIONAL ART NFT MARKETPLACE",
              text: "Indigena NFT Marketplace is connecting Indigenous people globally",
              image: "https://blockchaintechs.io/wp-content/uploads/2022/11/Indigena.webp" // Empty image property
            },
            {
              title: "NFT AUCTION PLATFORM",
              text: "NFT auction platform to raise capital and smart contracts with timed auctions",
              image: "https://blockchaintechs.io/wp-content/uploads/2022/11/Lockness.webp" // Empty image property
            },
            {
              title: "CRYPTO STARTUP LAUNCHPAD",
              text: "FantomPAD is the primary launch and listing platform for the Fantom network.",
              image: "https://blockchaintechs.io/wp-content/uploads/2022/11/Skippy.webp" // Empty image property
            },
            {
              title: "CARBON CREDIT TRADING DAPP",
              text: "Tokenize real world carbon credits in to blockchain based crypto tokens",
              image: "https://blockchaintechs.io/wp-content/uploads/2022/11/SkyClinicRound.webp" // Empty image property
            }
          ]);
    //add more 









    const [animatedId, setAnimateId] = useState("");
    const [item,setItem]=useState<any>(servicesData[1]);
    const [itemLeft,setItemLeft]=useState<any>(servicesData[0]);
    const [itemRight,setItemRight]=useState<any>(servicesData[3]);
    
    useEffect(() => {

    }, []);
    useEffect(() => {
        let leftIndex;
        let middleIndex = currentIndex;
        let rightIndex;
    
        // Calculate the indexes for the left, middle, and right items
        if (currentIndex === 0) {
            leftIndex = servicesData.length - 1; // Set left index to the last item in the array
            rightIndex = currentIndex + 1;
        } else {
            leftIndex = currentIndex - 1;
            rightIndex = (currentIndex + 1) % servicesData.length;
        }
    
        // Set items based on calculated indexes
        setItemLeft(servicesData[leftIndex]);
        setItem(servicesData[middleIndex]);
        setItemRight(servicesData[rightIndex]);
    }, [currentIndex, servicesData]);
    
    const prev = () => {

        if (currentIndex > 1) {
            setCurrentIndex(currentIndex - 1);
        }
     else{
        setCurrentIndex(servicesData.length-1);
     }
        setAnimateId(Date.now() + "goLeft");
    }

    const next = () => {
        if (currentIndex < servicesData.length - 1) {
            setCurrentIndex(currentIndex + 1);
        }
        else{
        setCurrentIndex(0);
        }
        setAnimateId(Date.now() + "id");
    }

    useEffect(() => {

    }, [animatedId]);

    return (<div className="swapAll d-flex align-items-center" style={{ margin: '0 auto', maxWidth: "max-content" }}>
        <div className='btn' style={{ padding: 20 }}>
            <IconButton onClick={() => {
                prev()
            }} color='success' style={{ border: "1px solid var(--green)" }}>
                <FaAngleLeft />
            </IconButton>
        </div>
        <SwipeItemLeft animatedId={animatedId} item={itemLeft} />
        <SwipeItem animatedId={animatedId} item={item} />
        <SwipeItemRight animatedId={animatedId} item={itemRight} />
        <div className='btn2' style={{ padding: 20 }}>
            <IconButton onClick={() => {
                next()
            }} color='success' style={{ border: "1px solid var(--green)" }}>
                <FaAngleRight />
            </IconButton>
        </div>

    </div>)
}

export default SwipeAll;