import { Button } from "@mui/material";
import { MDBBtn } from "mdb-react-ui-kit";
import Link from "next/link";
import React from "react";

export default function IntroSmallText() {
  return (
    <>
      <div
        className="elementor-text-editor elementor-clearfix"
        style={{
          boxSizing: "border-box",
          color: "rgb(255, 255, 255)",
          fontFamily: "Rubik, sans-serif",
          fontWeight: 300,
          font: "300 18px / 27px Rubik, sans-serif",
        }}
      >
        <p
          style={{
            margin: "0px 0px 18px",
            padding: "0px",
            lineHeight: 1.5,
            boxSizing: "border-box",
          }}
        >
          Empower your digital asset strategies with 
          our advanced market making and automated trading technologies


        </p>
        <p
          style={{
            margin: "0px 0px 18px",
            lineHeight: 1.5,
            boxSizing: "border-box",
            padding: "0px",
            marginBottom: "0px",
          }}
        >
          We have the best blockchain developers in the market who
          can offer you feature-rich Blockchain development services
          that are tailored to your business challenges
        </p>{" "}
        <br/><br/>

<div className='d-flex '>      <Link href='/portfolio'> <Button size='large' variant="outlined">Our portfolio</Button></Link>
<Link href='/contactUs'><MDBBtn size='lg' color='success' style={{marginLeft:10,background:'var(--green)'}}>Get Free estimation</MDBBtn></Link>

        </div>
      </div>    </>
  );
}
