import ColoredHeader from '@/utils/coloredHeader'
import PText from '@/utils/pText'
import { MDBBtn } from 'mdb-react-ui-kit'
import Image from 'next/image'
import React from 'react'

const TakeYourBusiness:React.FC=()=>{
    return (
        <>
        <div className='yourBusiness m-block d-flex align-items-center justify-content-between'>
<div style={{width:"65%"}}>
<ColoredHeader>
Take Your Business to the Next Level With Us!
</ColoredHeader>
<br/>
<PText>
We are looking to work with people 
and companies that are serious about the mass adoption of blockchain,
 metaverse, NFT, and crypto to explore new opportunities
</PText>
<div style={{padding:"10px 30px"}}><MDBBtn className='m-fullWidth' style={{marginLeft:40}} size='lg' color='success' >JOIN WITH US</MDBBtn></div>

    </div>

<div style={{width:"35%"}}>
<Image src='https://blockchaintechs.io/wp-content/uploads/2022/11/Are-you-looking-to-partner-with-metaverse-300x231.webp' width={500} height={500} alt=''/>
    </div>
    

        </div>
        </>
    )
}

export default TakeYourBusiness;