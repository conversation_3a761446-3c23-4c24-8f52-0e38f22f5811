import ColoredHeader from '@/utils/coloredHeader'
import React from 'react'
import ExploreCard from './exploreCard'

const ExploreCards:React.FC=()=>{
const cards=[
    {
        image:"/images/icons/Blockchain_Consulting.webp",
        heading:"Blockchain Consulting",
        text:"Choosing platforms, designing systems, feasibility assessments, and initiating proof-of-concept endeavors are pivotal."
    },{
        image:"/images/icons/Smart_Contracts.webp",
        heading:"Smart Contracts",
        text:"Design, code, test, deploy contracts ensuring functionality, compliance, reducing manual intervention."
    },{
        image:"/images/icons/Crypto_Exchanges.webp",
        heading:"Crypto Exchanges        ",
        text:"Facilitating order matching, integrating wallets, managing liquidity, providing trading APIs, and ensuring robust security measures are essential.        "
    },{
        image:"/images/icons/Crypto_Exchanges.webp",
        heading:"Real Estate Tokenization",
        text:"Tokenize real estate ownership, allowing for fractional ownership and democratizing access to investment opportunities in the property market."
    },
    
    {
        image:"/images/icons/Real_estate_tokenization.webp",
        heading:"Real Estate Tokenization        ",
        text:"Tokenize real estate ownership, allowing for fractional ownership and democratizing access to investment opportunities in the property market."
    },

    {
        image:"/images/icons/Play2Earn_Games.webp",
        heading:"Play 2 Earn Game",
        text:"Engage in 3D gaming, earn digital assets, explore NFTs, and craft intricate models, environments, and characters for immersive virtual experiences.        "
    },{
        image:"/images/icons/NFT_Marketplace.webp",
        heading:"NFT Marketplace        ",
        text:"Engage in purchasing, selling, and trading NFTs, which symbolize exclusive digital assets, within a dynamic and evolving marketplace environment."
    },{
        image:"/images/icons/Metaverse.webp",
        heading:"Metaverse",
        text:"Creating immersive digital realms for interactive experiences, gaming, socialization, education, and entertainment fosters engaging and dynamic environments for diverse user engagement.        "
    },{
        image:"/images/icons/Multichain_Wallet.webp",
        heading:"Multichain Wallet        ",
        text:"Strengthen security, privacy, and interoperability to enable seamless access to decentralized finance (DeFi) services and enhance user experience.        "
    },{
        image:"/images/icons/Move2Earn-Mobile-Apps.webp",
        heading:"Crypto Payment Gateway        ",
        text:"Enable secure transactions, seamlessly integrate with websites, and accept various cryptocurrencies to enhance flexibility and accessibility for users.        "
    },{
        image:"/images/icons/DeF-dApps-Development.webp",
        heading:"DeFi dApps Development",
        text:"Access financial services while emphasizing transparency, interoperability, and composability to ensure seamless integration and innovation within the ecosystem. "
    },{
        image:"/images/icons/Crowdfunding_Platform.webp",
        heading:"Crowdfunding Platform",
        text:"Develop user interfaces, smart contracts, and robust security protocols to facilitate token sales and manage investment pooling with confidence and efficiency.        "
    },{
        image:"/images/icons/Move2Earn-Mobile-Apps.webp",
        heading:"Move to Earn Mobile Apps        ",
        text:"Participate in diverse activities to earn rewards, fostering engagement and incentivizing involvement across various platforms and ecosystems.        "
    },{
        image:"/images/icons/ARVR.webp",
        heading:"AR/VR        ",
        text:"Craft immersive experiences that seamlessly merge virtual elements with reality, offering innovative interactions and enriching engagements across physical and digital realms.        "
    },{
        image:"/images/icons/Layer-1-2-Blockchains.webp",
        heading:"Layer-1 & 2 Blockchains",
        text:"Pioneer foundational protocol development, implementing scalable solutions to propel innovation and address the evolving needs of decentralized networks.        "
    },{
        image:"/images/icons/Staking-and-Swapping.webp",
        heading:"Staking & Swap Platform        ",
        text:"Develop user-friendly interfaces for staking, efficiently manage rewards, and seamlessly integrate with diverse blockchain networks to optimize user experience.        "
    },{
        image:"/images/icons/DAO_Protocol.webp",
        heading:"DAO Protocol",
        text:"Establish a framework for decentralized autonomous organizations (DAOs), utilizing smart contracts to govern and enhance organizational decision-making processes.        "
    },{
        image:"/images/icons/ICOVesting_Platform.webp",
        heading:"Whitelisted ICO/Vesting platform        ",
        text:"Establish a secure, compliant environment for token sales, prioritizing investor protection through the implementation of robust vesting mechanisms.        "
    }
]


    return (
        <>
        
        <ColoredHeader>Explore Our Blockchain Development Services</ColoredHeader>
<br/>
<div className='d-flex ' style={{padding:18,gap:10,flexFlow:"row wrap"}}>
    {cards.map((item:any,i:number)=><ExploreCard key={i} heading={item.heading} text={item.text} image={item.image}/>)}
</div>



        </>
    )
}

export default ExploreCards;