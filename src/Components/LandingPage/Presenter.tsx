import React from 'react';
import { MDBContainer } from 'mdb-react-ui-kit';
import "@/Components/LandingPage/style.css";
import Image from 'next/image';
import IntroText from './introText';
import IntroSmallText from './intoSmallText';

const Presenter: React.FC = () => {
  return (
    <MDBContainer fluid className='presenter-container' role="banner">
      <div className='presenter d-flex align-items-center'>
        <div style={{maxWidth:"57%"}} className="hero-content">
          <IntroText/>
          <IntroSmallText/>
        </div>

        <div style={{width:"40%",position:"relative"}} className='imageContainer'>
          <Image
            width={500}
            height={500}
            alt="AltAppLabs blockchain development services - innovative crypto solutions"
            style={{width:"100%",minHeight:'80vh'}}
            src='/images/hero2.png'
            priority
            quality={85}
            placeholder="blur"
            blurDataURL="data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQABAAD/2wBDAAYEBQYFBAYGBQYHBwYIChAKCgkJChQODwwQFxQYGBcUFhYaHSUfGhsjHBYWICwgIyYnKSopGR8tMC0oMCUoKSj/2wBDAQcHBwoIChMKChMoGhYaKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCj/wAARCAAIAAoDASIAAhEBAxEB/8QAFQABAQAAAAAAAAAAAAAAAAAAAAv/xAAhEAACAQMDBQAAAAAAAAAAAAABAgMABAUGIWGRkqGx0f/EABUBAQEAAAAAAAAAAAAAAAAAAAMF/8QAGhEAAgIDAAAAAAAAAAAAAAAAAAECEgMRkf/aAAwDAQACEQMRAD8AltJagyeH0AthI5xdrLcNM91BF5pX2HaH9bcfaSXWGaRmknyJckliyjqTzSlT54b6bk+h0R//2Q=="
          />
        </div>
      </div>
    </MDBContainer>
  );
}

export default Presenter;
