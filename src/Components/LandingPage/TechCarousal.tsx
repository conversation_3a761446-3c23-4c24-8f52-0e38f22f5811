import TechnologyItem from '@/utils/technologyItem';
import React from 'react';
import AliceCarousel from 'react-alice-carousel';
import 'react-alice-carousel/lib/alice-carousel.css';

interface TechCarouselProps {
  techImages: string[];
}

const TechCarousel: React.FC<TechCarouselProps> = ({ techImages }) => {
  return (
    <div style={{width:'90%',maxWidth:'100vw',overflow:'hidden',margin:'0 auto'}}><AliceCarousel 
autoPlay
autoPlayInterval={1500}
infinite
    responsive={{
        0: { items: 1},
      200:{items:2},

        400:{items:3},

        600:{items:4},
        768: { items: 6 },
        1024: { items: 8 },
      }}
    mouseTracking 
    disableDotsControls
disableButtonsControls
    items={techImages.map((img:string,i:number) => <TechnologyItem key={i} img={img} />)} />
  </div>
  );
};

export default TechCarousel;
