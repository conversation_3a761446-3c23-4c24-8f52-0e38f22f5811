import ColoredHeader from '@/utils/coloredHeader';
import React from 'react'
import BlogCard from './blogCard';
import Bold from '@/utils/pBolder';

interface Props {

}
const Blogs: React.FC = () => {
    const blogs = [
        {

            title: 'Top Web 3.0 Business Ideas to Make Rich in 2023',
            text: 'Top Web 3.0 Business Ideas to Make Rich in 2023 Web3 commerce has revolutionized the digital world and created a wide range of new business',
            image: 'https://blockchaintechs.io/wp-content/uploads/2022/11/Top-web-3.0-Business-Ideas-to-make-you-rich-in-2023-Banner-300x165.webp',
        },
        {

            title: 'Metaverse in Education: Redefining the Learning Experience...            ',
            text: 'The metaverse in education could be seen as an upgraded educational environment that combines components of the real and virtual educational environments with metaverse technologies.',
            image: 'https://blockchaintechs.io/wp-content/uploads/2022/12/Metaverse-in-Education.webp',
        },
        {

            title: 'Metaverse as a Services',
            text: 'Metaverse As A Services An Overview of Metaverse The metaverse is Users can take part in a social economy based on real-world circumstances in the            ',
            image: 'https://blockchaintechs.io/wp-content/uploads/2022/11/Top-web-3.0-Business-Ideas-to-make-you-rich-in-2023-Banner.webp',
        },
        {

            title: 'Web 3.0 Transform the Future of Finance Industry            ',
            text: 'Web 3.0 Transform the Future of Finance Industry Web 3.0 a potential future version of the internet. Web 3.0 is interesting because it is decentralized            ',
            image: 'https://blockchaintechs.io/wp-content/uploads/2022/12/Metaverse-as-a-Service-1.webp',
        },
        
    ]
    return (
        <>
            <ColoredHeader>
                Our Insights
            </ColoredHeader>
            <br /><br />
            <div className='BlogMainContainer d-flex ' style={{ padding: 20, maxWidth: '95%', margin: "0 auto" }}>

                <div style={{ width: "40%"}}>
                    <BlogCard isBig={true} style={{width:"100%",maxWidth:"100%" }} title='Top Metaverse Business Ideas in 2023' text='Top Metaverse Business Ideas in 2023 The metaverse is the big trend in the digital world. From an investing perspective, there is no better place' readMore={() => {
                        console.log("nothing...")
                    }}
                        image='https://blockchaintechs.io/wp-content/uploads/2022/12/Top-Metaverse-Business-Ideas-150x150.webp'

                    />
                </div>

                <div style={{ width: "60%" }} className='blogsLists d-flex'> 
                    {blogs.map((e,index)=><BlogCard readMore={()=>console.log("nothing...")}
                     style={{ width:"45%",margin:'2%'}} {...e} key={index}/> )}
                </div>

            </div>

        </>
    )
}
export default Blogs;