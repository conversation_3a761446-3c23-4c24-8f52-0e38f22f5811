import Image from "next/image";
import React from "react";

export default function SnapShotAnimation() {
  return (
    <>
      <div
        className="elementor-main-swiper swiper-container swiper-container-initialized swiper-container-horizontal"
        style={{
          boxSizing: "border-box",
          marginLeft: "auto",
          marginRight: "auto",
          zIndex: 1,
          borderRadius: "16px",
          overflow: "hidden",
          height: "500px",
          paddingTop: "180px",
          paddingRight: "0px",
          position: "relative",
          width: "calc(98% - 40px)",
          cursor: "grab",
        }}
      >
        <div
          className="swiper-wrapper"
          style={{
            position: "relative",
            width: "100%",
            height: "100%",
            zIndex: 1,
            transitionProperty: "transform",
            boxSizing: "content-box",
            display: "flex",
            WebkitBoxAlign: "stretch",
            alignItems: "stretch",
            transitionDuration: "0ms",
            transform: "translate3d(-4544px, 0px, 0px)",
          }}
        >
          <div
            className="swiper-slide swiper-slide-duplicate"
            style={{
              boxSizing: "border-box",
              flexShrink: 0,
              position: "relative",
              borderStyle: "solid",
              borderWidth: "0px",
              overflow: "hidden",
              willChange: "transform",
              transitionDuration: "0.5s",
              transitionProperty:
                "border, background, transform, -webkit-transform",
              padding: "0px 35px",
              top: "-100px",
              height: "250px",
              width: "368.667px",
              marginRight: "10px",
            }}
          >
            <div
              className="elementor-testimonial"
              style={{ boxSizing: "border-box", textAlign: "center" }}
            >
              <div
                className="elementor-testimonial__content"
                style={{ boxSizing: "border-box" }}
              >
                <div
                  className="elementor-testimonial__text"
                  style={{
                    boxSizing: "border-box",
                    fontSize: "1.3em",
                    fontStyle: "italic",
                    lineHeight: 1.5,
                    fontFamily: "Rubik, sans-serif",
                  }}
                >
                  <div
                    className="outer_box_content"
                    style={{
                      boxSizing: "border-box",
                      padding: "0px",
                      display: "flex",
                      alignItems: "center",
                      flexDirection: "column",
                    }}
                  >
                    <div
                      className="inner_box_content"
                      style={{
                        boxSizing: "border-box",
                        padding: "0px 20px",
                        display: "flex",
                        alignItems: "center",
                        flexDirection: "column",
                      }}
                    >
                      <div
                        className="background_logo"
                        style={{
                          boxSizing: "border-box",
                          background: "rgb(2, 11, 18)",
                          padding: "12px",
                          borderRadius: "100%",
                          width: "125px",
                          marginBottom: "10px",
                          position: "relative",
                        }}
                      >
                        <Image
                          height={101}
                          width={101}
                          alt="Stakinggocity"
                          src="https://blockchaintechs.io/wp-content/uploads/2022/08/Stakingocity-1.webp"
                          style={{
                            userSelect: "none",
                            verticalAlign: "top",
                            boxSizing: "border-box",
                            border: "none",
                            borderRadius: "0px",
                            height: "auto",
                            maxWidth: "100%",
                            boxShadow: "none",
                            objectFit: "contain",
                            width: "262px",
                            outline: "none",
                            pointerEvents: "none",
                          }}
                        />
                      </div>
                      <h3
                        style={{
                          margin: "0px",
                          padding: "0px",
                          overflowWrap: "break-word",
                          boxSizing: "border-box",
                          fontFamily: "rubik",
                          fontWeight: 500,
                          fontStyle: "normal",
                          fontSize: "15px",
                          lineHeight: 1.15,
                          textTransform: "uppercase",
                          marginBottom: "5px",
                          color: "rgb(0, 212, 108)",
                        }}
                      >
                        NFT Staking Platform
                      </h3>
                      <p
                        style={{
                          margin: "0px 0px 18px",
                          padding: "0px",
                          lineHeight: 1.5,
                          boxSizing: "border-box",
                          color: "rgb(235, 235, 235)",
                          fontFamily: "rubik",
                          fontStyle: "normal",
                          fontWeight: 300,
                          fontSize: "14px",
                        }}
                      >
                        NFT staking platform Individuals can earn rewards based
                        on the Annual Percentage Yield (APY)
                      </p>
                      <button
                        style={{
                          outline: "none",
                          padding: "10px 30px",
                          margin: "0px 0px 15px",
                          border: "none",
                          borderRadius: "5px",
                          width: "auto",
                          cursor: "pointer",
                          lineHeight: "23px",
                          fontSize: "12px",
                          fontWeight: 500,
                          textTransform: "uppercase",
                          letterSpacing: "0.2px",
                          color: "rgb(255, 255, 255)",
                          transition: "all 0.4s ease-in-out 0s",
                          boxSizing: "border-box",
                          background: "rgb(0, 212, 108)",
                          backgroundImage: "initial",
                          backgroundSize: "initial",
                          height: "unset",
                          display: "none",
                        }}
                      >
                        <a
                          href="#"
                          target="_blank"
                          style={{
                            boxSizing: "border-box",
                            textDecoration: "none",
                            boxShadow: "none",
                            display: "inline",
                            transition: "all 0.1s ease 0s",
                            color: "rgb(0, 0, 0)",
                            fontWeight: 600,
                            fontFamily: "rubik",
                            fontSize: "13px",
                            outline: "none",
                          }}
                        >
                          View More
                        </a>
                      </button>
                    </div>
                  </div>{" "}
                </div>
              </div>
              <div
                className="elementor-testimonial__footer"
                style={{
                  boxSizing: "border-box",
                  display: "flex",
                  WebkitBoxAlign: "center",
                  alignItems: "center",
                  WebkitBoxPack: "center",
                  justifyContent: "center",
                  marginTop: "0px",
                }}
              />
            </div>
          </div>
          <div
            className="swiper-slide swiper-slide-duplicate"
            style={{
              boxSizing: "border-box",
              flexShrink: 0,
              position: "relative",
              borderStyle: "solid",
              borderWidth: "0px",
              overflow: "hidden",
              willChange: "transform",
              transitionDuration: "0.5s",
              transitionProperty:
                "border, background, transform, -webkit-transform",
              padding: "0px 35px",
              top: "-100px",
              height: "250px",
              width: "368.667px",
              marginRight: "10px",
            }}
          >
            <div
              className="elementor-testimonial"
              style={{ boxSizing: "border-box", textAlign: "center" }}
            >
              <div
                className="elementor-testimonial__content"
                style={{ boxSizing: "border-box" }}
              >
                <div
                  className="elementor-testimonial__text"
                  style={{
                    boxSizing: "border-box",
                    fontSize: "1.3em",
                    fontStyle: "italic",
                    lineHeight: 1.5,
                    fontFamily: "Rubik, sans-serif",
                  }}
                >
                  <div
                    className="outer_box_content"
                    style={{
                      boxSizing: "border-box",
                      padding: "0px",
                      display: "flex",
                      alignItems: "center",
                      flexDirection: "column",
                    }}
                  >
                    <div
                      className="inner_box_content"
                      style={{
                        boxSizing: "border-box",
                        padding: "0px 20px",
                        display: "flex",
                        alignItems: "center",
                        flexDirection: "column",
                      }}
                    >
                      <div
                        className="background_logo"
                        style={{
                          boxSizing: "border-box",
                          background: "rgb(2, 11, 18)",
                          padding: "12px",
                          borderRadius: "100%",
                          width: "125px",
                          marginBottom: "10px",
                          position: "relative",
                        }}
                      >
                        <Image
                          height={101}
                          width={101}
                          alt="Lendspot"
                          src="https://blockchaintechs.io/wp-content/uploads/2022/11/Lendspot.webp"
                          style={{
                            userSelect: "none",
                            verticalAlign: "top",
                            boxSizing: "border-box",
                            border: "none",
                            borderRadius: "0px",
                            height: "auto",
                            maxWidth: "100%",
                            boxShadow: "none",
                            objectFit: "contain",
                            width: "262px",
                            outline: "none",
                            pointerEvents: "none",
                          }}
                        />
                      </div>
                      <h3
                        style={{
                          margin: "0px",
                          padding: "0px",
                          overflowWrap: "break-word",
                          boxSizing: "border-box",
                          fontFamily: "rubik",
                          fontWeight: 500,
                          fontStyle: "normal",
                          fontSize: "15px",
                          lineHeight: 1.15,
                          textTransform: "uppercase",
                          marginBottom: "5px",
                          color: "rgb(0, 212, 108)",
                        }}
                      >
                        NFT Lending & Borrowing
                      </h3>
                      <p
                        style={{
                          margin: "0px 0px 18px",
                          padding: "0px",
                          lineHeight: 1.5,
                          boxSizing: "border-box",
                          color: "rgb(235, 235, 235)",
                          fontFamily: "rubik",
                          fontStyle: "normal",
                          fontWeight: 300,
                          fontSize: "14px",
                        }}
                      >
                        Get crypto loans against NFTs and lend crypto to
                        generate additional income.
                      </p>
                      <button
                        style={{
                          outline: "none",
                          padding: "10px 30px",
                          margin: "0px 0px 15px",
                          border: "none",
                          borderRadius: "5px",
                          width: "auto",
                          cursor: "pointer",
                          lineHeight: "23px",
                          fontSize: "12px",
                          fontWeight: 500,
                          textTransform: "uppercase",
                          letterSpacing: "0.2px",
                          color: "rgb(255, 255, 255)",
                          transition: "all 0.4s ease-in-out 0s",
                          boxSizing: "border-box",
                          background: "rgb(0, 212, 108)",
                          backgroundImage: "initial",
                          backgroundSize: "initial",
                          height: "unset",
                          display: "none",
                        }}
                      >
                        <a
                          href="#"
                          target="_blank"
                          style={{
                            boxSizing: "border-box",
                            textDecoration: "none",
                            boxShadow: "none",
                            display: "inline",
                            transition: "all 0.1s ease 0s",
                            color: "rgb(0, 0, 0)",
                            fontWeight: 600,
                            fontFamily: "rubik",
                            fontSize: "13px",
                            outline: "none",
                          }}
                        >
                          View More
                        </a>
                      </button>
                    </div>
                  </div>{" "}
                </div>
              </div>
              <div
                className="elementor-testimonial__footer"
                style={{
                  boxSizing: "border-box",
                  display: "flex",
                  WebkitBoxAlign: "center",
                  alignItems: "center",
                  WebkitBoxPack: "center",
                  justifyContent: "center",
                  marginTop: "0px",
                }}
              />
            </div>
          </div>
          <div
            className="swiper-slide swiper-slide-duplicate"
            style={{
              boxSizing: "border-box",
              flexShrink: 0,
              position: "relative",
              borderStyle: "solid",
              borderWidth: "0px",
              overflow: "hidden",
              willChange: "transform",
              transitionDuration: "0.5s",
              transitionProperty:
                "border, background, transform, -webkit-transform",
              padding: "0px 35px",
              top: "-100px",
              height: "250px",
              width: "368.667px",
              marginRight: "10px",
            }}
          >
            <div
              className="elementor-testimonial"
              style={{ boxSizing: "border-box", textAlign: "center" }}
            >
              <div
                className="elementor-testimonial__content"
                style={{ boxSizing: "border-box" }}
              >
                <div
                  className="elementor-testimonial__text"
                  style={{
                    boxSizing: "border-box",
                    fontSize: "1.3em",
                    fontStyle: "italic",
                    lineHeight: 1.5,
                    fontFamily: "Rubik, sans-serif",
                  }}
                >
                  <div
                    className="outer_box_content"
                    style={{
                      boxSizing: "border-box",
                      padding: "0px",
                      display: "flex",
                      alignItems: "center",
                      flexDirection: "column",
                    }}
                  >
                    <div
                      className="inner_box_content"
                      style={{
                        boxSizing: "border-box",
                        padding: "0px 20px",
                        display: "flex",
                        alignItems: "center",
                        flexDirection: "column",
                      }}
                    >
                      <div
                        className="background_logo"
                        style={{
                          boxSizing: "border-box",
                          background: "rgb(2, 11, 18)",
                          padding: "12px",
                          borderRadius: "100%",
                          width: "125px",
                          marginBottom: "10px",
                          position: "relative",
                        }}
                      >
                        <Image
                          height={101}
                          width={101}
                          alt="Expedited"
                          src="https://blockchaintechs.io/wp-content/uploads/2022/11/ExpeditedRound.webp"
                          style={{
                            userSelect: "none",
                            verticalAlign: "top",
                            boxSizing: "border-box",
                            border: "none",
                            borderRadius: "0px",
                            height: "auto",
                            maxWidth: "100%",
                            boxShadow: "none",
                            objectFit: "contain",
                            width: "262px",
                            outline: "none",
                            pointerEvents: "none",
                          }}
                        />
                      </div>
                      <h3
                        style={{
                          margin: "0px",
                          padding: "0px",
                          overflowWrap: "break-word",
                          boxSizing: "border-box",
                          fontFamily: "rubik",
                          fontWeight: 500,
                          fontStyle: "normal",
                          fontSize: "15px",
                          lineHeight: 1.15,
                          textTransform: "uppercase",
                          marginBottom: "5px",
                          color: "rgb(0, 212, 108)",
                        }}
                      >
                        Recruitment Platform
                      </h3>
                      <p
                        style={{
                          margin: "0px 0px 18px",
                          padding: "0px",
                          lineHeight: 1.5,
                          boxSizing: "border-box",
                          color: "rgb(235, 235, 235)",
                          fontFamily: "rubik",
                          fontStyle: "normal",
                          fontWeight: 300,
                          fontSize: "14px",
                        }}
                      >
                        New Gen Recruitement Platform with Candidates and
                        Recruiters
                      </p>
                      <button
                        style={{
                          outline: "none",
                          padding: "10px 30px",
                          margin: "0px 0px 15px",
                          border: "none",
                          borderRadius: "5px",
                          width: "auto",
                          cursor: "pointer",
                          lineHeight: "23px",
                          fontSize: "12px",
                          fontWeight: 500,
                          textTransform: "uppercase",
                          letterSpacing: "0.2px",
                          color: "rgb(255, 255, 255)",
                          transition: "all 0.4s ease-in-out 0s",
                          boxSizing: "border-box",
                          background: "rgb(0, 212, 108)",
                          backgroundImage: "initial",
                          backgroundSize: "initial",
                          height: "unset",
                          display: "none",
                        }}
                      >
                        <a
                          href="#"
                          target="_blank"
                          style={{
                            boxSizing: "border-box",
                            textDecoration: "none",
                            boxShadow: "none",
                            display: "inline",
                            transition: "all 0.1s ease 0s",
                            color: "rgb(0, 0, 0)",
                            fontWeight: 600,
                            fontFamily: "rubik",
                            fontSize: "13px",
                            outline: "none",
                          }}
                        >
                          View More
                        </a>
                      </button>
                    </div>
                  </div>{" "}
                </div>
              </div>
              <div
                className="elementor-testimonial__footer"
                style={{
                  boxSizing: "border-box",
                  display: "flex",
                  WebkitBoxAlign: "center",
                  alignItems: "center",
                  WebkitBoxPack: "center",
                  justifyContent: "center",
                  marginTop: "0px",
                }}
              />
            </div>
          </div>
          <div
            className="swiper-slide"
            style={{
              boxSizing: "border-box",
              flexShrink: 0,
              position: "relative",
              borderStyle: "solid",
              borderWidth: "0px",
              overflow: "hidden",
              willChange: "transform",
              transitionDuration: "0.5s",
              transitionProperty:
                "border, background, transform, -webkit-transform",
              padding: "0px 35px",
              top: "-100px",
              height: "250px",
              width: "368.667px",
              marginRight: "10px",
            }}
          >
            <div
              className="elementor-testimonial"
              style={{ boxSizing: "border-box", textAlign: "center" }}
            >
              <div
                className="elementor-testimonial__content"
                style={{ boxSizing: "border-box" }}
              >
                <div
                  className="elementor-testimonial__text"
                  style={{
                    boxSizing: "border-box",
                    fontSize: "1.3em",
                    fontStyle: "italic",
                    lineHeight: 1.5,
                    fontFamily: "Rubik, sans-serif",
                  }}
                >
                  <div
                    className="outer_box_content"
                    style={{
                      boxSizing: "border-box",
                      padding: "0px",
                      display: "flex",
                      alignItems: "center",
                      flexDirection: "column",
                    }}
                  >
                    <div
                      className="inner_box_content"
                      style={{
                        boxSizing: "border-box",
                        padding: "0px 20px",
                        display: "flex",
                        alignItems: "center",
                        flexDirection: "column",
                      }}
                    >
                      <div
                        className="background_logo"
                        style={{
                          boxSizing: "border-box",
                          background: "rgb(2, 11, 18)",
                          padding: "12px",
                          borderRadius: "100%",
                          width: "125px",
                          marginBottom: "10px",
                          position: "relative",
                        }}
                      >
                        <Image
                          height={101}
                          width={101}
                          alt="Lockness"
                          src="https://blockchaintechs.io/wp-content/uploads/2022/11/Lockness.webp"
                          style={{
                            userSelect: "none",
                            verticalAlign: "top",
                            boxSizing: "border-box",
                            border: "none",
                            borderRadius: "0px",
                            height: "auto",
                            maxWidth: "100%",
                            boxShadow: "none",
                            objectFit: "contain",
                            width: "262px",
                            outline: "none",
                            pointerEvents: "none",
                          }}
                        />
                      </div>
                      <h3
                        style={{
                          margin: "0px",
                          padding: "0px",
                          overflowWrap: "break-word",
                          boxSizing: "border-box",
                          fontFamily: "rubik",
                          fontWeight: 500,
                          fontStyle: "normal",
                          fontSize: "15px",
                          lineHeight: 1.15,
                          textTransform: "uppercase",
                          marginBottom: "5px",
                          color: "rgb(0, 212, 108)",
                        }}
                      >
                        Escrow Payment Platform
                      </h3>
                      <p
                        style={{
                          margin: "0px 0px 18px",
                          padding: "0px",
                          lineHeight: 1.5,
                          boxSizing: "border-box",
                          color: "rgb(235, 235, 235)",
                          fontFamily: "rubik",
                          fontStyle: "normal",
                          fontWeight: 300,
                          fontSize: "14px",
                        }}
                      >
                        The PayPal of crypto aims on providing a safe and secure
                        P2P Payment
                      </p>
                      <button
                        style={{
                          outline: "none",
                          padding: "10px 30px",
                          margin: "0px 0px 15px",
                          border: "none",
                          borderRadius: "5px",
                          width: "auto",
                          cursor: "pointer",
                          lineHeight: "23px",
                          fontSize: "12px",
                          fontWeight: 500,
                          textTransform: "uppercase",
                          letterSpacing: "0.2px",
                          color: "rgb(255, 255, 255)",
                          transition: "all 0.4s ease-in-out 0s",
                          boxSizing: "border-box",
                          background: "rgb(0, 212, 108)",
                          backgroundImage: "initial",
                          backgroundSize: "initial",
                          height: "unset",
                          display: "none",
                        }}
                      >
                        <a
                          href="##lockness"
                          target="_blank"
                          style={{
                            boxSizing: "border-box",
                            textDecoration: "none",
                            boxShadow: "none",
                            display: "inline",
                            transition: "all 0.1s ease 0s",
                            color: "rgb(0, 0, 0)",
                            fontWeight: 600,
                            fontFamily: "rubik",
                            fontSize: "13px",
                            outline: "none",
                          }}
                        >
                          See More
                        </a>
                      </button>
                    </div>
                  </div>{" "}
                </div>
              </div>
              <div
                className="elementor-testimonial__footer"
                style={{
                  boxSizing: "border-box",
                  display: "flex",
                  WebkitBoxAlign: "center",
                  alignItems: "center",
                  WebkitBoxPack: "center",
                  justifyContent: "center",
                  marginTop: "0px",
                }}
              />
            </div>
          </div>
          <div
            className="swiper-slide"
            style={{
              boxSizing: "border-box",
              flexShrink: 0,
              position: "relative",
              borderStyle: "solid",
              borderWidth: "0px",
              overflow: "hidden",
              willChange: "transform",
              transitionDuration: "0.5s",
              transitionProperty:
                "border, background, transform, -webkit-transform",
              padding: "0px 35px",
              top: "-100px",
              height: "250px",
              width: "368.667px",
              marginRight: "10px",
            }}
          >
            <div
              className="elementor-testimonial"
              style={{ boxSizing: "border-box", textAlign: "center" }}
            >
              <div
                className="elementor-testimonial__content"
                style={{ boxSizing: "border-box" }}
              >
                <div
                  className="elementor-testimonial__text"
                  style={{
                    boxSizing: "border-box",
                    fontSize: "1.3em",
                    fontStyle: "italic",
                    lineHeight: 1.5,
                    fontFamily: "Rubik, sans-serif",
                  }}
                >
                  <div
                    className="outer_box_content"
                    style={{
                      boxSizing: "border-box",
                      padding: "0px",
                      display: "flex",
                      alignItems: "center",
                      flexDirection: "column",
                    }}
                  >
                    <div
                      className="inner_box_content"
                      style={{
                        boxSizing: "border-box",
                        padding: "0px 20px",
                        display: "flex",
                        alignItems: "center",
                        flexDirection: "column",
                      }}
                    >
                      <div
                        className="background_logo"
                        style={{
                          boxSizing: "border-box",
                          background: "rgb(2, 11, 18)",
                          padding: "12px",
                          borderRadius: "100%",
                          width: "125px",
                          marginBottom: "10px",
                          position: "relative",
                        }}
                      >
                        <Image
                          height={101}
                          width={101}
                          alt="Metabloqs"
                          src="https://blockchaintechs.io/wp-content/uploads/2022/08/Metabloqs.webp"
                          style={{
                            userSelect: "none",
                            verticalAlign: "top",
                            boxSizing: "border-box",
                            border: "none",
                            borderRadius: "0px",
                            height: "auto",
                            maxWidth: "100%",
                            boxShadow: "none",
                            objectFit: "contain",
                            width: "262px",
                            outline: "none",
                            pointerEvents: "none",
                          }}
                        />
                      </div>
                      <h3
                        style={{
                          margin: "0px",
                          padding: "0px",
                          overflowWrap: "break-word",
                          boxSizing: "border-box",
                          fontFamily: "rubik",
                          fontWeight: 500,
                          fontStyle: "normal",
                          fontSize: "15px",
                          lineHeight: 1.15,
                          textTransform: "uppercase",
                          marginBottom: "5px",
                          color: "rgb(0, 212, 108)",
                        }}
                      >
                        Metaverse Ecosystem
                      </h3>
                      <p
                        style={{
                          margin: "0px 0px 18px",
                          padding: "0px",
                          lineHeight: 1.5,
                          boxSizing: "border-box",
                          color: "rgb(235, 235, 235)",
                          fontFamily: "rubik",
                          fontStyle: "normal",
                          fontWeight: 300,
                          fontSize: "14px",
                        }}
                      >
                        Metabloqs will serve as the canvas for a metaverse that
                        is grounded in reality
                      </p>
                      <button
                        style={{
                          outline: "none",
                          padding: "10px 30px",
                          margin: "0px 0px 15px",
                          border: "none",
                          borderRadius: "5px",
                          width: "auto",
                          cursor: "pointer",
                          lineHeight: "23px",
                          fontSize: "12px",
                          fontWeight: 500,
                          textTransform: "uppercase",
                          letterSpacing: "0.2px",
                          color: "rgb(255, 255, 255)",
                          transition: "all 0.4s ease-in-out 0s",
                          boxSizing: "border-box",
                          background: "rgb(0, 212, 108)",
                          backgroundImage: "initial",
                          backgroundSize: "initial",
                          height: "unset",
                          display: "none",
                        }}
                      >
                        <a
                          href="#"
                          target="_blank"
                          style={{
                            boxSizing: "border-box",
                            textDecoration: "none",
                            boxShadow: "none",
                            display: "inline",
                            transition: "all 0.1s ease 0s",
                            color: "rgb(0, 0, 0)",
                            fontWeight: 600,
                            fontFamily: "rubik",
                            fontSize: "13px",
                            outline: "none",
                          }}
                        >
                          View More
                        </a>
                      </button>
                    </div>
                  </div>{" "}
                </div>
              </div>
              <div
                className="elementor-testimonial__footer"
                style={{
                  boxSizing: "border-box",
                  display: "flex",
                  WebkitBoxAlign: "center",
                  alignItems: "center",
                  WebkitBoxPack: "center",
                  justifyContent: "center",
                  marginTop: "0px",
                }}
              />
            </div>
          </div>
          <div
            className="swiper-slide"
            style={{
              boxSizing: "border-box",
              flexShrink: 0,
              position: "relative",
              borderStyle: "solid",
              borderWidth: "0px",
              overflow: "hidden",
              willChange: "transform",
              transitionDuration: "0.5s",
              transitionProperty:
                "border, background, transform, -webkit-transform",
              padding: "0px 35px",
              top: "-100px",
              height: "250px",
              width: "368.667px",
              marginRight: "10px",
            }}
          >
            <div
              className="elementor-testimonial"
              style={{ boxSizing: "border-box", textAlign: "center" }}
            >
              <div
                className="elementor-testimonial__content"
                style={{ boxSizing: "border-box" }}
              >
                <div
                  className="elementor-testimonial__text"
                  style={{
                    boxSizing: "border-box",
                    fontSize: "1.3em",
                    fontStyle: "italic",
                    lineHeight: 1.5,
                    fontFamily: "Rubik, sans-serif",
                  }}
                >
                  <div
                    className="outer_box_content"
                    style={{
                      boxSizing: "border-box",
                      padding: "0px",
                      display: "flex",
                      alignItems: "center",
                      flexDirection: "column",
                    }}
                  >
                    <div
                      className="inner_box_content"
                      style={{
                        boxSizing: "border-box",
                        padding: "0px 20px",
                        display: "flex",
                        alignItems: "center",
                        flexDirection: "column",
                      }}
                    >
                      <div
                        className="background_logo"
                        style={{
                          boxSizing: "border-box",
                          background: "rgb(2, 11, 18)",
                          padding: "12px",
                          borderRadius: "100%",
                          width: "125px",
                          marginBottom: "10px",
                          position: "relative",
                        }}
                      >
                        <Image
                          height={101}
                          width={101}
                          alt="Skippy"
                          src="https://blockchaintechs.io/wp-content/uploads/2022/11/Skippy.webp"
                          style={{
                            userSelect: "none",
                            verticalAlign: "top",
                            boxSizing: "border-box",
                            border: "none",
                            borderRadius: "0px",
                            height: "auto",
                            maxWidth: "100%",
                            boxShadow: "none",
                            objectFit: "contain",
                            width: "262px",
                            outline: "none",
                            pointerEvents: "none",
                          }}
                        />
                      </div>
                      <h3
                        style={{
                          margin: "0px",
                          padding: "0px",
                          overflowWrap: "break-word",
                          boxSizing: "border-box",
                          fontFamily: "rubik",
                          fontWeight: 500,
                          fontStyle: "normal",
                          fontSize: "15px",
                          lineHeight: 1.15,
                          textTransform: "uppercase",
                          marginBottom: "5px",
                          color: "rgb(0, 212, 108)",
                        }}
                      >
                        Apparels Marketplace
                      </h3>
                      <p
                        style={{
                          margin: "0px 0px 18px",
                          padding: "0px",
                          lineHeight: 1.5,
                          boxSizing: "border-box",
                          color: "rgb(235, 235, 235)",
                          fontFamily: "rubik",
                          fontStyle: "normal",
                          fontWeight: 300,
                          fontSize: "14px",
                        }}
                      >
                        Apparels marketplace with fiat and crypto payment
                        gateway integrated.
                      </p>
                      <button
                        style={{
                          outline: "none",
                          padding: "10px 30px",
                          margin: "0px 0px 15px",
                          border: "none",
                          borderRadius: "5px",
                          width: "auto",
                          cursor: "pointer",
                          lineHeight: "23px",
                          fontSize: "12px",
                          fontWeight: 500,
                          textTransform: "uppercase",
                          letterSpacing: "0.2px",
                          color: "rgb(255, 255, 255)",
                          transition: "all 0.4s ease-in-out 0s",
                          boxSizing: "border-box",
                          background: "rgb(0, 212, 108)",
                          backgroundImage: "initial",
                          backgroundSize: "initial",
                          height: "unset",
                          display: "none",
                        }}
                      >
                        <a
                          target="_blank"
                          style={{
                            boxSizing: "border-box",
                            textDecoration: "none",
                            boxShadow: "none",
                            display: "inline",
                            transition: "all 0.1s ease 0s",
                            color: "rgb(0, 0, 0)",
                            fontWeight: 600,
                            fontFamily: "rubik",
                            fontSize: "13px",
                            outline: "none",
                          }}
                        >
                          View More
                        </a>
                      </button>
                    </div>
                  </div>{" "}
                </div>
              </div>
              <div
                className="elementor-testimonial__footer"
                style={{
                  boxSizing: "border-box",
                  display: "flex",
                  WebkitBoxAlign: "center",
                  alignItems: "center",
                  WebkitBoxPack: "center",
                  justifyContent: "center",
                  marginTop: "0px",
                }}
              />
            </div>
          </div>
          <div
            className="swiper-slide"
            style={{
              boxSizing: "border-box",
              flexShrink: 0,
              position: "relative",
              borderStyle: "solid",
              borderWidth: "0px",
              overflow: "hidden",
              willChange: "transform",
              transitionDuration: "0.5s",
              transitionProperty:
                "border, background, transform, -webkit-transform",
              padding: "0px 35px",
              top: "-100px",
              height: "250px",
              width: "368.667px",
              marginRight: "10px",
            }}
          >
            <div
              className="elementor-testimonial"
              style={{ boxSizing: "border-box", textAlign: "center" }}
            >
              <div
                className="elementor-testimonial__content"
                style={{ boxSizing: "border-box" }}
              >
                <div
                  className="elementor-testimonial__text"
                  style={{
                    boxSizing: "border-box",
                    fontSize: "1.3em",
                    fontStyle: "italic",
                    lineHeight: 1.5,
                    fontFamily: "Rubik, sans-serif",
                  }}
                >
                  <div
                    className="outer_box_content"
                    style={{
                      boxSizing: "border-box",
                      padding: "0px",
                      display: "flex",
                      alignItems: "center",
                      flexDirection: "column",
                    }}
                  >
                    <div
                      className="inner_box_content"
                      style={{
                        boxSizing: "border-box",
                        padding: "0px 20px",
                        display: "flex",
                        alignItems: "center",
                        flexDirection: "column",
                      }}
                    >
                      <div
                        className="background_logo"
                        style={{
                          boxSizing: "border-box",
                          background: "rgb(2, 11, 18)",
                          padding: "12px",
                          borderRadius: "100%",
                          width: "125px",
                          marginBottom: "10px",
                          position: "relative",
                        }}
                      >
                        <Image
                          height={101}
                          width={101}
                          alt="BT Swap"
                          src="https://blockchaintechs.io/wp-content/uploads/2022/11/BT-Swap.webp"
                          style={{
                            userSelect: "none",
                            verticalAlign: "top",
                            boxSizing: "border-box",
                            border: "none",
                            borderRadius: "0px",
                            height: "auto",
                            maxWidth: "100%",
                            boxShadow: "none",
                            objectFit: "contain",
                            width: "262px",
                            outline: "none",
                            pointerEvents: "none",
                          }}
                        />
                      </div>
                      <h3
                        style={{
                          margin: "0px",
                          padding: "0px",
                          overflowWrap: "break-word",
                          boxSizing: "border-box",
                          fontFamily: "rubik",
                          fontWeight: 500,
                          fontStyle: "normal",
                          fontSize: "15px",
                          lineHeight: 1.15,
                          textTransform: "uppercase",
                          marginBottom: "5px",
                          color: "rgb(0, 212, 108)",
                        }}
                      >
                        Decentralized Crypto Exchange
                      </h3>
                      <p
                        style={{
                          margin: "0px 0px 18px",
                          padding: "0px",
                          lineHeight: 1.5,
                          boxSizing: "border-box",
                          color: "rgb(235, 235, 235)",
                          fontFamily: "rubik",
                          fontStyle: "normal",
                          fontWeight: 300,
                          fontSize: "14px",
                        }}
                      >
                        BT Swap is Hybrid multichain decentralized exchange
                        platform with AMM
                      </p>
                      <button
                        style={{
                          outline: "none",
                          padding: "10px 30px",
                          margin: "0px 0px 15px",
                          border: "none",
                          borderRadius: "5px",
                          width: "auto",
                          cursor: "pointer",
                          lineHeight: "23px",
                          fontSize: "12px",
                          fontWeight: 500,
                          textTransform: "uppercase",
                          letterSpacing: "0.2px",
                          color: "rgb(255, 255, 255)",
                          transition: "all 0.4s ease-in-out 0s",
                          boxSizing: "border-box",
                          background: "rgb(0, 212, 108)",
                          backgroundImage: "initial",
                          backgroundSize: "initial",
                          height: "unset",
                          display: "none",
                        }}
                      >
                        <a
                          href="##bt-swap"
                          target="_blank"
                          style={{
                            boxSizing: "border-box",
                            textDecoration: "none",
                            boxShadow: "none",
                            display: "inline",
                            transition: "all 0.1s ease 0s",
                            color: "rgb(0, 0, 0)",
                            fontWeight: 600,
                            fontFamily: "rubik",
                            fontSize: "13px",
                            outline: "none",
                          }}
                        >
                          View More
                        </a>
                      </button>
                    </div>
                  </div>{" "}
                </div>
              </div>
              <div
                className="elementor-testimonial__footer"
                style={{
                  boxSizing: "border-box",
                  display: "flex",
                  WebkitBoxAlign: "center",
                  alignItems: "center",
                  WebkitBoxPack: "center",
                  justifyContent: "center",
                  marginTop: "0px",
                }}
              />
            </div>
          </div>
          <div
            className="swiper-slide"
            style={{
              boxSizing: "border-box",
              flexShrink: 0,
              position: "relative",
              borderStyle: "solid",
              borderWidth: "0px",
              overflow: "hidden",
              willChange: "transform",
              transitionDuration: "0.5s",
              transitionProperty:
                "border, background, transform, -webkit-transform",
              padding: "0px 35px",
              top: "-100px",
              height: "250px",
              width: "368.667px",
              marginRight: "10px",
            }}
          >
            <div
              className="elementor-testimonial"
              style={{ boxSizing: "border-box", textAlign: "center" }}
            >
              <div
                className="elementor-testimonial__content"
                style={{ boxSizing: "border-box" }}
              >
                <div
                  className="elementor-testimonial__text"
                  style={{
                    boxSizing: "border-box",
                    fontSize: "1.3em",
                    fontStyle: "italic",
                    lineHeight: 1.5,
                    fontFamily: "Rubik, sans-serif",
                  }}
                >
                  <div
                    className="outer_box_content"
                    style={{
                      boxSizing: "border-box",
                      padding: "0px",
                      display: "flex",
                      alignItems: "center",
                      flexDirection: "column",
                    }}
                  >
                    <div
                      className="inner_box_content"
                      style={{
                        boxSizing: "border-box",
                        padding: "0px 20px",
                        display: "flex",
                        alignItems: "center",
                        flexDirection: "column",
                      }}
                    >
                      <div
                        className="background_logo"
                        style={{
                          boxSizing: "border-box",
                          background: "rgb(2, 11, 18)",
                          padding: "12px",
                          borderRadius: "100%",
                          width: "125px",
                          marginBottom: "10px",
                          position: "relative",
                        }}
                      >
                        <Image
                          height={101}
                          width={101}
                          alt="Doxazo"
                          src="https://blockchaintechs.io/wp-content/uploads/2022/11/Doxazo.webp"
                          style={{
                            userSelect: "none",
                            verticalAlign: "top",
                            boxSizing: "border-box",
                            border: "none",
                            borderRadius: "0px",
                            height: "auto",
                            maxWidth: "100%",
                            boxShadow: "none",
                            objectFit: "contain",
                            width: "262px",
                            outline: "none",
                            pointerEvents: "none",
                          }}
                        />
                      </div>
                      <h3
                        style={{
                          margin: "0px",
                          padding: "0px",
                          overflowWrap: "break-word",
                          boxSizing: "border-box",
                          fontFamily: "rubik",
                          fontWeight: 500,
                          fontStyle: "normal",
                          fontSize: "15px",
                          lineHeight: 1.15,
                          textTransform: "uppercase",
                          marginBottom: "5px",
                          color: "rgb(0, 212, 108)",
                        }}
                      >
                        Crypto Staking & Swapping
                      </h3>
                      <p
                        style={{
                          margin: "0px 0px 18px",
                          padding: "0px",
                          lineHeight: 1.5,
                          boxSizing: "border-box",
                          color: "rgb(235, 235, 235)",
                          fontFamily: "rubik",
                          fontStyle: "normal",
                          fontWeight: 300,
                          fontSize: "14px",
                        }}
                      >
                        A staking platform with your own token and earning
                        profits by staking
                      </p>
                      <button
                        style={{
                          outline: "none",
                          padding: "10px 30px",
                          margin: "0px 0px 15px",
                          border: "none",
                          borderRadius: "5px",
                          width: "auto",
                          cursor: "pointer",
                          lineHeight: "23px",
                          fontSize: "12px",
                          fontWeight: 500,
                          textTransform: "uppercase",
                          letterSpacing: "0.2px",
                          color: "rgb(255, 255, 255)",
                          transition: "all 0.4s ease-in-out 0s",
                          boxSizing: "border-box",
                          background: "rgb(0, 212, 108)",
                          backgroundImage: "initial",
                          backgroundSize: "initial",
                          height: "unset",
                          display: "none",
                        }}
                      >
                        <a
                          href="##doxazo"
                          target="_blank"
                          style={{
                            boxSizing: "border-box",
                            textDecoration: "none",
                            boxShadow: "none",
                            display: "inline",
                            transition: "all 0.1s ease 0s",
                            color: "rgb(0, 0, 0)",
                            fontWeight: 600,
                            fontFamily: "rubik",
                            fontSize: "13px",
                            outline: "none",
                          }}
                        >
                          View More
                        </a>
                      </button>
                    </div>
                  </div>{" "}
                </div>
              </div>
              <div
                className="elementor-testimonial__footer"
                style={{
                  boxSizing: "border-box",
                  display: "flex",
                  WebkitBoxAlign: "center",
                  alignItems: "center",
                  WebkitBoxPack: "center",
                  justifyContent: "center",
                  marginTop: "0px",
                }}
              />
            </div>
          </div>
          <div
            className="swiper-slide"
            style={{
              boxSizing: "border-box",
              flexShrink: 0,
              position: "relative",
              borderStyle: "solid",
              borderWidth: "0px",
              overflow: "hidden",
              willChange: "transform",
              transitionDuration: "0.5s",
              transitionProperty:
                "border, background, transform, -webkit-transform",
              padding: "0px 35px",
              top: "-100px",
              height: "250px",
              width: "368.667px",
              marginRight: "10px",
            }}
          >
            <div
              className="elementor-testimonial"
              style={{ boxSizing: "border-box", textAlign: "center" }}
            >
              <div
                className="elementor-testimonial__content"
                style={{ boxSizing: "border-box" }}
              >
                <div
                  className="elementor-testimonial__text"
                  style={{
                    boxSizing: "border-box",
                    fontSize: "1.3em",
                    fontStyle: "italic",
                    lineHeight: 1.5,
                    fontFamily: "Rubik, sans-serif",
                  }}
                >
                  <div
                    className="outer_box_content"
                    style={{
                      boxSizing: "border-box",
                      padding: "0px",
                      display: "flex",
                      alignItems: "center",
                      flexDirection: "column",
                    }}
                  >
                    <div
                      className="inner_box_content"
                      style={{
                        boxSizing: "border-box",
                        padding: "0px 20px",
                        display: "flex",
                        alignItems: "center",
                        flexDirection: "column",
                      }}
                    >
                      <div
                        className="background_logo"
                        style={{
                          boxSizing: "border-box",
                          background: "rgb(2, 11, 18)",
                          padding: "12px",
                          borderRadius: "100%",
                          width: "125px",
                          marginBottom: "10px",
                          position: "relative",
                        }}
                      >
                        <Image
                          height={101}
                          width={101}
                          alt="Doxa House"
                          src="https://blockchaintechs.io/wp-content/uploads/2022/11/Doxa-House.webp"
                          style={{
                            userSelect: "none",
                            verticalAlign: "top",
                            boxSizing: "border-box",
                            border: "none",
                            borderRadius: "0px",
                            height: "auto",
                            maxWidth: "100%",
                            boxShadow: "none",
                            objectFit: "contain",
                            width: "262px",
                            outline: "none",
                            pointerEvents: "none",
                          }}
                        />
                      </div>
                      <h3
                        style={{
                          margin: "0px",
                          padding: "0px",
                          overflowWrap: "break-word",
                          boxSizing: "border-box",
                          fontFamily: "rubik",
                          fontWeight: 500,
                          fontStyle: "normal",
                          fontSize: "15px",
                          lineHeight: 1.15,
                          textTransform: "uppercase",
                          marginBottom: "5px",
                          color: "rgb(0, 212, 108)",
                        }}
                      >
                        Social Networking App
                      </h3>
                      <p
                        style={{
                          margin: "0px 0px 18px",
                          padding: "0px",
                          lineHeight: 1.5,
                          boxSizing: "border-box",
                          color: "rgb(235, 235, 235)",
                          fontFamily: "rubik",
                          fontStyle: "normal",
                          fontWeight: 300,
                          fontSize: "14px",
                        }}
                      >
                        Crypto powered real time Audio & Video social networking
                        platform
                      </p>
                      <button
                        style={{
                          outline: "none",
                          padding: "10px 30px",
                          margin: "0px 0px 15px",
                          border: "none",
                          borderRadius: "5px",
                          width: "auto",
                          cursor: "pointer",
                          lineHeight: "23px",
                          fontSize: "12px",
                          fontWeight: 500,
                          textTransform: "uppercase",
                          letterSpacing: "0.2px",
                          color: "rgb(255, 255, 255)",
                          transition: "all 0.4s ease-in-out 0s",
                          boxSizing: "border-box",
                          background: "rgb(0, 212, 108)",
                          backgroundImage: "initial",
                          backgroundSize: "initial",
                          height: "unset",
                          display: "none",
                        }}
                      >
                        <a
                          href="##doxa-house"
                          target="_blank"
                          style={{
                            boxSizing: "border-box",
                            textDecoration: "none",
                            boxShadow: "none",
                            display: "inline",
                            transition: "all 0.1s ease 0s",
                            color: "rgb(0, 0, 0)",
                            fontWeight: 600,
                            fontFamily: "rubik",
                            fontSize: "13px",
                            outline: "none",
                          }}
                        >
                          View More
                        </a>
                      </button>
                    </div>
                  </div>{" "}
                </div>
              </div>
              <div
                className="elementor-testimonial__footer"
                style={{
                  boxSizing: "border-box",
                  display: "flex",
                  WebkitBoxAlign: "center",
                  alignItems: "center",
                  WebkitBoxPack: "center",
                  justifyContent: "center",
                  marginTop: "0px",
                }}
              />
            </div>
          </div>
          <div
            className="swiper-slide"
            style={{
              boxSizing: "border-box",
              flexShrink: 0,
              position: "relative",
              borderStyle: "solid",
              borderWidth: "0px",
              overflow: "hidden",
              willChange: "transform",
              transitionDuration: "0.5s",
              transitionProperty:
                "border, background, transform, -webkit-transform",
              padding: "0px 35px",
              top: "-100px",
              height: "250px",
              width: "368.667px",
              marginRight: "10px",
            }}
          >
            <div
              className="elementor-testimonial"
              style={{ boxSizing: "border-box", textAlign: "center" }}
            >
              <div
                className="elementor-testimonial__content"
                style={{ boxSizing: "border-box" }}
              >
                <div
                  className="elementor-testimonial__text"
                  style={{
                    boxSizing: "border-box",
                    fontSize: "1.3em",
                    fontStyle: "italic",
                    lineHeight: 1.5,
                    fontFamily: "Rubik, sans-serif",
                  }}
                >
                  <div
                    className="outer_box_content"
                    style={{
                      boxSizing: "border-box",
                      padding: "0px",
                      display: "flex",
                      alignItems: "center",
                      flexDirection: "column",
                    }}
                  >
                    <div
                      className="inner_box_content"
                      style={{
                        boxSizing: "border-box",
                        padding: "0px 20px",
                        display: "flex",
                        alignItems: "center",
                        flexDirection: "column",
                      }}
                    >
                      <div
                        className="background_logo"
                        style={{
                          boxSizing: "border-box",
                          background: "rgb(2, 11, 18)",
                          padding: "12px",
                          borderRadius: "100%",
                          width: "125px",
                          marginBottom: "10px",
                          position: "relative",
                        }}
                      >
                        <Image
                          height={101}
                          width={101}
                          alt="Indigena"
                          src="https://blockchaintechs.io/wp-content/uploads/2022/11/Indigena.webp"
                          style={{
                            userSelect: "none",
                            verticalAlign: "top",
                            boxSizing: "border-box",
                            border: "none",
                            borderRadius: "0px",
                            height: "auto",
                            maxWidth: "100%",
                            boxShadow: "none",
                            objectFit: "contain",
                            width: "262px",
                            outline: "none",
                            pointerEvents: "none",
                          }}
                        />
                      </div>
                      <h3
                        style={{
                          margin: "0px",
                          padding: "0px",
                          overflowWrap: "break-word",
                          boxSizing: "border-box",
                          fontFamily: "rubik",
                          fontWeight: 500,
                          fontStyle: "normal",
                          fontSize: "15px",
                          lineHeight: 1.15,
                          textTransform: "uppercase",
                          marginBottom: "5px",
                          color: "rgb(0, 212, 108)",
                        }}
                      >
                        Traditional Art NFT Marketplace
                      </h3>
                      <p
                        style={{
                          margin: "0px 0px 18px",
                          padding: "0px",
                          lineHeight: 1.5,
                          boxSizing: "border-box",
                          color: "rgb(235, 235, 235)",
                          fontFamily: "rubik",
                          fontStyle: "normal",
                          fontWeight: 300,
                          fontSize: "14px",
                        }}
                      >
                        Indigena NFT Marketplace is connecting Indigenous people
                        globally
                      </p>
                      <button
                        style={{
                          outline: "none",
                          padding: "10px 30px",
                          margin: "0px 0px 15px",
                          border: "none",
                          borderRadius: "5px",
                          width: "auto",
                          cursor: "pointer",
                          lineHeight: "23px",
                          fontSize: "12px",
                          fontWeight: 500,
                          textTransform: "uppercase",
                          letterSpacing: "0.2px",
                          color: "rgb(255, 255, 255)",
                          transition: "all 0.4s ease-in-out 0s",
                          boxSizing: "border-box",
                          background: "rgb(0, 212, 108)",
                          backgroundImage: "initial",
                          backgroundSize: "initial",
                          height: "unset",
                          display: "none",
                        }}
                      >
                        <a
                          href="##indigena"
                          target="_blank"
                          style={{
                            boxSizing: "border-box",
                            textDecoration: "none",
                            boxShadow: "none",
                            display: "inline",
                            transition: "all 0.1s ease 0s",
                            color: "rgb(0, 0, 0)",
                            fontWeight: 600,
                            fontFamily: "rubik",
                            fontSize: "13px",
                            outline: "none",
                          }}
                        >
                          View More
                        </a>
                      </button>
                    </div>
                  </div>{" "}
                </div>
              </div>
              <div
                className="elementor-testimonial__footer"
                style={{
                  boxSizing: "border-box",
                  display: "flex",
                  WebkitBoxAlign: "center",
                  alignItems: "center",
                  WebkitBoxPack: "center",
                  justifyContent: "center",
                  marginTop: "0px",
                }}
              />
            </div>
          </div>
          <div
            className="swiper-slide"
            style={{
              boxSizing: "border-box",
              flexShrink: 0,
              position: "relative",
              borderStyle: "solid",
              borderWidth: "0px",
              overflow: "hidden",
              willChange: "transform",
              transitionDuration: "0.5s",
              transitionProperty:
                "border, background, transform, -webkit-transform",
              padding: "0px 35px",
              top: "-100px",
              height: "250px",
              width: "368.667px",
              marginRight: "10px",
            }}
          >
            <div
              className="elementor-testimonial"
              style={{ boxSizing: "border-box", textAlign: "center" }}
            >
              <div
                className="elementor-testimonial__content"
                style={{ boxSizing: "border-box" }}
              >
                <div
                  className="elementor-testimonial__text"
                  style={{
                    boxSizing: "border-box",
                    fontSize: "1.3em",
                    fontStyle: "italic",
                    lineHeight: 1.5,
                    fontFamily: "Rubik, sans-serif",
                  }}
                >
                  <div
                    className="outer_box_content"
                    style={{
                      boxSizing: "border-box",
                      padding: "0px",
                      display: "flex",
                      alignItems: "center",
                      flexDirection: "column",
                    }}
                  >
                    <div
                      className="inner_box_content"
                      style={{
                        boxSizing: "border-box",
                        padding: "0px 20px",
                        display: "flex",
                        alignItems: "center",
                        flexDirection: "column",
                      }}
                    >
                      <div
                        className="background_logo"
                        style={{
                          boxSizing: "border-box",
                          background: "rgb(2, 11, 18)",
                          padding: "12px",
                          borderRadius: "100%",
                          width: "125px",
                          marginBottom: "10px",
                          position: "relative",
                        }}
                      >
                        <Image
                          height={101}
                          width={101}
                          alt="Hive"
                          src="https://blockchaintechs.io/wp-content/uploads/2022/11/Hive.webp"
                          style={{
                            userSelect: "none",
                            verticalAlign: "top",
                            boxSizing: "border-box",
                            border: "none",
                            borderRadius: "0px",
                            height: "auto",
                            maxWidth: "100%",
                            boxShadow: "none",
                            objectFit: "contain",
                            width: "262px",
                            outline: "none",
                            pointerEvents: "none",
                          }}
                        />
                      </div>
                      <h3
                        style={{
                          margin: "0px",
                          padding: "0px",
                          overflowWrap: "break-word",
                          boxSizing: "border-box",
                          fontFamily: "rubik",
                          fontWeight: 500,
                          fontStyle: "normal",
                          fontSize: "15px",
                          lineHeight: 1.15,
                          textTransform: "uppercase",
                          marginBottom: "5px",
                          color: "rgb(0, 212, 108)",
                        }}
                      >
                        NFT Auction Platform
                      </h3>
                      <p
                        style={{
                          margin: "0px 0px 18px",
                          padding: "0px",
                          lineHeight: 1.5,
                          boxSizing: "border-box",
                          color: "rgb(235, 235, 235)",
                          fontFamily: "rubik",
                          fontStyle: "normal",
                          fontWeight: 300,
                          fontSize: "14px",
                        }}
                      >
                        NFT auction platform to raise capital and smart
                        contracts with timed auctions
                      </p>
                      <button
                        style={{
                          outline: "none",
                          padding: "10px 30px",
                          margin: "0px 0px 15px",
                          border: "none",
                          borderRadius: "5px",
                          width: "auto",
                          cursor: "pointer",
                          lineHeight: "23px",
                          fontSize: "12px",
                          fontWeight: 500,
                          textTransform: "uppercase",
                          letterSpacing: "0.2px",
                          color: "rgb(255, 255, 255)",
                          transition: "all 0.4s ease-in-out 0s",
                          boxSizing: "border-box",
                          background: "rgb(0, 212, 108)",
                          backgroundImage: "initial",
                          backgroundSize: "initial",
                          height: "unset",
                          display: "none",
                        }}
                      >
                        <a
                          href="##hive-investments"
                          target="_blank"
                          style={{
                            boxSizing: "border-box",
                            textDecoration: "none",
                            boxShadow: "none",
                            display: "inline",
                            transition: "all 0.1s ease 0s",
                            color: "rgb(0, 0, 0)",
                            fontWeight: 600,
                            fontFamily: "rubik",
                            fontSize: "13px",
                            outline: "none",
                          }}
                        >
                          View More
                        </a>
                      </button>
                    </div>
                  </div>{" "}
                </div>
              </div>
              <div
                className="elementor-testimonial__footer"
                style={{
                  boxSizing: "border-box",
                  display: "flex",
                  WebkitBoxAlign: "center",
                  alignItems: "center",
                  WebkitBoxPack: "center",
                  justifyContent: "center",
                  marginTop: "0px",
                }}
              />
            </div>
          </div>
          <div
            className="swiper-slide swiper-slide-prev"
            style={{
              boxSizing: "border-box",
              flexShrink: 0,
              position: "relative",
              opacity: 0,
              borderStyle: "solid",
              borderWidth: "0px",
              overflow: "hidden",
              willChange: "transform",
              transitionDuration: "0.5s",
              transitionProperty:
                "border, background, transform, -webkit-transform",
              padding: "0px 35px",
              top: "-100px",
              height: "250px",
              width: "368.667px",
              marginRight: "10px",
            }}
          >
            <div
              className="elementor-testimonial"
              style={{ boxSizing: "border-box", textAlign: "center" }}
            >
              <div
                className="elementor-testimonial__content"
                style={{ boxSizing: "border-box" }}
              >
                <div
                  className="elementor-testimonial__text"
                  style={{
                    boxSizing: "border-box",
                    fontSize: "1.3em",
                    fontStyle: "italic",
                    lineHeight: 1.5,
                    fontFamily: "Rubik, sans-serif",
                  }}
                >
                  <div
                    className="outer_box_content"
                    style={{
                      boxSizing: "border-box",
                      padding: "0px",
                      display: "flex",
                      alignItems: "center",
                      flexDirection: "column",
                    }}
                  >
                    <div
                      className="inner_box_content"
                      style={{
                        boxSizing: "border-box",
                        padding: "0px 20px",
                        display: "flex",
                        alignItems: "center",
                        flexDirection: "column",
                      }}
                    >
                      <div
                        className="background_logo"
                        style={{
                          boxSizing: "border-box",
                          background: "rgb(2, 11, 18)",
                          padding: "12px",
                          borderRadius: "100%",
                          width: "125px",
                          marginBottom: "10px",
                          position: "relative",
                        }}
                      >
                        <Image
                          height={101}
                          width={101}
                          alt="Fantompad"
                          src="https://blockchaintechs.io/wp-content/uploads/2022/11/Fantom-PAD.webp"
                          style={{
                            userSelect: "none",
                            verticalAlign: "top",
                            boxSizing: "border-box",
                            border: "none",
                            borderRadius: "0px",
                            height: "auto",
                            maxWidth: "100%",
                            boxShadow: "none",
                            objectFit: "contain",
                            width: "262px",
                            outline: "none",
                            pointerEvents: "none",
                          }}
                        />
                      </div>
                      <h3
                        style={{
                          margin: "0px",
                          padding: "0px",
                          overflowWrap: "break-word",
                          boxSizing: "border-box",
                          fontFamily: "rubik",
                          fontWeight: 500,
                          fontStyle: "normal",
                          fontSize: "15px",
                          lineHeight: 1.15,
                          textTransform: "uppercase",
                          marginBottom: "5px",
                          color: "rgb(0, 212, 108)",
                        }}
                      >
                        Crypto Startup LaunchPad
                      </h3>
                      <p
                        style={{
                          margin: "0px 0px 18px",
                          padding: "0px",
                          lineHeight: 1.5,
                          boxSizing: "border-box",
                          color: "rgb(235, 235, 235)",
                          fontFamily: "rubik",
                          fontStyle: "normal",
                          fontWeight: 300,
                          fontSize: "14px",
                        }}
                      >
                        FantomPAD is the primary launch and listing platform for
                        the Fantom network.{" "}
                      </p>
                      <button
                        style={{
                          outline: "none",
                          padding: "10px 30px",
                          margin: "0px 0px 15px",
                          border: "none",
                          borderRadius: "5px",
                          width: "auto",
                          cursor: "pointer",
                          lineHeight: "23px",
                          fontSize: "12px",
                          fontWeight: 500,
                          textTransform: "uppercase",
                          letterSpacing: "0.2px",
                          color: "rgb(255, 255, 255)",
                          transition: "all 0.4s ease-in-out 0s",
                          boxSizing: "border-box",
                          background: "rgb(0, 212, 108)",
                          backgroundImage: "initial",
                          backgroundSize: "initial",
                          height: "unset",
                          display: "none",
                        }}
                      >
                        <a
                          href="##fantompad"
                          target="_blank"
                          style={{
                            boxSizing: "border-box",
                            textDecoration: "none",
                            boxShadow: "none",
                            display: "inline",
                            transition: "all 0.1s ease 0s",
                            color: "rgb(0, 0, 0)",
                            fontWeight: 600,
                            fontFamily: "rubik",
                            fontSize: "13px",
                            outline: "none",
                          }}
                        >
                          View More
                        </a>
                      </button>
                    </div>
                  </div>{" "}
                </div>
              </div>
              <div
                className="elementor-testimonial__footer"
                style={{
                  boxSizing: "border-box",
                  display: "flex",
                  WebkitBoxAlign: "center",
                  alignItems: "center",
                  WebkitBoxPack: "center",
                  justifyContent: "center",
                  marginTop: "0px",
                }}
              />
            </div>
          </div>
          <div
            className="swiper-slide swiper-slide-active"
            style={{
              boxSizing: "border-box",
              flexShrink: 0,
              position: "relative",
              borderStyle: "solid",
              borderWidth: "0px",
              overflow: "hidden",
              willChange: "transform",
              transitionDuration: "0.5s",
              transitionProperty:
                "border, background, transform, -webkit-transform",
              padding: "0px 35px",
              top: "-100px",
              height: "250px",
              width: "368.667px",
              marginRight: "10px",
            }}
          >
            <div
              className="elementor-testimonial"
              style={{ boxSizing: "border-box", textAlign: "center" }}
            >
              <div
                className="elementor-testimonial__content"
                style={{ boxSizing: "border-box" }}
              >
                <div
                  className="elementor-testimonial__text"
                  style={{
                    boxSizing: "border-box",
                    fontSize: "1.3em",
                    fontStyle: "italic",
                    lineHeight: 1.5,
                    fontFamily: "Rubik, sans-serif",
                  }}
                >
                  <div
                    className="outer_box_content"
                    style={{
                      boxSizing: "border-box",
                      padding: "0px",
                      display: "flex",
                      alignItems: "center",
                      flexDirection: "column",
                    }}
                  >
                    <div
                      className="inner_box_content"
                      style={{
                        boxSizing: "border-box",
                        padding: "0px 20px",
                        display: "flex",
                        alignItems: "center",
                        flexDirection: "column",
                      }}
                    >
                      <div
                        className="background_logo"
                        style={{
                          boxSizing: "border-box",
                          background: "rgb(2, 11, 18)",
                          padding: "12px",
                          borderRadius: "100%",
                          width: "125px",
                          marginBottom: "10px",
                          position: "relative",
                        }}
                      >
                        <Image
                          height={101}
                          width={101}
                          alt="Sustain Coin"
                          src="https://blockchaintechs.io/wp-content/uploads/2022/11/Sustain-Coin.webp"
                          style={{
                            userSelect: "none",
                            verticalAlign: "top",
                            boxSizing: "border-box",
                            border: "none",
                            borderRadius: "0px",
                            height: "auto",
                            maxWidth: "100%",
                            boxShadow: "none",
                            objectFit: "contain",
                            width: "262px",
                            outline: "none",
                            pointerEvents: "none",
                          }}
                        />
                      </div>
                      <h3
                        style={{
                          margin: "0px",
                          padding: "0px",
                          overflowWrap: "break-word",
                          boxSizing: "border-box",
                          fontFamily: "rubik",
                          fontWeight: 500,
                          fontStyle: "normal",
                          fontSize: "15px",
                          lineHeight: 1.15,
                          textTransform: "uppercase",
                          marginBottom: "5px",
                          color: "rgb(0, 212, 108)",
                        }}
                      >
                        Carbon Credit Trading dApp
                      </h3>
                      <p
                        style={{
                          margin: "0px 0px 18px",
                          padding: "0px",
                          lineHeight: 1.5,
                          boxSizing: "border-box",
                          color: "rgb(235, 235, 235)",
                          fontFamily: "rubik",
                          fontStyle: "normal",
                          fontWeight: 300,
                          fontSize: "14px",
                        }}
                      >
                        Tokenize real world carbon credits in to blockchain
                        based crypto tokens
                      </p>
                      <button
                        style={{
                          outline: "none",
                          padding: "10px 30px",
                          margin: "0px 0px 15px",
                          border: "none",
                          borderRadius: "5px",
                          width: "auto",
                          cursor: "pointer",
                          lineHeight: "23px",
                          fontSize: "12px",
                          fontWeight: 500,
                          textTransform: "uppercase",
                          letterSpacing: "0.2px",
                          color: "rgb(255, 255, 255)",
                          transition: "all 0.4s ease-in-out 0s",
                          boxSizing: "border-box",
                          background: "rgb(0, 212, 108)",
                          backgroundImage: "initial",
                          backgroundSize: "initial",
                          height: "unset",
                          display: "none",
                        }}
                      >
                        <a
                          href="##sustain-coin"
                          target="_blank"
                          style={{
                            boxSizing: "border-box",
                            textDecoration: "none",
                            boxShadow: "none",
                            display: "inline",
                            transition: "all 0.1s ease 0s",
                            color: "rgb(0, 0, 0)",
                            fontWeight: 600,
                            fontFamily: "rubik",
                            fontSize: "13px",
                            outline: "none",
                          }}
                        >
                          View More
                        </a>
                      </button>
                    </div>
                  </div>{" "}
                </div>
              </div>
              <div
                className="elementor-testimonial__footer"
                style={{
                  boxSizing: "border-box",
                  display: "flex",
                  WebkitBoxAlign: "center",
                  alignItems: "center",
                  WebkitBoxPack: "center",
                  justifyContent: "center",
                  marginTop: "0px",
                }}
              />
            </div>
          </div>
          <div
            className="swiper-slide swiper-slide-next"
            style={{
              boxSizing: "border-box",
              flexShrink: 0,
              position: "relative",
              borderStyle: "solid",
              borderWidth: "0px",
              willChange: "transform",
              transitionDuration: "0.5s",
              transitionProperty:
                "border, background, transform, -webkit-transform",
              padding: "0px 35px",
              height: "250px",
              background: "rgb(7, 22, 26)",
              borderRadius: "16px",
              overflow: "visible",
              boxShadow: "rgb(0, 212, 108) 0.5px 1px 20px 0.5px",
              top: "-50px",
              width: "368.667px",
              marginRight: "10px",
            }}
          >
            <div
              className="elementor-testimonial"
              style={{ boxSizing: "border-box", textAlign: "center" }}
            >
              <div
                className="elementor-testimonial__content"
                style={{ boxSizing: "border-box" }}
              >
                <div
                  className="elementor-testimonial__text"
                  style={{
                    boxSizing: "border-box",
                    fontSize: "1.3em",
                    fontStyle: "italic",
                    lineHeight: 1.5,
                    fontFamily: "Rubik, sans-serif",
                  }}
                >
                  <div
                    className="outer_box_content"
                    style={{
                      boxSizing: "border-box",
                      padding: "0px",
                      display: "flex",
                      alignItems: "center",
                      flexDirection: "column",
                    }}
                  >
                    <div
                      className="inner_box_content"
                      style={{
                        boxSizing: "border-box",
                        padding: "0px 20px",
                        display: "flex",
                        alignItems: "center",
                        flexDirection: "column",
                        position: "absolute",
                        top: "-95px",
                      }}
                    >
                      <div
                        className="background_logo"
                        style={{
                          boxSizing: "border-box",
                          position: "relative",
                          background: "rgb(2, 11, 18)",
                          padding: "10px",
                          borderRadius: "100%",
                          width: "175px",
                          marginBottom: "20px",
                        }}
                      >
                        <Image
                          height={155}
                          width={155}
                          alt="Metaplexar"
                          src="https://blockchaintechs.io/wp-content/uploads/2022/11/Metaplexar.webp"
                          style={{
                            userSelect: "none",
                            verticalAlign: "top",
                            boxSizing: "border-box",
                            border: "none",
                            borderRadius: "0px",
                            height: "auto",
                            maxWidth: "100%",
                            boxShadow: "none",
                            objectFit: "contain",
                            width: "262px",
                            outline: "none",
                            pointerEvents: "none",
                          }}
                        />
                      </div>
                      <h3
                        style={{
                          margin: "0px",
                          padding: "0px",
                          overflowWrap: "break-word",
                          boxSizing: "border-box",
                          fontFamily: "rubik",
                          fontStyle: "normal",
                          lineHeight: 1.15,
                          textTransform: "uppercase",
                          marginBottom: "5px",
                          color: "rgb(0, 212, 108)",
                          fontSize: "18px",
                          fontWeight: 600,
                        }}
                      >
                        DeFi DAO Protocol
                      </h3>
                      <p
                        style={{
                          margin: "0px 0px 18px",
                          padding: "0px",
                          lineHeight: 1.5,
                          boxSizing: "border-box",
                          color: "rgb(235, 235, 235)",
                          fontFamily: "rubik",
                          fontStyle: "normal",
                          fontWeight: 300,
                          fontSize: "14px",
                        }}
                      >
                        DeFi protocol to make crypto investments more simple and
                        secure
                      </p>
                      <button
                        style={{
                          outline: "none",
                          padding: "10px 30px",
                          margin: "0px 0px 15px",
                          border: "none",
                          borderRadius: "5px",
                          width: "auto",
                          cursor: "pointer",
                          lineHeight: "23px",
                          fontSize: "12px",
                          fontWeight: 500,
                          textTransform: "uppercase",
                          letterSpacing: "0.2px",
                          color: "rgb(255, 255, 255)",
                          transition: "all 0.4s ease-in-out 0s",
                          boxSizing: "border-box",
                          background: "rgb(0, 212, 108)",
                          backgroundImage: "initial",
                          backgroundSize: "initial",
                          height: "unset",
                          display: "block",
                        }}
                      >
                        <a
                          href="##metaplexar-dao-protocol"
                          target="_blank"
                          style={{
                            boxSizing: "border-box",
                            textDecoration: "none",
                            boxShadow: "none",
                            display: "inline",
                            transition: "all 0.1s ease 0s",
                            color: "rgb(0, 0, 0)",
                            fontWeight: 600,
                            fontFamily: "rubik",
                            fontSize: "13px",
                            outline: "none",
                          }}
                        >
                          View More
                        </a>
                      </button>
                    </div>
                  </div>{" "}
                </div>
              </div>
              <div
                className="elementor-testimonial__footer"
                style={{
                  boxSizing: "border-box",
                  display: "flex",
                  WebkitBoxAlign: "center",
                  alignItems: "center",
                  WebkitBoxPack: "center",
                  justifyContent: "center",
                  marginTop: "0px",
                }}
              />
            </div>
          </div>
          <div
            className="swiper-slide"
            style={{
              boxSizing: "border-box",
              flexShrink: 0,
              position: "relative",
              borderStyle: "solid",
              borderWidth: "0px",
              overflow: "hidden",
              willChange: "transform",
              transitionDuration: "0.5s",
              transitionProperty:
                "border, background, transform, -webkit-transform",
              padding: "0px 35px",
              top: "-100px",
              height: "250px",
              width: "368.667px",
              marginRight: "10px",
            }}
          >
            <div
              className="elementor-testimonial"
              style={{ boxSizing: "border-box", textAlign: "center" }}
            >
              <div
                className="elementor-testimonial__content"
                style={{ boxSizing: "border-box" }}
              >
                <div
                  className="elementor-testimonial__text"
                  style={{
                    boxSizing: "border-box",
                    fontSize: "1.3em",
                    fontStyle: "italic",
                    lineHeight: 1.5,
                    fontFamily: "Rubik, sans-serif",
                  }}
                >
                  <div
                    className="outer_box_content"
                    style={{
                      boxSizing: "border-box",
                      padding: "0px",
                      display: "flex",
                      alignItems: "center",
                      flexDirection: "column",
                    }}
                  >
                    <div
                      className="inner_box_content"
                      style={{
                        boxSizing: "border-box",
                        padding: "0px 20px",
                        display: "flex",
                        alignItems: "center",
                        flexDirection: "column",
                      }}
                    >
                      <div
                        className="background_logo"
                        style={{
                          boxSizing: "border-box",
                          background: "rgb(2, 11, 18)",
                          padding: "12px",
                          borderRadius: "100%",
                          width: "125px",
                          marginBottom: "10px",
                          position: "relative",
                        }}
                      >
                        <Image
                          height={101}
                          width={101}
                          alt="pelxp"
                          src="https://blockchaintechs.io/wp-content/uploads/2022/11/PelXP.webp"
                          style={{
                            userSelect: "none",
                            verticalAlign: "top",
                            boxSizing: "border-box",
                            border: "none",
                            borderRadius: "0px",
                            height: "auto",
                            maxWidth: "100%",
                            boxShadow: "none",
                            objectFit: "contain",
                            width: "262px",
                            outline: "none",
                            pointerEvents: "none",
                          }}
                        />
                      </div>
                      <h3
                        style={{
                          margin: "0px",
                          padding: "0px",
                          overflowWrap: "break-word",
                          boxSizing: "border-box",
                          fontFamily: "rubik",
                          fontWeight: 500,
                          fontStyle: "normal",
                          fontSize: "15px",
                          lineHeight: 1.15,
                          textTransform: "uppercase",
                          marginBottom: "5px",
                          color: "rgb(0, 212, 108)",
                        }}
                      >
                        Fractional NFT Marketplace
                      </h3>
                      <p
                        style={{
                          margin: "0px 0px 18px",
                          padding: "0px",
                          lineHeight: 1.5,
                          boxSizing: "border-box",
                          color: "rgb(235, 235, 235)",
                          fontFamily: "rubik",
                          fontStyle: "normal",
                          fontWeight: 300,
                          fontSize: "14px",
                        }}
                      >
                        High value real world arts are converted as pixels to
                        mint NFTs
                      </p>
                      <button
                        style={{
                          outline: "none",
                          padding: "10px 30px",
                          margin: "0px 0px 15px",
                          border: "none",
                          borderRadius: "5px",
                          width: "auto",
                          cursor: "pointer",
                          lineHeight: "23px",
                          fontSize: "12px",
                          fontWeight: 500,
                          textTransform: "uppercase",
                          letterSpacing: "0.2px",
                          color: "rgb(255, 255, 255)",
                          transition: "all 0.4s ease-in-out 0s",
                          boxSizing: "border-box",
                          background: "rgb(0, 212, 108)",
                          backgroundImage: "initial",
                          backgroundSize: "initial",
                          height: "unset",
                          display: "none",
                        }}
                      >
                        <a
                          href="##pelxp"
                          target="_blank"
                          style={{
                            boxSizing: "border-box",
                            textDecoration: "none",
                            boxShadow: "none",
                            display: "inline",
                            transition: "all 0.1s ease 0s",
                            color: "rgb(0, 0, 0)",
                            fontWeight: 600,
                            fontFamily: "rubik",
                            fontSize: "13px",
                            outline: "none",
                          }}
                        >
                          View More
                        </a>
                      </button>
                    </div>
                  </div>{" "}
                </div>
              </div>
              <div
                className="elementor-testimonial__footer"
                style={{
                  boxSizing: "border-box",
                  display: "flex",
                  WebkitBoxAlign: "center",
                  alignItems: "center",
                  WebkitBoxPack: "center",
                  justifyContent: "center",
                  marginTop: "0px",
                }}
              />
            </div>
          </div>
          <div
            className="swiper-slide"
            style={{
              boxSizing: "border-box",
              flexShrink: 0,
              position: "relative",
              borderStyle: "solid",
              borderWidth: "0px",
              overflow: "hidden",
              willChange: "transform",
              transitionDuration: "0.5s",
              transitionProperty:
                "border, background, transform, -webkit-transform",
              padding: "0px 35px",
              top: "-100px",
              height: "250px",
              width: "368.667px",
              marginRight: "10px",
            }}
          >
            <div
              className="elementor-testimonial"
              style={{ boxSizing: "border-box", textAlign: "center" }}
            >
              <div
                className="elementor-testimonial__content"
                style={{ boxSizing: "border-box" }}
              >
                <div
                  className="elementor-testimonial__text"
                  style={{
                    boxSizing: "border-box",
                    fontSize: "1.3em",
                    fontStyle: "italic",
                    lineHeight: 1.5,
                    fontFamily: "Rubik, sans-serif",
                  }}
                >
                  <div
                    className="outer_box_content"
                    style={{
                      boxSizing: "border-box",
                      padding: "0px",
                      display: "flex",
                      alignItems: "center",
                      flexDirection: "column",
                    }}
                  >
                    <div
                      className="inner_box_content"
                      style={{
                        boxSizing: "border-box",
                        padding: "0px 20px",
                        display: "flex",
                        alignItems: "center",
                        flexDirection: "column",
                      }}
                    >
                      <div
                        className="background_logo"
                        style={{
                          boxSizing: "border-box",
                          background: "rgb(2, 11, 18)",
                          padding: "12px",
                          borderRadius: "100%",
                          width: "125px",
                          marginBottom: "10px",
                          position: "relative",
                        }}
                      >
                        <Image
                          height={101}
                          width={101}
                          alt="Uniplay"
                          src="https://blockchaintechs.io/wp-content/uploads/2022/11/Uniplay.webp"
                          style={{
                            userSelect: "none",
                            verticalAlign: "top",
                            boxSizing: "border-box",
                            border: "none",
                            borderRadius: "0px",
                            height: "auto",
                            maxWidth: "100%",
                            boxShadow: "none",
                            objectFit: "contain",
                            width: "262px",
                            outline: "none",
                            pointerEvents: "none",
                          }}
                        />
                      </div>
                      <h3
                        style={{
                          margin: "0px",
                          padding: "0px",
                          overflowWrap: "break-word",
                          boxSizing: "border-box",
                          fontFamily: "rubik",
                          fontWeight: 500,
                          fontStyle: "normal",
                          fontSize: "15px",
                          lineHeight: 1.15,
                          textTransform: "uppercase",
                          marginBottom: "5px",
                          color: "rgb(0, 212, 108)",
                        }}
                      >
                        Play to Earn Game
                      </h3>
                      <p
                        style={{
                          margin: "0px 0px 18px",
                          padding: "0px",
                          lineHeight: 1.5,
                          boxSizing: "border-box",
                          color: "rgb(235, 235, 235)",
                          fontFamily: "rubik",
                          fontStyle: "normal",
                          fontWeight: 300,
                          fontSize: "14px",
                        }}
                      >
                        Players can earn by performing the actions suggested by
                        the creators of the game.
                      </p>
                      <button
                        style={{
                          outline: "none",
                          padding: "10px 30px",
                          margin: "0px 0px 15px",
                          border: "none",
                          borderRadius: "5px",
                          width: "auto",
                          cursor: "pointer",
                          lineHeight: "23px",
                          fontSize: "12px",
                          fontWeight: 500,
                          textTransform: "uppercase",
                          letterSpacing: "0.2px",
                          color: "rgb(255, 255, 255)",
                          transition: "all 0.4s ease-in-out 0s",
                          boxSizing: "border-box",
                          background: "rgb(0, 212, 108)",
                          backgroundImage: "initial",
                          backgroundSize: "initial",
                          height: "unset",
                          display: "none",
                        }}
                      >
                        <a
                          href="##uniplay-p2e"
                          target="_blank"
                          style={{
                            boxSizing: "border-box",
                            textDecoration: "none",
                            boxShadow: "none",
                            display: "inline",
                            transition: "all 0.1s ease 0s",
                            color: "rgb(0, 0, 0)",
                            fontWeight: 600,
                            fontFamily: "rubik",
                            fontSize: "13px",
                            outline: "none",
                          }}
                        >
                          View More
                        </a>
                      </button>
                    </div>
                  </div>{" "}
                </div>
              </div>
              <div
                className="elementor-testimonial__footer"
                style={{
                  boxSizing: "border-box",
                  display: "flex",
                  WebkitBoxAlign: "center",
                  alignItems: "center",
                  WebkitBoxPack: "center",
                  justifyContent: "center",
                  marginTop: "0px",
                }}
              />
            </div>
          </div>
          <div
            className="swiper-slide"
            style={{
              boxSizing: "border-box",
              flexShrink: 0,
              position: "relative",
              borderStyle: "solid",
              borderWidth: "0px",
              overflow: "hidden",
              willChange: "transform",
              transitionDuration: "0.5s",
              transitionProperty:
                "border, background, transform, -webkit-transform",
              padding: "0px 35px",
              top: "-100px",
              height: "250px",
              width: "368.667px",
              marginRight: "10px",
            }}
          >
            <div
              className="elementor-testimonial"
              style={{ boxSizing: "border-box", textAlign: "center" }}
            >
              <div
                className="elementor-testimonial__content"
                style={{ boxSizing: "border-box" }}
              >
                <div
                  className="elementor-testimonial__text"
                  style={{
                    boxSizing: "border-box",
                    fontSize: "1.3em",
                    fontStyle: "italic",
                    lineHeight: 1.5,
                    fontFamily: "Rubik, sans-serif",
                  }}
                >
                  <div
                    className="outer_box_content"
                    style={{
                      boxSizing: "border-box",
                      padding: "0px",
                      display: "flex",
                      alignItems: "center",
                      flexDirection: "column",
                    }}
                  >
                    <div
                      className="inner_box_content"
                      style={{
                        boxSizing: "border-box",
                        padding: "0px 20px",
                        display: "flex",
                        alignItems: "center",
                        flexDirection: "column",
                      }}
                    >
                      <div
                        className="background_logo"
                        style={{
                          boxSizing: "border-box",
                          background: "rgb(2, 11, 18)",
                          padding: "12px",
                          borderRadius: "100%",
                          width: "125px",
                          marginBottom: "10px",
                          position: "relative",
                        }}
                      >
                        <Image
                          height={101}
                          width={101}
                          alt="Cobe"
                          src="https://blockchaintechs.io/wp-content/uploads/2022/11/Cobe.webp"
                          style={{
                            userSelect: "none",
                            verticalAlign: "top",
                            boxSizing: "border-box",
                            border: "none",
                            borderRadius: "0px",
                            height: "auto",
                            maxWidth: "100%",
                            boxShadow: "none",
                            objectFit: "contain",
                            width: "262px",
                            outline: "none",
                            pointerEvents: "none",
                          }}
                        />
                      </div>
                      <h3
                        style={{
                          margin: "0px",
                          padding: "0px",
                          overflowWrap: "break-word",
                          boxSizing: "border-box",
                          fontFamily: "rubik",
                          fontWeight: 500,
                          fontStyle: "normal",
                          fontSize: "15px",
                          lineHeight: 1.15,
                          textTransform: "uppercase",
                          marginBottom: "5px",
                          color: "rgb(0, 212, 108)",
                        }}
                      >
                        Cross Border Payment Platform
                      </h3>
                      <p
                        style={{
                          margin: "0px 0px 18px",
                          padding: "0px",
                          lineHeight: 1.5,
                          boxSizing: "border-box",
                          color: "rgb(235, 235, 235)",
                          fontFamily: "rubik",
                          fontStyle: "normal",
                          fontWeight: 300,
                          fontSize: "14px",
                        }}
                      >
                        CCross-border Payment platform for both buyers and
                        sellers with Escrow
                      </p>
                      <button
                        style={{
                          outline: "none",
                          padding: "10px 30px",
                          margin: "0px 0px 15px",
                          border: "none",
                          borderRadius: "5px",
                          width: "auto",
                          cursor: "pointer",
                          lineHeight: "23px",
                          fontSize: "12px",
                          fontWeight: 500,
                          textTransform: "uppercase",
                          letterSpacing: "0.2px",
                          color: "rgb(255, 255, 255)",
                          transition: "all 0.4s ease-in-out 0s",
                          boxSizing: "border-box",
                          background: "rgb(0, 212, 108)",
                          backgroundImage: "initial",
                          backgroundSize: "initial",
                          height: "unset",
                          display: "none",
                        }}
                      >
                        <a
                          href="##cobe"
                          target="_blank"
                          style={{
                            boxSizing: "border-box",
                            textDecoration: "none",
                            boxShadow: "none",
                            display: "inline",
                            transition: "all 0.1s ease 0s",
                            color: "rgb(0, 0, 0)",
                            fontWeight: 600,
                            fontFamily: "rubik",
                            fontSize: "13px",
                            outline: "none",
                          }}
                        >
                          View More
                        </a>
                      </button>
                    </div>
                  </div>{" "}
                </div>
              </div>
              <div
                className="elementor-testimonial__footer"
                style={{
                  boxSizing: "border-box",
                  display: "flex",
                  WebkitBoxAlign: "center",
                  alignItems: "center",
                  WebkitBoxPack: "center",
                  justifyContent: "center",
                  marginTop: "0px",
                }}
              />
            </div>
          </div>
          <div
            className="swiper-slide"
            style={{
              boxSizing: "border-box",
              flexShrink: 0,
              position: "relative",
              borderStyle: "solid",
              borderWidth: "0px",
              overflow: "hidden",
              willChange: "transform",
              transitionDuration: "0.5s",
              transitionProperty:
                "border, background, transform, -webkit-transform",
              padding: "0px 35px",
              top: "-100px",
              height: "250px",
              width: "368.667px",
              marginRight: "10px",
            }}
          >
            <div
              className="elementor-testimonial"
              style={{ boxSizing: "border-box", textAlign: "center" }}
            >
              <div
                className="elementor-testimonial__content"
                style={{ boxSizing: "border-box" }}
              >
                <div
                  className="elementor-testimonial__text"
                  style={{
                    boxSizing: "border-box",
                    fontSize: "1.3em",
                    fontStyle: "italic",
                    lineHeight: 1.5,
                    fontFamily: "Rubik, sans-serif",
                  }}
                >
                  <div
                    className="outer_box_content"
                    style={{
                      boxSizing: "border-box",
                      padding: "0px",
                      display: "flex",
                      alignItems: "center",
                      flexDirection: "column",
                    }}
                  >
                    <div
                      className="inner_box_content"
                      style={{
                        boxSizing: "border-box",
                        padding: "0px 20px",
                        display: "flex",
                        alignItems: "center",
                        flexDirection: "column",
                      }}
                    >
                      <div
                        className="background_logo"
                        style={{
                          boxSizing: "border-box",
                          background: "rgb(2, 11, 18)",
                          padding: "12px",
                          borderRadius: "100%",
                          width: "125px",
                          marginBottom: "10px",
                          position: "relative",
                        }}
                      >
                        <Image
                          height={101}
                          width={101}
                          alt="Fayre"
                          src="https://blockchaintechs.io/wp-content/uploads/2022/11/Fayre.webp"
                          style={{
                            userSelect: "none",
                            verticalAlign: "top",
                            boxSizing: "border-box",
                            border: "none",
                            borderRadius: "0px",
                            height: "auto",
                            maxWidth: "100%",
                            boxShadow: "none",
                            objectFit: "contain",
                            width: "262px",
                            outline: "none",
                            pointerEvents: "none",
                          }}
                        />
                      </div>
                      <h3
                        style={{
                          margin: "0px",
                          padding: "0px",
                          overflowWrap: "break-word",
                          boxSizing: "border-box",
                          fontFamily: "rubik",
                          fontWeight: 500,
                          fontStyle: "normal",
                          fontSize: "15px",
                          lineHeight: 1.15,
                          textTransform: "uppercase",
                          marginBottom: "5px",
                          color: "rgb(0, 212, 108)",
                        }}
                      >
                        Brands & Fans NFT Marketplace
                      </h3>
                      <p
                        style={{
                          margin: "0px 0px 18px",
                          padding: "0px",
                          lineHeight: 1.5,
                          boxSizing: "border-box",
                          color: "rgb(235, 235, 235)",
                          fontFamily: "rubik",
                          fontStyle: "normal",
                          fontWeight: 300,
                          fontSize: "14px",
                        }}
                      >
                        Fayre provides a comprehensive suite of tools for brands
                        to participate
                      </p>
                      <button
                        style={{
                          outline: "none",
                          padding: "10px 30px",
                          margin: "0px 0px 15px",
                          border: "none",
                          borderRadius: "5px",
                          width: "auto",
                          cursor: "pointer",
                          lineHeight: "23px",
                          fontSize: "12px",
                          fontWeight: 500,
                          textTransform: "uppercase",
                          letterSpacing: "0.2px",
                          color: "rgb(255, 255, 255)",
                          transition: "all 0.4s ease-in-out 0s",
                          boxSizing: "border-box",
                          background: "rgb(0, 212, 108)",
                          backgroundImage: "initial",
                          backgroundSize: "initial",
                          height: "unset",
                          display: "none",
                        }}
                      >
                        <a
                          href="##fayre"
                          target="_blank"
                          style={{
                            boxSizing: "border-box",
                            textDecoration: "none",
                            boxShadow: "none",
                            display: "inline",
                            transition: "all 0.1s ease 0s",
                            color: "rgb(0, 0, 0)",
                            fontWeight: 600,
                            fontFamily: "rubik",
                            fontSize: "13px",
                            outline: "none",
                          }}
                        >
                          View More
                        </a>
                      </button>
                    </div>
                  </div>{" "}
                </div>
              </div>
              <div
                className="elementor-testimonial__footer"
                style={{
                  boxSizing: "border-box",
                  display: "flex",
                  WebkitBoxAlign: "center",
                  alignItems: "center",
                  WebkitBoxPack: "center",
                  justifyContent: "center",
                  marginTop: "0px",
                }}
              />
            </div>
          </div>
          <div
            className="swiper-slide"
            style={{
              boxSizing: "border-box",
              flexShrink: 0,
              position: "relative",
              borderStyle: "solid",
              borderWidth: "0px",
              overflow: "hidden",
              willChange: "transform",
              transitionDuration: "0.5s",
              transitionProperty:
                "border, background, transform, -webkit-transform",
              padding: "0px 35px",
              top: "-100px",
              height: "250px",
              width: "368.667px",
              marginRight: "10px",
            }}
          >
            <div
              className="elementor-testimonial"
              style={{ boxSizing: "border-box", textAlign: "center" }}
            >
              <div
                className="elementor-testimonial__content"
                style={{ boxSizing: "border-box" }}
              >
                <div
                  className="elementor-testimonial__text"
                  style={{
                    boxSizing: "border-box",
                    fontSize: "1.3em",
                    fontStyle: "italic",
                    lineHeight: 1.5,
                    fontFamily: "Rubik, sans-serif",
                  }}
                >
                  <div
                    className="outer_box_content"
                    style={{
                      boxSizing: "border-box",
                      padding: "0px",
                      display: "flex",
                      alignItems: "center",
                      flexDirection: "column",
                    }}
                  >
                    <div
                      className="inner_box_content"
                      style={{
                        boxSizing: "border-box",
                        padding: "0px 20px",
                        display: "flex",
                        alignItems: "center",
                        flexDirection: "column",
                      }}
                    >
                      <div
                        className="background_logo"
                        style={{
                          boxSizing: "border-box",
                          background: "rgb(2, 11, 18)",
                          padding: "12px",
                          borderRadius: "100%",
                          width: "125px",
                          marginBottom: "10px",
                          position: "relative",
                        }}
                      >
                        <Image
                          height={101}
                          width={101}
                          alt="Stakinggocity"
                          src="https://blockchaintechs.io/wp-content/uploads/2022/08/Stakingocity-1.webp"
                          style={{
                            userSelect: "none",
                            verticalAlign: "top",
                            boxSizing: "border-box",
                            border: "none",
                            borderRadius: "0px",
                            height: "auto",
                            maxWidth: "100%",
                            boxShadow: "none",
                            objectFit: "contain",
                            width: "262px",
                            outline: "none",
                            pointerEvents: "none",
                          }}
                        />
                      </div>
                      <h3
                        style={{
                          margin: "0px",
                          padding: "0px",
                          overflowWrap: "break-word",
                          boxSizing: "border-box",
                          fontFamily: "rubik",
                          fontWeight: 500,
                          fontStyle: "normal",
                          fontSize: "15px",
                          lineHeight: 1.15,
                          textTransform: "uppercase",
                          marginBottom: "5px",
                          color: "rgb(0, 212, 108)",
                        }}
                      >
                        NFT Staking Platform
                      </h3>
                      <p
                        style={{
                          margin: "0px 0px 18px",
                          padding: "0px",
                          lineHeight: 1.5,
                          boxSizing: "border-box",
                          color: "rgb(235, 235, 235)",
                          fontFamily: "rubik",
                          fontStyle: "normal",
                          fontWeight: 300,
                          fontSize: "14px",
                        }}
                      >
                        NFT staking platform Individuals can earn rewards based
                        on the Annual Percentage Yield (APY)
                      </p>
                      <button
                        style={{
                          outline: "none",
                          padding: "10px 30px",
                          margin: "0px 0px 15px",
                          border: "none",
                          borderRadius: "5px",
                          width: "auto",
                          cursor: "pointer",
                          lineHeight: "23px",
                          fontSize: "12px",
                          fontWeight: 500,
                          textTransform: "uppercase",
                          letterSpacing: "0.2px",
                          color: "rgb(255, 255, 255)",
                          transition: "all 0.4s ease-in-out 0s",
                          boxSizing: "border-box",
                          background: "rgb(0, 212, 108)",
                          backgroundImage: "initial",
                          backgroundSize: "initial",
                          height: "unset",
                          display: "none",
                        }}
                      >
                        <a
                          href="##stakingocity"
                          target="_blank"
                          style={{
                            boxSizing: "border-box",
                            textDecoration: "none",
                            boxShadow: "none",
                            display: "inline",
                            transition: "all 0.1s ease 0s",
                            color: "rgb(0, 0, 0)",
                            fontWeight: 600,
                            fontFamily: "rubik",
                            fontSize: "13px",
                            outline: "none",
                          }}
                        >
                          View More
                        </a>
                      </button>
                    </div>
                  </div>{" "}
                </div>
              </div>
              <div
                className="elementor-testimonial__footer"
                style={{
                  boxSizing: "border-box",
                  display: "flex",
                  WebkitBoxAlign: "center",
                  alignItems: "center",
                  WebkitBoxPack: "center",
                  justifyContent: "center",
                  marginTop: "0px",
                }}
              />
            </div>
          </div>
          <div
            className="swiper-slide"
            style={{
              boxSizing: "border-box",
              flexShrink: 0,
              position: "relative",
              borderStyle: "solid",
              borderWidth: "0px",
              overflow: "hidden",
              willChange: "transform",
              transitionDuration: "0.5s",
              transitionProperty:
                "border, background, transform, -webkit-transform",
              padding: "0px 35px",
              top: "-100px",
              height: "250px",
              width: "368.667px",
              marginRight: "10px",
            }}
          >
            <div
              className="elementor-testimonial"
              style={{ boxSizing: "border-box", textAlign: "center" }}
            >
              <div
                className="elementor-testimonial__content"
                style={{ boxSizing: "border-box" }}
              >
                <div
                  className="elementor-testimonial__text"
                  style={{
                    boxSizing: "border-box",
                    fontSize: "1.3em",
                    fontStyle: "italic",
                    lineHeight: 1.5,
                    fontFamily: "Rubik, sans-serif",
                  }}
                >
                  <div
                    className="outer_box_content"
                    style={{
                      boxSizing: "border-box",
                      padding: "0px",
                      display: "flex",
                      alignItems: "center",
                      flexDirection: "column",
                    }}
                  >
                    <div
                      className="inner_box_content"
                      style={{
                        boxSizing: "border-box",
                        padding: "0px 20px",
                        display: "flex",
                        alignItems: "center",
                        flexDirection: "column",
                      }}
                    >
                      <div
                        className="background_logo"
                        style={{
                          boxSizing: "border-box",
                          background: "rgb(2, 11, 18)",
                          padding: "12px",
                          borderRadius: "100%",
                          width: "125px",
                          marginBottom: "10px",
                          position: "relative",
                        }}
                      >
                        <Image
                          height={101}
                          width={101}
                          alt="Lendspot"
                          src="https://blockchaintechs.io/wp-content/uploads/2022/11/Lendspot.webp"
                          style={{
                            userSelect: "none",
                            verticalAlign: "top",
                            boxSizing: "border-box",
                            border: "none",
                            borderRadius: "0px",
                            height: "auto",
                            maxWidth: "100%",
                            boxShadow: "none",
                            objectFit: "contain",
                            width: "262px",
                            outline: "none",
                            pointerEvents: "none",
                          }}
                        />
                      </div>
                      <h3
                        style={{
                          margin: "0px",
                          padding: "0px",
                          overflowWrap: "break-word",
                          boxSizing: "border-box",
                          fontFamily: "rubik",
                          fontWeight: 500,
                          fontStyle: "normal",
                          fontSize: "15px",
                          lineHeight: 1.15,
                          textTransform: "uppercase",
                          marginBottom: "5px",
                          color: "rgb(0, 212, 108)",
                        }}
                      >
                        NFT Lending & Borrowing
                      </h3>
                      <p
                        style={{
                          margin: "0px 0px 18px",
                          padding: "0px",
                          lineHeight: 1.5,
                          boxSizing: "border-box",
                          color: "rgb(235, 235, 235)",
                          fontFamily: "rubik",
                          fontStyle: "normal",
                          fontWeight: 300,
                          fontSize: "14px",
                        }}
                      >
                        Get crypto loans against NFTs and lend crypto to
                        generate additional income.
                      </p>
                      <button
                        style={{
                          outline: "none",
                          padding: "10px 30px",
                          margin: "0px 0px 15px",
                          border: "none",
                          borderRadius: "5px",
                          width: "auto",
                          cursor: "pointer",
                          lineHeight: "23px",
                          fontSize: "12px",
                          fontWeight: 500,
                          textTransform: "uppercase",
                          letterSpacing: "0.2px",
                          color: "rgb(255, 255, 255)",
                          transition: "all 0.4s ease-in-out 0s",
                          boxSizing: "border-box",
                          background: "rgb(0, 212, 108)",
                          backgroundImage: "initial",
                          backgroundSize: "initial",
                          height: "unset",
                          display: "none",
                        }}
                      >
                        <a
                          href="##lendspot"
                          target="_blank"
                          style={{
                            boxSizing: "border-box",
                            textDecoration: "none",
                            boxShadow: "none",
                            display: "inline",
                            transition: "all 0.1s ease 0s",
                            color: "rgb(0, 0, 0)",
                            fontWeight: 600,
                            fontFamily: "rubik",
                            fontSize: "13px",
                            outline: "none",
                          }}
                        >
                          View More
                        </a>
                      </button>
                    </div>
                  </div>{" "}
                </div>
              </div>
              <div
                className="elementor-testimonial__footer"
                style={{
                  boxSizing: "border-box",
                  display: "flex",
                  WebkitBoxAlign: "center",
                  alignItems: "center",
                  WebkitBoxPack: "center",
                  justifyContent: "center",
                  marginTop: "0px",
                }}
              />
            </div>
          </div>
          <div
            className="swiper-slide"
            style={{
              boxSizing: "border-box",
              flexShrink: 0,
              position: "relative",
              borderStyle: "solid",
              borderWidth: "0px",
              overflow: "hidden",
              willChange: "transform",
              transitionDuration: "0.5s",
              transitionProperty:
                "border, background, transform, -webkit-transform",
              padding: "0px 35px",
              top: "-100px",
              height: "250px",
              width: "368.667px",
              marginRight: "10px",
            }}
          >
            <div
              className="elementor-testimonial"
              style={{ boxSizing: "border-box", textAlign: "center" }}
            >
              <div
                className="elementor-testimonial__content"
                style={{ boxSizing: "border-box" }}
              >
                <div
                  className="elementor-testimonial__text"
                  style={{
                    boxSizing: "border-box",
                    fontSize: "1.3em",
                    fontStyle: "italic",
                    lineHeight: 1.5,
                    fontFamily: "Rubik, sans-serif",
                  }}
                >
                  <div
                    className="outer_box_content"
                    style={{
                      boxSizing: "border-box",
                      padding: "0px",
                      display: "flex",
                      alignItems: "center",
                      flexDirection: "column",
                    }}
                  >
                    <div
                      className="inner_box_content"
                      style={{
                        boxSizing: "border-box",
                        padding: "0px 20px",
                        display: "flex",
                        alignItems: "center",
                        flexDirection: "column",
                      }}
                    >
                      <div
                        className="background_logo"
                        style={{
                          boxSizing: "border-box",
                          background: "rgb(2, 11, 18)",
                          padding: "12px",
                          borderRadius: "100%",
                          width: "125px",
                          marginBottom: "10px",
                          position: "relative",
                        }}
                      >
                        <Image
                          height={101}
                          width={101}
                          alt="Expedited"
                          src="https://blockchaintechs.io/wp-content/uploads/2022/11/ExpeditedRound.webp"
                          style={{
                            userSelect: "none",
                            verticalAlign: "top",
                            boxSizing: "border-box",
                            border: "none",
                            borderRadius: "0px",
                            height: "auto",
                            maxWidth: "100%",
                            boxShadow: "none",
                            objectFit: "contain",
                            width: "262px",
                            outline: "none",
                            pointerEvents: "none",
                          }}
                        />
                      </div>
                      <h3
                        style={{
                          margin: "0px",
                          padding: "0px",
                          overflowWrap: "break-word",
                          boxSizing: "border-box",
                          fontFamily: "rubik",
                          fontWeight: 500,
                          fontStyle: "normal",
                          fontSize: "15px",
                          lineHeight: 1.15,
                          textTransform: "uppercase",
                          marginBottom: "5px",
                          color: "rgb(0, 212, 108)",
                        }}
                      >
                        Recruitment Platform
                      </h3>
                      <p
                        style={{
                          margin: "0px 0px 18px",
                          padding: "0px",
                          lineHeight: 1.5,
                          boxSizing: "border-box",
                          color: "rgb(235, 235, 235)",
                          fontFamily: "rubik",
                          fontStyle: "normal",
                          fontWeight: 300,
                          fontSize: "14px",
                        }}
                      >
                        New Gen Recruitement Platform with Candidates and
                        Recruiters
                      </p>
                      <button
                        style={{
                          outline: "none",
                          padding: "10px 30px",
                          margin: "0px 0px 15px",
                          border: "none",
                          borderRadius: "5px",
                          width: "auto",
                          cursor: "pointer",
                          lineHeight: "23px",
                          fontSize: "12px",
                          fontWeight: 500,
                          textTransform: "uppercase",
                          letterSpacing: "0.2px",
                          color: "rgb(255, 255, 255)",
                          transition: "all 0.4s ease-in-out 0s",
                          boxSizing: "border-box",
                          background: "rgb(0, 212, 108)",
                          backgroundImage: "initial",
                          backgroundSize: "initial",
                          height: "unset",
                          display: "none",
                        }}
                      >
                        <a
                          href="#"
                          target="_blank"
                          style={{
                            boxSizing: "border-box",
                            textDecoration: "none",
                            boxShadow: "none",
                            display: "inline",
                            transition: "all 0.1s ease 0s",
                            color: "rgb(0, 0, 0)",
                            fontWeight: 600,
                            fontFamily: "rubik",
                            fontSize: "13px",
                            outline: "none",
                          }}
                        >
                          View More
                        </a>
                      </button>
                    </div>
                  </div>{" "}
                </div>
              </div>
              <div
                className="elementor-testimonial__footer"
                style={{
                  boxSizing: "border-box",
                  display: "flex",
                  WebkitBoxAlign: "center",
                  alignItems: "center",
                  WebkitBoxPack: "center",
                  justifyContent: "center",
                  marginTop: "0px",
                }}
              />
            </div>
          </div>
          <div
            className="swiper-slide swiper-slide-duplicate"
            style={{
              boxSizing: "border-box",
              flexShrink: 0,
              position: "relative",
              borderStyle: "solid",
              borderWidth: "0px",
              overflow: "hidden",
              willChange: "transform",
              transitionDuration: "0.5s",
              transitionProperty:
                "border, background, transform, -webkit-transform",
              padding: "0px 35px",
              top: "-100px",
              height: "250px",
              width: "368.667px",
              marginRight: "10px",
            }}
          >
            <div
              className="elementor-testimonial"
              style={{ boxSizing: "border-box", textAlign: "center" }}
            >
              <div
                className="elementor-testimonial__content"
                style={{ boxSizing: "border-box" }}
              >
                <div
                  className="elementor-testimonial__text"
                  style={{
                    boxSizing: "border-box",
                    fontSize: "1.3em",
                    fontStyle: "italic",
                    lineHeight: 1.5,
                    fontFamily: "Rubik, sans-serif",
                  }}
                >
                  <div
                    className="outer_box_content"
                    style={{
                      boxSizing: "border-box",
                      padding: "0px",
                      display: "flex",
                      alignItems: "center",
                      flexDirection: "column",
                    }}
                  >
                    <div
                      className="inner_box_content"
                      style={{
                        boxSizing: "border-box",
                        padding: "0px 20px",
                        display: "flex",
                        alignItems: "center",
                        flexDirection: "column",
                      }}
                    >
                      <div
                        className="background_logo"
                        style={{
                          boxSizing: "border-box",
                          background: "rgb(2, 11, 18)",
                          padding: "12px",
                          borderRadius: "100%",
                          width: "125px",
                          marginBottom: "10px",
                          position: "relative",
                        }}
                      >
                        <Image
                          height={101}
                          width={101}
                          alt="Lockness"
                          src="https://blockchaintechs.io/wp-content/uploads/2022/11/Lockness.webp"
                          style={{
                            userSelect: "none",
                            verticalAlign: "top",
                            boxSizing: "border-box",
                            border: "none",
                            borderRadius: "0px",
                            height: "auto",
                            maxWidth: "100%",
                            boxShadow: "none",
                            objectFit: "contain",
                            width: "262px",
                            outline: "none",
                            pointerEvents: "none",
                          }}
                        />
                      </div>
                      <h3
                        style={{
                          margin: "0px",
                          padding: "0px",
                          overflowWrap: "break-word",
                          boxSizing: "border-box",
                          fontFamily: "rubik",
                          fontWeight: 500,
                          fontStyle: "normal",
                          fontSize: "15px",
                          lineHeight: 1.15,
                          textTransform: "uppercase",
                          marginBottom: "5px",
                          color: "rgb(0, 212, 108)",
                        }}
                      >
                        Escrow Payment Platform
                      </h3>
                      <p
                        style={{
                          margin: "0px 0px 18px",
                          padding: "0px",
                          lineHeight: 1.5,
                          boxSizing: "border-box",
                          color: "rgb(235, 235, 235)",
                          fontFamily: "rubik",
                          fontStyle: "normal",
                          fontWeight: 300,
                          fontSize: "14px",
                        }}
                      >
                        The PayPal of crypto aims on providing a safe and secure
                        P2P Payment
                      </p>
                      <button
                        style={{
                          outline: "none",
                          padding: "10px 30px",
                          margin: "0px 0px 15px",
                          border: "none",
                          borderRadius: "5px",
                          width: "auto",
                          cursor: "pointer",
                          lineHeight: "23px",
                          fontSize: "12px",
                          fontWeight: 500,
                          textTransform: "uppercase",
                          letterSpacing: "0.2px",
                          color: "rgb(255, 255, 255)",
                          transition: "all 0.4s ease-in-out 0s",
                          boxSizing: "border-box",
                          background: "rgb(0, 212, 108)",
                          backgroundImage: "initial",
                          backgroundSize: "initial",
                          height: "unset",
                          display: "none",
                        }}
                      >
                        <a
                          href="#"
                          target="_blank"
                          style={{
                            boxSizing: "border-box",
                            textDecoration: "none",
                            boxShadow: "none",
                            display: "inline",
                            transition: "all 0.1s ease 0s",
                            color: "rgb(0, 0, 0)",
                            fontWeight: 600,
                            fontFamily: "rubik",
                            fontSize: "13px",
                            outline: "none",
                          }}
                        >
                          See More
                        </a>
                      </button>
                    </div>
                  </div>{" "}
                </div>
              </div>
              <div
                className="elementor-testimonial__footer"
                style={{
                  boxSizing: "border-box",
                  display: "flex",
                  WebkitBoxAlign: "center",
                  alignItems: "center",
                  WebkitBoxPack: "center",
                  justifyContent: "center",
                  marginTop: "0px",
                }}
              />
            </div>
          </div>
          <div
            className="swiper-slide swiper-slide-duplicate"
            style={{
              boxSizing: "border-box",
              flexShrink: 0,
              position: "relative",
              borderStyle: "solid",
              borderWidth: "0px",
              overflow: "hidden",
              willChange: "transform",
              transitionDuration: "0.5s",
              transitionProperty:
                "border, background, transform, -webkit-transform",
              padding: "0px 35px",
              top: "-100px",
              height: "250px",
              width: "368.667px",
              marginRight: "10px",
            }}
          >
            <div
              className="elementor-testimonial"
              style={{ boxSizing: "border-box", textAlign: "center" }}
            >
              <div
                className="elementor-testimonial__content"
                style={{ boxSizing: "border-box" }}
              >
                <div
                  className="elementor-testimonial__text"
                  style={{
                    boxSizing: "border-box",
                    fontSize: "1.3em",
                    fontStyle: "italic",
                    lineHeight: 1.5,
                    fontFamily: "Rubik, sans-serif",
                  }}
                >
                  <div
                    className="outer_box_content"
                    style={{
                      boxSizing: "border-box",
                      padding: "0px",
                      display: "flex",
                      alignItems: "center",
                      flexDirection: "column",
                    }}
                  >
                    <div
                      className="inner_box_content"
                      style={{
                        boxSizing: "border-box",
                        padding: "0px 20px",
                        display: "flex",
                        alignItems: "center",
                        flexDirection: "column",
                      }}
                    >
                      <div
                        className="background_logo"
                        style={{
                          boxSizing: "border-box",
                          background: "rgb(2, 11, 18)",
                          padding: "12px",
                          borderRadius: "100%",
                          width: "125px",
                          marginBottom: "10px",
                          position: "relative",
                        }}
                      >
                        <Image
                          height={101}
                          width={101}
                          alt="Metabloqs"
                          src="https://blockchaintechs.io/wp-content/uploads/2022/08/Metabloqs.webp"
                          style={{
                            userSelect: "none",
                            verticalAlign: "top",
                            boxSizing: "border-box",
                            border: "none",
                            borderRadius: "0px",
                            height: "auto",
                            maxWidth: "100%",
                            boxShadow: "none",
                            objectFit: "contain",
                            width: "262px",
                            outline: "none",
                            pointerEvents: "none",
                          }}
                        />
                      </div>
                      <h3
                        style={{
                          margin: "0px",
                          padding: "0px",
                          overflowWrap: "break-word",
                          boxSizing: "border-box",
                          fontFamily: "rubik",
                          fontWeight: 500,
                          fontStyle: "normal",
                          fontSize: "15px",
                          lineHeight: 1.15,
                          textTransform: "uppercase",
                          marginBottom: "5px",
                          color: "rgb(0, 212, 108)",
                        }}
                      >
                        Metaverse Ecosystem
                      </h3>
                      <p
                        style={{
                          margin: "0px 0px 18px",
                          padding: "0px",
                          lineHeight: 1.5,
                          boxSizing: "border-box",
                          color: "rgb(235, 235, 235)",
                          fontFamily: "rubik",
                          fontStyle: "normal",
                          fontWeight: 300,
                          fontSize: "14px",
                        }}
                      >
                        Metabloqs will serve as the canvas for a metaverse that
                        is grounded in reality
                      </p>
                      <button
                        style={{
                          outline: "none",
                          padding: "10px 30px",
                          margin: "0px 0px 15px",
                          border: "none",
                          borderRadius: "5px",
                          width: "auto",
                          cursor: "pointer",
                          lineHeight: "23px",
                          fontSize: "12px",
                          fontWeight: 500,
                          textTransform: "uppercase",
                          letterSpacing: "0.2px",
                          color: "rgb(255, 255, 255)",
                          transition: "all 0.4s ease-in-out 0s",
                          boxSizing: "border-box",
                          background: "rgb(0, 212, 108)",
                          backgroundImage: "initial",
                          backgroundSize: "initial",
                          height: "unset",
                          display: "none",
                        }}
                      >
                        <a
                          href="#"
                          target="_blank"
                          style={{
                            boxSizing: "border-box",
                            textDecoration: "none",
                            boxShadow: "none",
                            display: "inline",
                            transition: "all 0.1s ease 0s",
                            color: "rgb(0, 0, 0)",
                            fontWeight: 600,
                            fontFamily: "rubik",
                            fontSize: "13px",
                            outline: "none",
                          }}
                        >
                          View More
                        </a>
                      </button>
                    </div>
                  </div>{" "}
                </div>
              </div>
              <div
                className="elementor-testimonial__footer"
                style={{
                  boxSizing: "border-box",
                  display: "flex",
                  WebkitBoxAlign: "center",
                  alignItems: "center",
                  WebkitBoxPack: "center",
                  justifyContent: "center",
                  marginTop: "0px",
                }}
              />
            </div>
          </div>
          <div
            className="swiper-slide swiper-slide-duplicate"
            style={{
              boxSizing: "border-box",
              flexShrink: 0,
              position: "relative",
              borderStyle: "solid",
              borderWidth: "0px",
              overflow: "hidden",
              willChange: "transform",
              transitionDuration: "0.5s",
              transitionProperty:
                "border, background, transform, -webkit-transform",
              padding: "0px 35px",
              top: "-100px",
              height: "250px",
              width: "368.667px",
              marginRight: "10px",
            }}
          >
            <div
              className="elementor-testimonial"
              style={{ boxSizing: "border-box", textAlign: "center" }}
            >
              <div
                className="elementor-testimonial__content"
                style={{ boxSizing: "border-box" }}
              >
                <div
                  className="elementor-testimonial__text"
                  style={{
                    boxSizing: "border-box",
                    fontSize: "1.3em",
                    fontStyle: "italic",
                    lineHeight: 1.5,
                    fontFamily: "Rubik, sans-serif",
                  }}
                >
                  <div
                    className="outer_box_content"
                    style={{
                      boxSizing: "border-box",
                      padding: "0px",
                      display: "flex",
                      alignItems: "center",
                      flexDirection: "column",
                    }}
                  >
                    <div
                      className="inner_box_content"
                      style={{
                        boxSizing: "border-box",
                        padding: "0px 20px",
                        display: "flex",
                        alignItems: "center",
                        flexDirection: "column",
                      }}
                    >
                      <div
                        className="background_logo"
                        style={{
                          boxSizing: "border-box",
                          background: "rgb(2, 11, 18)",
                          padding: "12px",
                          borderRadius: "100%",
                          width: "125px",
                          marginBottom: "10px",
                          position: "relative",
                        }}
                      >
                        <Image
                          height={101}
                          width={101}
                          alt="Skippy"
                          src="https://blockchaintechs.io/wp-content/uploads/2022/11/Skippy.webp"
                          style={{
                            userSelect: "none",
                            verticalAlign: "top",
                            boxSizing: "border-box",
                            border: "none",
                            borderRadius: "0px",
                            height: "auto",
                            maxWidth: "100%",
                            boxShadow: "none",
                            objectFit: "contain",
                            width: "262px",
                            outline: "none",
                            pointerEvents: "none",
                          }}
                        />
                      </div>
                      <h3
                        style={{
                          margin: "0px",
                          padding: "0px",
                          overflowWrap: "break-word",
                          boxSizing: "border-box",
                          fontFamily: "rubik",
                          fontWeight: 500,
                          fontStyle: "normal",
                          fontSize: "15px",
                          lineHeight: 1.15,
                          textTransform: "uppercase",
                          marginBottom: "5px",
                          color: "rgb(0, 212, 108)",
                        }}
                      >
                        Apparels Marketplace
                      </h3>
                      <p
                        style={{
                          margin: "0px 0px 18px",
                          padding: "0px",
                          lineHeight: 1.5,
                          boxSizing: "border-box",
                          color: "rgb(235, 235, 235)",
                          fontFamily: "rubik",
                          fontStyle: "normal",
                          fontWeight: 300,
                          fontSize: "14px",
                        }}
                      >
                        Apparels marketplace with fiat and crypto payment
                        gateway integrated.
                      </p>
                      <button
                        style={{
                          outline: "none",
                          padding: "10px 30px",
                          margin: "0px 0px 15px",
                          border: "none",
                          borderRadius: "5px",
                          width: "auto",
                          cursor: "pointer",
                          lineHeight: "23px",
                          fontSize: "12px",
                          fontWeight: 500,
                          textTransform: "uppercase",
                          letterSpacing: "0.2px",
                          color: "rgb(255, 255, 255)",
                          transition: "all 0.4s ease-in-out 0s",
                          boxSizing: "border-box",
                          background: "rgb(0, 212, 108)",
                          backgroundImage: "initial",
                          backgroundSize: "initial",
                          height: "unset",
                          display: "none",
                        }}
                      >
                        <a
                          target="_blank"
                          style={{
                            boxSizing: "border-box",
                            textDecoration: "none",
                            boxShadow: "none",
                            display: "inline",
                            transition: "all 0.1s ease 0s",
                            color: "rgb(0, 0, 0)",
                            fontWeight: 600,
                            fontFamily: "rubik",
                            fontSize: "13px",
                            outline: "none",
                          }}
                        >
                          View More
                        </a>
                      </button>
                    </div>
                  </div>{" "}
                </div>
              </div>
              <div
                className="elementor-testimonial__footer"
                style={{
                  boxSizing: "border-box",
                  display: "flex",
                  WebkitBoxAlign: "center",
                  alignItems: "center",
                  WebkitBoxPack: "center",
                  justifyContent: "center",
                  marginTop: "0px",
                }}
              />
            </div>
          </div>
        </div>
        <div
          className="elementor-swiper-button elementor-swiper-button-prev"
          aria-label="Previous slide"
          role="button"
          tabIndex={0}
          style={{
            boxSizing: "border-box",
            right: "unset",
            position: "absolute",
            display: "inline-flex",
            zIndex: 1,
            cursor: "pointer",
            transform: "translateY(-50%)",
            left: "10px",
            fontSize: "20px",
            border: "3px solid rgb(0, 0, 0)",
            padding: "12px",
            borderRadius: "100px",
            background: "rgb(0, 212, 108)",
            color: "rgb(0, 0, 0)",
            top: "50%",
          }}
        >
          <svg
            className="e-font-icon-svg e-eicon-chevron-left"
            aria-hidden="true"
            viewBox="0 0 1000 1000"
            xmlns="http://www.w3.org/2000/svg"
            style={{
              pointerEvents: "none",
              boxSizing: "border-box",
              fill: "rgba(238, 238, 238, 0.9)",
              height: "1em",
              width: "1em",
            }}
          >
            <path
              d="M646 125C629 125 613 133 604 142L308 442C296 454 292 471 292 487 292 504 296 521 308 533L604 854C617 867 629 875 646 875 663 875 679 871 692 858 704 846 713 829 713 812 713 796 708 779 692 767L438 487 692 225C700 217 708 204 708 187 708 171 704 154 692 142 675 129 663 125 646 125Z"
              style={{ boxSizing: "border-box" }}
            />
          </svg>{" "}
          <span
            className="elementor-screen-only"
            style={{
              margin: "-1px",
              padding: "0px",
              overflow: "hidden",
              border: "0px",
              position: "absolute",
              top: "-10000em",
              width: "1px",
              height: "1px",
              clip: "rect(0px, 0px, 0px, 0px)",
              boxSizing: "border-box",
            }}
          >
            Previous
          </span>
        </div>
        <div
          className="elementor-swiper-button elementor-swiper-button-next"
          aria-label="Next slide"
          role="button"
          tabIndex={0}
          style={{
            boxSizing: "border-box",
            left: "unset",
            position: "absolute",
            display: "inline-flex",
            zIndex: 1,
            cursor: "pointer",
            transform: "translateY(-50%)",
            right: "10px",
            fontSize: "20px",
            border: "3px solid rgb(0, 0, 0)",
            padding: "12px",
            borderRadius: "100px",
            background: "rgb(0, 212, 108)",
            color: "rgb(0, 0, 0)",
            top: "50%",
          }}
        >
          <svg
            className="e-font-icon-svg e-eicon-chevron-right"
            aria-hidden="true"
            viewBox="0 0 1000 1000"
            xmlns="http://www.w3.org/2000/svg"
            style={{
              pointerEvents: "none",
              boxSizing: "border-box",
              fill: "rgba(238, 238, 238, 0.9)",
              height: "1em",
              width: "1em",
            }}
          >
            <path
              d="M696 533C708 521 713 504 713 487 713 471 708 454 696 446L400 146C388 133 375 125 354 125 338 125 325 129 313 142 300 154 292 171 292 187 292 204 296 221 308 233L563 492 304 771C292 783 288 800 288 817 288 833 296 850 308 863 321 871 338 875 354 875 371 875 388 867 400 854L696 533Z"
              style={{ boxSizing: "border-box" }}
            />
          </svg>{" "}
          <span
            className="elementor-screen-only"
            style={{
              margin: "-1px",
              padding: "0px",
              overflow: "hidden",
              border: "0px",
              position: "absolute",
              top: "-10000em",
              width: "1px",
              height: "1px",
              clip: "rect(0px, 0px, 0px, 0px)",
              boxSizing: "border-box",
            }}
          >
            Next
          </span>
        </div>
        <span
          className="swiper-notification"
          aria-atomic="true"
          aria-live="assertive"
          style={{
            boxSizing: "border-box",
            position: "absolute",
            left: "0px",
            top: "0px",
            pointerEvents: "none",
            opacity: 0,
            zIndex: -1000,
          }}
        />
      </div>
          </>
  );
}
