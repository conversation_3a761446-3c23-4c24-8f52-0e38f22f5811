import React, { useEffect, useMemo, useState } from 'react'
import Swipe<PERSON><PERSON><PERSON>eft from './swipeItemLeft';
import SwipeItem from './swipeItem';
import SwipeItemRight from './swipeItemRight';
import { IconButton } from '@mui/material';
import { FaAngleLeft, FaAngleRight } from 'react-icons/fa';

const SwipeAll: React.FC = () => {
    const [currentIndex, setCurrentIndex] = useState(0);



    const [servicesData,setServicesData] = useState([
        {
            "title": "Crypto-Powered Gaming Solutions",
            "text": "Elevate your gaming experience with our Crypto-Powered Gaming Solutions. Explore Play-To-Earn platforms designed to meet market demands, integrating cutting-edge features for enhanced profitability.",
            "image": "https://blockchaintechs.io/wp-content/uploads/2022/11/P2E.webp"
        },
        {
            "title": "Next-Gen Crypto Solutions",
            "text": "Unlock the potential of cryptocurrency with our comprehensive development services. From ICO development to cryptocurrency exchange solutions and multi-currency wallet development, we offer a holistic approach to crypto innovation.",
            "image": "https://blockchaintechs.io/wp-content/uploads/2022/11/Crypto-currency-Development.webp"
        },
        {
            "title": "Empower Your Platform with Web 3.0",
            "text": "Embrace the future of the internet with our Web 3.0 Services. Harness the expertise of our developers to create bespoke solutions that delve into the realm of decentralized technologies, driving your platform forward.",
            "image": "https://blockchaintechs.io/wp-content/uploads/2022/11/WEB3.O.webp"
        },
        {
            "title": "Revolutionize Finance with DeFi",
            "text": "Revolutionize the financial landscape with our DeFi Development Services. Build secure and scalable DeFi platforms, including decentralized applications, wallets, decentralized exchanges, and tokens, to stay ahead in the digital economy.",
            "image": "https://blockchaintechs.io/wp-content/uploads/2022/11/DEFI-Services.webp"
        },
        {
            "title": "Crafting the Metaverse Experience",
            "text": "Embark on a journey to the metaverse with our Development Services. Dive into 3D virtual worlds, metaverse NFT marketplaces, and decentralized platforms, offering end-to-end solutions to bring your vision to life.",
            "image": "https://blockchaintechs.io/wp-content/uploads/2022/11/Metaverse-Development-services.webp"
        },
        {
            "title": "Unify Chains with NFT Marketplaces",
            "text": "Expand your reach with our Multichain NFT Marketplace solutions. Launch your platform seamlessly across multiple Blockchain Networks, including Ethereum, Polygon, BSC, EOS, Solana, and more, unlocking new opportunities for your business.",
            "image": "https://blockchaintechs.io/wp-content/uploads/2022/11/Multichain-NFT-Marketplace.webp"
        }
    ]
    );

    // Duplicate data removed, as requested



/*

[
        {
            title: "P2E Game Development",
            text: "Our Play To Earn game development services offer a top-notch P2E game platform, with market-focused features and functionality tailored to your business objectives.",
            image: "https://blockchaintechs.io/wp-content/uploads/2022/11/P2E.webp" //
        },
        {
            title: "Cryptocurrency Development",
            text: "Our Cryptocurrency development services offer widespread ICO development, cryptocurrency exchange development, and multi-currency wallet development, among other services.",
            image: "https://blockchaintechs.io/wp-content/uploads/2022/11/Crypto-currency-Development.webp" // Empty image property
        },
        {
            title: "Web 3.0 Services",
            text: "To implement your digital solution using the newest technology, hire Web 3.0 developers. With our custom web3 development services, dive deeper into the world of web 3.0.",
            image: "https://blockchaintechs.io/wp-content/uploads/2022/11/WEB3.O.webp" // Empty image property
        },
        {
            title: "DeFi Development Services",
            text: "We provide secure & scalable DeFi platforms. Our DeFi development services include a variety of highly desirable DeFi solutions, including dApps, wallets, DEXs, and tokens.",
            image: "https://blockchaintechs.io/wp-content/uploads/2022/11/DEFI-Services.webp" // Empty image property
        },
        {
            title: "Metaverse Development Services",
            text: "We can assist you in developing the metaverse apps, 3D virtual worlds, and metaverse NFT marketplaces on decentralized platforms with end-to-end metaverse services.",
            image: "https://blockchaintechs.io/wp-content/uploads/2022/11/Metaverse-Development-services.webp" // Empty image property
        },
        {
            title: "Multichain NFT Marketplace",
            text: "We help you launch your Multichain NFT Marketplace platform on several Blockchain Networks like Ethereum, Polygon, BSC, EOS, Solana, and more.",
            image: "https://blockchaintechs.io/wp-content/uploads/2022/11/Multichain-NFT-Marketplace.webp" // Empty image property
        }
    ]

*/




    const [animatedId, setAnimateId] = useState("");
    const [item,setItem]=useState<any>(servicesData[1]);
    const [itemLeft,setItemLeft]=useState<any>(servicesData[0]);
    const [itemRight,setItemRight]=useState<any>(servicesData[3]);
    
    useEffect(() => {

    }, []);
    useEffect(() => {
        let leftIndex;
        let middleIndex = currentIndex;
        let rightIndex;
    
        // Calculate the indexes for the left, middle, and right items
        if (currentIndex === 0) {
            leftIndex = servicesData.length - 1; // Set left index to the last item in the array
            rightIndex = currentIndex + 1;
        } else {
            leftIndex = currentIndex - 1;
            rightIndex = (currentIndex + 1) % servicesData.length;
        }
    
        // Set items based on calculated indexes
        setItemLeft(servicesData[leftIndex]);
        setItem(servicesData[middleIndex]);
        setItemRight(servicesData[rightIndex]);
    }, [currentIndex, servicesData]);
    
    const prev = () => {

        if (currentIndex > 1) {
            setCurrentIndex(currentIndex - 1);
        }
     else{
        setCurrentIndex(servicesData.length-1);
     }
        setAnimateId(Date.now() + "goLeft");
    }

    const next = () => {
        if (currentIndex < servicesData.length - 1) {
            setCurrentIndex(currentIndex + 1);
        }
        else{
        setCurrentIndex(0);
        }
        setAnimateId(Date.now() + "id");
    }

    useEffect(() => {

    }, [animatedId]);

    return (<div className="swapAll d-flex align-items-center" style={{ margin: '0 auto', maxWidth: "max-content" }}>
        <div className='btn' style={{ padding: 20 }}>
            <IconButton onClick={() => {
                prev()
            }} color='success' style={{ border: "1px solid var(--green)" }}>
                <FaAngleLeft />
            </IconButton>
        </div>
        <SwipeItemLeft animatedId={animatedId} item={itemLeft} />
        <SwipeItem animatedId={animatedId} item={item} />
        <SwipeItemRight animatedId={animatedId} item={itemRight} />
        <div className='btn2' style={{ padding: 20 }}>
            <IconButton onClick={() => {
                next()
            }} color='success' style={{ border: "1px solid var(--green)" }}>
                <FaAngleRight />
            </IconButton>
        </div>

    </div>)
}

export default SwipeAll;