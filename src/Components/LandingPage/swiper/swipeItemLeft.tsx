import { MDBBtn } from "mdb-react-ui-kit";
import Image from "next/image";
import React, { useEffect, useState } from "react";
import "./style.css";
interface Props{
  animatedId:string,
  item:any
}
const SwipeItemLeft:React.FC<Props>=({animatedId,item})=>{
const [styles,setStyles]=useState<any>({
})
  useEffect(()=>{
    setStyles({
animationDuration:"500ms",
animationName:animatedId.includes("goLeft") ? "flipLeft_":"flipLeft"

    })
    setTimeout(()=>{
setStyles({});
    },1600);
  },[animatedId]);

  return (
    <>
<div className={`slider left ${animatedId.includes("goLeft") ? "goLeft":""}`} style={styles}>
<h4>{item?.title}</h4>

<Image src={item?.image} alt='' width={500} height={500}/>
<br/>
<p className="text">{item?.text}
</p>

<div className="d-flex justify-content-center">
<MDBBtn size='lg' color='success'>
Read more
</MDBBtn>
</div>

<br/>
</div>
          </>
  );
}

export default SwipeItemLeft;