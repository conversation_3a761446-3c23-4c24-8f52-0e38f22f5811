.slider{
    max-width:400px;
    width:340px;
    min-height: 380px;
    border-radius:20px;
    box-shadow: 1px 1px 10px 0px rgb(12, 14, 22);
    padding:25px !important;
    margin-left:15px;
    background: #05141A;
    overflow:hidden;
}
@media (max-width:700px) {
    .slider.right, .slider.left{
        display: none !important;
    }
    .swapAll{
        position: relative !important;
        width:100%;
    }
    .swapAll .btn2{
        left:0;
        margin-left: 80% !important;
        z-index:22;
        margin-top: -10%;
        position: absolute !important;
    }
   
    .swapAll .btn{
        margin-top: -10%;
        z-index:22;
        margin-left: -2%;
        position: absolute !important;
    }
    .slider.active{
        width:95% !important;

min-height: max-content;
        margin:0 auto !important;
    }
    
}
.slider h4{
    text-align: center;
    color:var(--green);
    font-weight: bolder;
}
.slider .text{
    text-align: center;
    color:rgb(243, 237, 237);
    font-weight: 460;
    min-height: max-content!important;
}
.slider img{

max-width: 120px;
max-height:100px;

margin:0 auto;
display:block;
}

.slider button{
margin:0 auto;
display:"block";}
@media (max-width:700px) {
.slider button{
display:none;
}

    .slider{
        margin: 0 !important;
    }
    /**remove left and right items**/
}
.slider.active{
    background:linear-gradient(to right top, rgb(17, 168, 94), rgb(0, 148, 108), rgb(0, 127, 113), rgb(0, 105, 108), rgb(0, 83, 94), rgb(0, 83, 94), rgb(0, 83, 94), rgb(0, 83, 94), rgb(0, 105, 108), rgb(0, 127, 113), rgb(0, 148, 108), rgb(17, 168, 94));
}
.slider.left{
    transform: perspective(900px) rotateY(-45deg);
}
.slider.right{
    transform: perspective(900px) rotateY(45deg);
}
.slider.left button, .slider.right button{
    transform: translate(-0px,100px);
    animation-name: goDown;
}
.slider.animate *{
    animation-duration:1s;
}
@keyframes goDown {
    0%{
    transform: translate(-0px,0px);
    }
100%{
    transform: translate(-0px,100px);
}
}


@keyframes flipRight {
    0%{
        transform: translate(30px,0px);
    }
    100%{
        transform: perspective(900px) rotateY(45deg) translate(-0px,0px);
    }
}


@keyframes flipLeft {
    0%{
        transform:none;
    }
    100%{
        transform: perspective(900px) rotateY(-45deg) translate(-0px,0px);
    }
}


@keyframes Active{
    0%{
        transform:translate(30px,0px);
    }
    100%{
        transform:translate(-0px,0px);
    }
}
/*go left animation*/

@keyframes flipRight_ {
    0%{
        transform:none;

    }
    100%{
        transform: perspective(900px) rotateY(45deg) translate(-0px,0px);
    }
}


@keyframes flipLeft_ {
    0%{
        transform: translate(-30px,0px);
    }
    100%{
        transform: perspective(900px) rotateY(-45deg) translate(-0px,0px);
    }
}


@keyframes Active_{
    0%{
        transform:translate(-30px,0px);
    }
    100%{
        transform:translate(-0px,0px);
    }
}