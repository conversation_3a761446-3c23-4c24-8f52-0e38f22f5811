import ColoredHeader from '@/utils/coloredHeader'
import React, { useRef, useEffect } from 'react';
import TechCarousel from './TechCarousal';
import Bold from '@/utils/pBolder';
import SkillBox from './skillBox';
import LargeSkillBox from './largeSkillBox';

const Technologies:React.FC=()=>{
    const techImages=[
        "https://blockchaintechs.io/wp-content/uploads/2022/11/Ethereum.webp",
        "https://blockchaintechs.io/wp-content/uploads/2022/11/Polygon-1.webp",
        "https://blockchaintechs.io/wp-content/uploads/2022/11/Ripple.png",
        "https://blockchaintechs.io/wp-content/uploads/2022/11/Tron-1.webp",
        "https://blockchaintechs.io/wp-content/uploads/2022/11/XDC.webp",
        "https://blockchaintechs.io/wp-content/uploads/2022/11/Avalanche.webp",
        "https://blockchaintechs.io/wp-content/uploads/2022/11/Binance-1.webp",
        "https://blockchaintechs.io/wp-content/uploads/2022/11/Fantom.webp",
        "https://blockchaintechs.io/wp-content/uploads/2022/11/Chainlink-1.webp",
        "https://blockchaintechs.io/wp-content/uploads/2022/11/Cardano-1.webp",
        "https://blockchaintechs.io/wp-content/uploads/2022/11/XDC.webp",
        "https://blockchaintechs.io/wp-content/uploads/2022/11/Solana-1.webp",
        "https://blockchaintechs.io/wp-content/uploads/2022/11/Polkadott.webp",
        "https://blockchaintechs.io/wp-content/uploads/2022/11/Solana-1.webp",
        "https://blockchaintechs.io/wp-content/uploads/2022/11/Ripple.png",
        "https://blockchaintechs.io/wp-content/uploads/2022/11/Hyperledger-1.webp",
        "https://blockchaintechs.io/wp-content/uploads/2022/11/Stellar-1.webp"
    ]

    const smallSkillBox=[
        {
            images:[
'https://blockchaintechs.io/wp-content/uploads/2022/11/React-Native.webp',
'https://blockchaintechs.io/wp-content/uploads/2022/11/web3-js.webp',
'https://blockchaintechs.io/wp-content/uploads/2022/11/Java.webp',
'https://blockchaintechs.io/wp-content/uploads/2022/11/Node-JS.webp'
],
            title:'Front & Backend'
        },
        {
            images:[
'https://blockchaintechs.io/wp-content/uploads/2022/11/Unity.webp',
'https://blockchaintechs.io/wp-content/uploads/2022/11/Unreal-Engine.webp',
'https://blockchaintechs.io/wp-content/uploads/2022/11/TensorFlow.webp',
'https://blockchaintechs.io/wp-content/uploads/2022/11/ARToolkit.webp'
            ],
            title:'AR, VR Development'
        },
        {
            images:[
'https://blockchaintechs.io/wp-content/uploads/2022/11/TensorFlow.webp',
'https://blockchaintechs.io/wp-content/uploads/2022/11/AWS.webp',
'https://blockchaintechs.io/wp-content/uploads/2022/11/scikit-learn.webp',
            ],
            title:'AI Development            '
        },
        {
            images:[
'https://blockchaintechs.io/wp-content/uploads/2022/11/Unity.webp',
'https://blockchaintechs.io/wp-content/uploads/2022/11/CRY-Engine.webp',
            ],
            title:'3D Development            '
        }
    ]
    return (
        <>
        <ColoredHeader>
        Our Technology Stack
        </ColoredHeader>
<br/>
<TechCarousel techImages={techImages}/>
<br/>
<div className='smallLargeContainer d-flex'>

<div className='smallComponentsContainer'>
<Bold>METAVERSE
</Bold>
<div className='d-flex ' style={{flexFlow:"row wrap"}}>
{smallSkillBox.map((item,i)=><SkillBox key={i} images={item.images} title={item.title}/>)}
</div>

</div>

<div className='largeComponentsContainer'>
<Bold>MOBILE</Bold>
<LargeSkillBox title='App Development' images={[
    'https://blockchaintechs.io/wp-content/uploads/2022/08/logoReactNative.webp',
    'https://blockchaintechs.io/wp-content/uploads/2022/11/Flutter.webp',
    'https://blockchaintechs.io/wp-content/uploads/2022/11/XCode.webp',
    'https://blockchaintechs.io/wp-content/uploads/2022/11/Android-Studio.webp',
    'https://blockchaintechs.io/wp-content/uploads/2022/11/Sketch.webp'
]}/>

</div>



</div>

        </>
    )
}

export default Technologies;