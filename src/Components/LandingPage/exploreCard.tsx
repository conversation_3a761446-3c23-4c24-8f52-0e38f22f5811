import Image from "next/image";
import React from "react";
interface Props{
    image:string,
    heading:string,
    text:string
}
const ExploreCard:React.FC<Props>=({image,heading,text})=> {
  return (
    <>
      <div
        className="explore_card_section"
        style={{
          boxSizing: "border-box",
          borderRadius: "24px",
          padding: "2.5%",
          background: "var(--dark)",
          width: "32%",
          display: "flex",
          alignItems: "flex-start",
          justifyContent: "center",
          flexDirection: "column",
          marginBottom: "2.11%",
          marginTop:15
        }}
      >
        {" "}
        <div
          className="icon_section"
          style={{
            boxSizing: "border-box",
            display: "flex",
            paddingBottom: "4%",
            alignItems: "center",
            justifyContent: "flex-start",
          }}
        >
          {" "}
          <Image
          className='exploreImage'
            height={56}
            width={56}
            alt="Smart_Contracts"
            src={image}
            style={{
              boxSizing: "border-box",
              border: "none",
              borderRadius: "0px",
              height: "auto",
              maxWidth: "100%",
              boxShadow: "none",
              marginRight: "16px",
              pointerEvents: "none",
            }}
          />{" "}
          <h3
            style={{
              boxSizing: "border-box",
              fontSize: "24px",
              lineHeight: "30px",
              color: "rgb(255, 255, 255)",
              fontFamily: "Rubik",
              fontWeight: 500,
              margin: "unset",
            }}
          >
        {heading}
          </h3>{" "}
        </div>{" "}
        <p
          style={{
            boxSizing: "border-box",
            placeContent: "center",
            display: "flex",
            alignItems: "center",
            lineHeight: "32px",
            color: "rgb(210, 210, 210)",
            margin: "unset",
          }}
        >
        {text}
        </p>{" "}
      </div>
      
    </>
  );
}
export default ExploreCard;