import LogoWithText from "@/utils/LogoWithText";
import Image from "next/image";
import React from "react";

export default function AwardData() {
    return (
        <>
            <div
                className="news_container_outer outer_new_container"
                style={{
                    boxSizing: "border-box",
                    borderRadius: "16px",
                    width: "100%",
                    display: "flex",
                    alignItems: "center",
                    justifyContent: "space-between",
                }}
            >
                <div
                    className="news_container_inner"
                    style={{ boxSizing: "border-box", padding: "0%", width: "75%" }}
                >
                    <div style={{fontSize:'larger'}} className='appIcon'>
                        <LogoWithText smallTextStyle={{fontSize:"medium"}} textStyle={{fontSize:'large'}} text="ALTAPPLAP" size={100}/>
                    <br/></div>
                    <p
                        style={{
                            margin: "0px 0px 18px",
                            padding: "0px",
                            lineHeight: 1.5,
                            boxSizing: "border-box",
                            color: "rgb(255, 255, 255)",
                            fontSize: "22px",
                            fontWeight: 300,
                        }}
                    >
                       No. 1 fastest-growing blockchain developer by TopDevelopers.co for 2022.
                       This accolade reflects our unwavering commitment to delivering cutting-edge 
                       blockchain solutions that drive innovation and efficiency in the digital world.
                    </p>
                    <a
                        href="/Portfolio"
                        target="_blank"
                        style={{
                            transition: "all 0.2s ease-in-out 0s",
                            boxSizing: "border-box",
                            textDecoration: "none",
                            boxShadow: "none",
                            fontSize: "24px",
                            color: "rgb(0, 212, 108)",
                            outline: "none",
                        }}
                    >
                        Visit →
                    </a>
                </div>
                <div
                    className="news_container_badge"
                    style={{ boxSizing: "border-box" }}
                >
                    <a
                        href="#"
                        target="_blank"
                        style={{
                            transition: "all 0.2s ease-in-out 0s",
                            boxSizing: "border-box",
                            textDecoration: "none",
                            boxShadow: "none",
                            fontSize: "24px",
                            color: "rgb(0, 212, 108)",
                            outline: "none",
                        }}
                    >
                        <Image
                            className="news_container_badge"
                            height={339}
                            width={316}
                            alt="TopDevelopers-ALTAPPLAP"
                            src="/images/award.webp"
                            style={{
                                userSelect: "none",
                                verticalAlign: "top",
                                boxSizing: "border-box",
                                border: "none",
                                borderRadius: "0px",
                                height: "auto",
                                maxWidth: "100%",
                                boxShadow: "none",
                                outline: "none",
                                pointerEvents: "none",
                            }}
                        />
                    </a>
                </div>
            </div>

        </>
    );
}
