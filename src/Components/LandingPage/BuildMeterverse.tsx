import ColoredHeader from '@/utils/coloredHeader'
import PText from '@/utils/pText'
import Image from 'next/image'
import React from 'react'
import { deflate } from 'zlib'
import MeterVerseBox from './meterverseBox'
import { Button } from '@mui/material'
import { MDBBtn } from 'mdb-react-ui-kit'
import Link from 'next/link'

const BuildMeterVerse:React.FC=()=>{
    return (
        <>
        <div className='buildMeterverse d-flex justify-content-between'>
            <div style={{width:"40%",height:'100%',position:"relative"}} >
<Image src='/images/scienceGuy.webp' className='animatedImageEL' objectFit='cover' width={500} height={500} alt=''/>
</div>


<div style={{width:"60%"}}>

    
<ColoredHeader>
Build Your Own Crypto Bots world
</ColoredHeader>
<PText>
With the help of our blockchain development services, you may develop new and, creative methods to 
provide excellent service that are driven by exceptional experiences
</PText>
<br/><br/>
<div className='meterVerseContainer d-flex justify-content-round'>
<MeterVerseBox src='/images/Virtual-Reality.svg' href='/CryptoArbitrageBot' text={"Arbitrage Bot"}/>
<MeterVerseBox src='/images/argumentedReality.svg' href='/CryptoTrading' color='blue' text='Trading Bots'/>
<MeterVerseBox src='/images/smartDevices.svg' href='/BotDevelopment' text='Development'/>
</div>
<br/>

<Link href='/contactUs'>
<div className='buttonContainer  d-flex justify-content-end'  style={{maxWidth:"90%",margin:"0 auto"}}>
    <MDBBtn className='text-center' style={{minWidth:'200px',marginLeft:5}} color='success' size='lg'>Lets Talk</MDBBtn>
    </div>
    </Link>

</div>


            </div>

        </>
    )
}

export default BuildMeterVerse;