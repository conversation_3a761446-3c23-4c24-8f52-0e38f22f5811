import LogoWithText from '@/utils/LogoWithText';
import MessageIcon from '@/utils/Mail';
import ColoredHeader from '@/utils/coloredHeader';
import Bold from '@/utils/pBolder';
import PText from '@/utils/pText';
import TelegramIcon from '@/utils/telegram';
import WhatsAppIcon from '@/utils/whatsApp';
import { MDBBtn } from 'mdb-react-ui-kit';
import Image from 'next/image';
import React, { useState, useEffect } from 'react'
import FollowIcon from './followIcon';
import { Facebook, Instagram, Linkedin, Twitter, ChevronDown, Check, X, Star, Send, Calendar, DollarSign, Users, Code, Smartphone, Globe, Shield, Zap } from 'react-feather';
import { FaDiscord } from 'react-icons/fa';
import { Divider } from '@mui/material';
import { ClipLoader } from 'react-spinners';
import toast, { Toaster } from 'react-hot-toast';
import { phoneNumber, telegramLink,email, phoneNumberUk } from '../fixed/sideContact';
import { Call, } from '@mui/icons-material';
import './advancedForm.css';

// Service options with icons
const serviceOptions = [
  { id: 'blockchain', name: 'Blockchain Development', icon: <Code size={20} />, description: 'Custom blockchain solutions' },
  { id: 'defi', name: 'DeFi Development', icon: <DollarSign size={20} />, description: 'Decentralized finance platforms' },
  { id: 'nft', name: 'NFT Development', icon: <Star size={20} />, description: 'NFT marketplaces & tokens' },
  { id: 'dapp', name: 'DApp Development', icon: <Globe size={20} />, description: 'Decentralized applications' },
  { id: 'smart-contracts', name: 'Smart Contracts', icon: <Shield size={20} />, description: 'Secure smart contract development' },
  { id: 'mobile', name: 'Mobile App Development', icon: <Smartphone size={20} />, description: 'iOS & Android applications' },
  { id: 'trading-bots', name: 'Trading Bots', icon: <Zap size={20} />, description: 'Automated trading solutions' },
  { id: 'consulting', name: 'Blockchain Consulting', icon: <Users size={20} />, description: 'Strategic blockchain guidance' },
];

// Support type options
const supportOptions = [
  { id: 'new-project', name: 'New Project Development', description: 'Start from scratch' },
  { id: 'existing-project', name: 'Existing Project Enhancement', description: 'Improve current solution' },
  { id: 'maintenance', name: 'Maintenance & Support', description: 'Ongoing technical support' },
  { id: 'consultation', name: 'Technical Consultation', description: 'Expert advice & strategy' },
  { id: 'audit', name: 'Code Audit & Security', description: 'Security review & optimization' },
  { id: 'integration', name: 'Third-party Integration', description: 'API & system integration' },
];

// Budget ranges
const budgetRanges = [
  { id: 'under-10k', name: 'Under $10,000', value: 'under-10k' },
  { id: '10k-25k', name: '$10,000 - $25,000', value: '10k-25k' },
  { id: '25k-50k', name: '$25,000 - $50,000', value: '25k-50k' },
  { id: '50k-100k', name: '$50,000 - $100,000', value: '50k-100k' },
  { id: '100k-250k', name: '$100,000 - $250,000', value: '100k-250k' },
  { id: 'over-250k', name: 'Over $250,000', value: 'over-250k' },
  { id: 'discuss', name: 'Let\'s Discuss', value: 'discuss' },
];

// Timeline options
const timelineOptions = [
  { id: 'asap', name: 'ASAP', description: 'Urgent project' },
  { id: '1-month', name: '1 Month', description: 'Quick delivery' },
  { id: '2-3-months', name: '2-3 Months', description: 'Standard timeline' },
  { id: '3-6-months', name: '3-6 Months', description: 'Complex project' },
  { id: '6-months-plus', name: '6+ Months', description: 'Long-term project' },
  { id: 'flexible', name: 'Flexible', description: 'No rush' },
];

const ConnectWithUs: React.FC = () => {
  const [formData, setFormData] = useState<any>({
    services: [],
    supportType: '',
    budget: '',
    timeline: '',
    priority: 'medium'
  });
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [currentStep, setCurrentStep] = useState(1);
  const [formErrors, setFormErrors] = useState<any>({});
  const [showServiceDropdown, setShowServiceDropdown] = useState(false);
  const [showSupportDropdown, setShowSupportDropdown] = useState(false);
  const [showBudgetDropdown, setShowBudgetDropdown] = useState(false);
  const [showTimelineDropdown, setShowTimelineDropdown] = useState(false);

  // Close dropdowns when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      const target = event.target as Element;
      if (!target.closest('.custom-dropdown')) {
        setShowServiceDropdown(false);
        setShowSupportDropdown(false);
        setShowBudgetDropdown(false);
        setShowTimelineDropdown(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  // Form validation
  const validateStep = (step: number) => {
    const errors: any = {};
    
    switch(step) {
      case 1:
        if (!formData.name?.trim()) errors.name = 'Name is required';
        if (!formData.email?.trim()) errors.email = 'Email is required';
        else if (!/\S+@\S+\.\S+/.test(formData.email)) errors.email = 'Email is invalid';
        if (!formData.phoneNumber?.trim()) errors.phoneNumber = 'Phone number is required';
        if (!formData.company?.trim()) errors.company = 'Company name is required';
        break;
      case 2:
        if (formData.services.length === 0) errors.services = 'Please select at least one service';
        if (!formData.supportType) errors.supportType = 'Please select support type';
        break;
      case 3:
        if (!formData.budget) errors.budget = 'Please select budget range';
        if (!formData.timeline) errors.timeline = 'Please select timeline';
        if (!formData.message?.trim()) errors.message = 'Project description is required';
        break;
    }
    
    setFormErrors(errors);
    return Object.keys(errors).length === 0;
  };

  const nextStep = () => {
    if (validateStep(currentStep)) {
      setCurrentStep(prev => Math.min(prev + 1, 3));
    }
  };

  const prevStep = () => {
    setCurrentStep(prev => Math.max(prev - 1, 1));
  };

  const toggleService = (serviceId: string) => {
    setFormData((prev: any) => ({
      ...prev,
      services: prev.services.includes(serviceId)
        ? prev.services.filter((id: string) => id !== serviceId)
        : [...prev.services, serviceId]
    }));
  };

  const submit = async (e: any) => {
    e.preventDefault();
    
    if (!validateStep(3)) return;
    
    if(isLoading) return;
    
    try {
      setIsLoading(true);
      
      // Enhanced form data with new fields
      const enhancedFormData = {
        ...formData,
        services: formData.services.map((id: string) => 
          serviceOptions.find(s => s.id === id)?.name
        ).join(', '),
        supportType: supportOptions.find(s => s.id === formData.supportType)?.name,
        budget: budgetRanges.find(b => b.id === formData.budget)?.name,
        timeline: timelineOptions.find(t => t.id === formData.timeline)?.name,
        submittedAt: new Date().toISOString(),
        source: 'Advanced Contact Form'
      };
      
      const params = new URLSearchParams(enhancedFormData);
      const res = await fetch(`/api/contactByMail?${params}`);
      const resData = await res.json();
      
      setIsLoading(false);
      toast.success("🎉 Thank you! We'll get back to you within 24 hours.", {
        duration: 5000,
        style: {
          background: 'var(--green)',
          color: 'white',
        },
      });
      
      // Reset form
      setFormData({
        services: [],
        supportType: '',
        budget: '',
        timeline: '',
        priority: 'medium'
      });
      setCurrentStep(1);
      
    } catch(err: any) {
      setIsLoading(false);
      toast.error("Something went wrong. Please try again.", {
        style: {
          background: '#ff4444',
          color: 'white',
        },
      });
    }
  };

  return (
    <>
      <Toaster/>
      <div className='connectWithUs'>
        <ColoredHeader>
          Connect with us
        </ColoredHeader>
        <br/>
        <Bold>Send us your requirements and our team will get back to your shortly</Bold>
        
        <div className='BlogMainContainer' style={{ 
          padding: 40, 
          maxWidth: '100%', 
          margin: "0 auto",
          background: "transparent",
          borderRadius: 0,
          display: 'block'
        }}>

          {/* Contact Information Cards */}
          <div className="contact-info-section" style={{
            display: 'grid',
            gridTemplateColumns: 'repeat(auto-fit, minmax(220px, 1fr))',
            gap: '15px',
            marginBottom: '40px',
            padding: '20px',
            background: 'rgba(10, 27, 24, 0.8)',
            borderRadius: '15px',
            border: '1px solid rgba(0, 212, 108, 0.3)',
            maxWidth: '900px',
            margin: '0 auto 40px auto'
          }}>
            <div className='contactCard d-flex align-items-center'>
              <div style={{maxWidth:"max-content"}}><WhatsAppIcon/></div>
              <div style={{paddingLeft:15}}>
                <span>WhatsApp us</span><br/>
                <span>+{phoneNumber}</span>
              </div>
            </div>

            <div className='contactCard d-flex align-items-center'>
              <div style={{maxWidth:"max-content"}}><TelegramIcon/></div>
              <div style={{paddingLeft:15}}>
                <span>Telegram</span><br/>
                <span>{telegramLink}</span>
              </div>
            </div>

            <div className='contactCard d-flex align-items-center'>
              <div style={{maxWidth:"max-content"}}><MessageIcon/></div>
              <div style={{paddingLeft:15}}>
                <span>Email</span><br/>
                <span>{email}</span>
              </div>
            </div>

            <div className='contactCard d-flex align-items-center'>
              <div style={{maxWidth:"max-content"}}><Call/></div>
              <div style={{paddingLeft:15}}>
                <span>Call</span><br/>
                <span>{phoneNumberUk}</span>
              </div>
            </div>
          </div>

          {/* Advanced Interactive Form */}
          <div className='advanced-form m-block'>
            {/* Progress Indicator */}
            <div className="form-progress">
              <div className="progress-steps">
                {[1, 2, 3].map((step) => (
                  <div key={step} className={`progress-step ${currentStep >= step ? 'active' : ''} ${currentStep > step ? 'completed' : ''}`}>
                    <div className="step-number">
                      {currentStep > step ? <Check size={16} /> : step}
                    </div>
                    <div className="step-label">
                      {step === 1 ? 'Contact Info' : step === 2 ? 'Services' : 'Project Details'}
                    </div>
                  </div>
                ))}
              </div>
              <div className="progress-bar">
                <div className="progress-fill" style={{ width: `${(currentStep / 3) * 100}%` }}></div>
              </div>
            </div>

            <form onSubmit={submit} className="multi-step-form">
              {/* Step 1: Contact Information */}
              {currentStep === 1 && (
                <div className="form-step step-1">
                  <h3 className="step-title">
                    <Users size={24} />
                    Let's get to know you
                  </h3>
                  <p className="step-description">Tell us about yourself and your company</p>
                  
                  <div className="form-grid">
                    <div className="form-group">
                      <label>Full Name *</label>
                      <input
                        type="text"
                        placeholder="Enter your full name"
                        value={formData.name || ''}
                        onChange={(e) => setFormData({...formData, name: e.target.value})}
                        className={formErrors.name ? 'error' : ''}
                      />
                      {formErrors.name && <span className="error-message">{formErrors.name}</span>}
                    </div>

                    <div className="form-group">
                      <label>Email Address *</label>
                      <input
                        type="email"
                        placeholder="<EMAIL>"
                        value={formData.email || ''}
                        onChange={(e) => setFormData({...formData, email: e.target.value})}
                        className={formErrors.email ? 'error' : ''}
                      />
                      {formErrors.email && <span className="error-message">{formErrors.email}</span>}
                    </div>

                    <div className="form-group">
                      <label>Phone Number *</label>
                      <input
                        type="tel"
                        placeholder="+****************"
                        value={formData.phoneNumber || ''}
                        onChange={(e) => setFormData({...formData, phoneNumber: e.target.value})}
                        className={formErrors.phoneNumber ? 'error' : ''}
                      />
                      {formErrors.phoneNumber && <span className="error-message">{formErrors.phoneNumber}</span>}
                    </div>

                    <div className="form-group">
                      <label>Company Name *</label>
                      <input
                        type="text"
                        placeholder="Your company name"
                        value={formData.company || ''}
                        onChange={(e) => setFormData({...formData, company: e.target.value})}
                        className={formErrors.company ? 'error' : ''}
                      />
                      {formErrors.company && <span className="error-message">{formErrors.company}</span>}
                    </div>

                    <div className="form-group full-width">
                      <label>Job Title</label>
                      <input
                        type="text"
                        placeholder="CEO, CTO, Product Manager, etc."
                        value={formData.jobTitle || ''}
                        onChange={(e) => setFormData({...formData, jobTitle: e.target.value})}
                      />
                    </div>
                  </div>

                  <div className="form-actions">
                    <button type="button" onClick={nextStep} className="btn-next">
                      Next Step <ChevronDown size={16} style={{ transform: 'rotate(-90deg)' }} />
                    </button>
                  </div>
                </div>
              )}

              {/* Step 2: Services Selection */}
              {currentStep === 2 && (
                <div className="form-step step-2">
                  <h3 className="step-title">
                    <Code size={24} />
                    What services do you need?
                  </h3>
                  <p className="step-description">Select all services that apply to your project</p>

                  <div className="services-grid">
                    {serviceOptions.map((service) => (
                      <div
                        key={service.id}
                        className={`service-card ${formData.services.includes(service.id) ? 'selected' : ''}`}
                        onClick={() => toggleService(service.id)}
                      >
                        <div className="service-icon">{service.icon}</div>
                        <div className="service-content">
                          <h4>{service.name}</h4>
                          <p>{service.description}</p>
                        </div>
                        <div className="service-check">
                          {formData.services.includes(service.id) && <Check size={16} />}
                        </div>
                      </div>
                    ))}
                  </div>
                  {formErrors.services && <span className="error-message">{formErrors.services}</span>}

                  <div className="form-group">
                    <label>What type of support do you need? *</label>
                    <div className="custom-dropdown">
                      <div
                        className={`dropdown-header ${formErrors.supportType ? 'error' : ''}`}
                        onClick={() => setShowSupportDropdown(!showSupportDropdown)}
                      >
                        <span>
                          {formData.supportType
                            ? supportOptions.find(s => s.id === formData.supportType)?.name
                            : 'Select support type'
                          }
                        </span>
                        <ChevronDown size={16} className={showSupportDropdown ? 'rotated' : ''} />
                      </div>
                      {showSupportDropdown && (
                        <div className="dropdown-options">
                          {supportOptions.map((option) => (
                            <div
                              key={option.id}
                              className={`dropdown-option ${formData.supportType === option.id ? 'selected' : ''}`}
                              onClick={() => {
                                setFormData({...formData, supportType: option.id})
                                setShowSupportDropdown(false)
                              }}
                            >
                              <div>
                                <strong>{option.name}</strong>
                                <p>{option.description}</p>
                              </div>
                              {formData.supportType === option.id && <Check size={16} />}
                            </div>
                          ))}
                        </div>
                      )}
                    </div>
                    {formErrors.supportType && <span className="error-message">{formErrors.supportType}</span>}
                  </div>

                  <div className="form-actions">
                    <button type="button" onClick={prevStep} className="btn-prev">
                      <ChevronDown size={16} style={{ transform: 'rotate(90deg)' }} /> Previous
                    </button>
                    <button type="button" onClick={nextStep} className="btn-next">
                      Next Step <ChevronDown size={16} style={{ transform: 'rotate(-90deg)' }} />
                    </button>
                  </div>
                </div>
              )}

              {/* Step 3: Project Details */}
              {currentStep === 3 && (
                <div className="form-step step-3">
                  <h3 className="step-title">
                    <DollarSign size={24} />
                    Project Details
                  </h3>
                  <p className="step-description">Help us understand your project scope and requirements</p>

                  <div className="form-grid">
                    <div className="form-group">
                      <label>Budget Range *</label>
                      <div className="custom-dropdown">
                        <div
                          className={`dropdown-header ${formErrors.budget ? 'error' : ''}`}
                          onClick={() => setShowBudgetDropdown(!showBudgetDropdown)}
                        >
                          <span>
                            {formData.budget
                              ? budgetRanges.find(b => b.id === formData.budget)?.name
                              : 'Select budget range'
                            }
                          </span>
                          <ChevronDown size={16} className={showBudgetDropdown ? 'rotated' : ''} />
                        </div>
                        {showBudgetDropdown && (
                          <div className="dropdown-options">
                            {budgetRanges.map((option) => (
                              <div
                                key={option.id}
                                className={`dropdown-option ${formData.budget === option.id ? 'selected' : ''}`}
                                onClick={() => {
                                  setFormData({...formData, budget: option.id})
                                  setShowBudgetDropdown(false)
                                }}
                              >
                                <strong>{option.name}</strong>
                                {formData.budget === option.id && <Check size={16} />}
                              </div>
                            ))}
                          </div>
                        )}
                      </div>
                      {formErrors.budget && <span className="error-message">{formErrors.budget}</span>}
                    </div>

                    <div className="form-group">
                      <label>Timeline *</label>
                      <div className="custom-dropdown">
                        <div
                          className={`dropdown-header ${formErrors.timeline ? 'error' : ''}`}
                          onClick={() => setShowTimelineDropdown(!showTimelineDropdown)}
                        >
                          <span>
                            {formData.timeline
                              ? timelineOptions.find(t => t.id === formData.timeline)?.name
                              : 'Select timeline'
                            }
                          </span>
                          <ChevronDown size={16} className={showTimelineDropdown ? 'rotated' : ''} />
                        </div>
                        {showTimelineDropdown && (
                          <div className="dropdown-options">
                            {timelineOptions.map((option) => (
                              <div
                                key={option.id}
                                className={`dropdown-option ${formData.timeline === option.id ? 'selected' : ''}`}
                                onClick={() => {
                                  setFormData({...formData, timeline: option.id})
                                  setShowTimelineDropdown(false)
                                }}
                              >
                                <div>
                                  <strong>{option.name}</strong>
                                  <p>{option.description}</p>
                                </div>
                                {formData.timeline === option.id && <Check size={16} />}
                              </div>
                            ))}
                          </div>
                        )}
                      </div>
                      {formErrors.timeline && <span className="error-message">{formErrors.timeline}</span>}
                    </div>
                  </div>

                  <div className="form-group full-width">
                    <label>Project Description *</label>
                    <textarea
                      placeholder="Tell us about your project, goals, and any specific requirements..."
                      value={formData.message || ''}
                      onChange={(e) => setFormData({...formData, message: e.target.value})}
                      className={formErrors.message ? 'error' : ''}
                      rows={5}
                    />
                    {formErrors.message && <span className="error-message">{formErrors.message}</span>}
                  </div>

                  <div className="form-group">
                    <label>Priority Level</label>
                    <div className="priority-options">
                      {['low', 'medium', 'high', 'urgent'].map((priority) => (
                        <label key={priority} className="priority-option">
                          <input
                            type="radio"
                            name="priority"
                            value={priority}
                            checked={formData.priority === priority}
                            onChange={(e) => setFormData({...formData, priority: e.target.value})}
                          />
                          <span className={`priority-label priority-${priority}`}>
                            {priority.charAt(0).toUpperCase() + priority.slice(1)}
                          </span>
                        </label>
                      ))}
                    </div>
                  </div>

                  <div className="form-actions">
                    <button type="button" onClick={prevStep} className="btn-prev">
                      <ChevronDown size={16} style={{ transform: 'rotate(90deg)' }} /> Previous
                    </button>
                    <button type="submit" className="btn-submit" disabled={isLoading}>
                      {isLoading ? (
                        <>
                          <ClipLoader size={16} color='white' />
                          Sending...
                        </>
                      ) : (
                        <>
                          <Send size={16} />
                          Send Message
                        </>
                      )}
                    </button>
                  </div>
                </div>
              )}
            </form>
          </div>
        </div>

        <br/>

        <div className='comapaniesList m-block d-flex ' style={{gap:10}}>
          <div style={{padding:20,width:"32%"}} className='text-center'>
            <Image
              src='https://blockchaintechs.io/wp-content/uploads/2022/04/Australia-Harbour.webp'
              alt=''
              width={200}
              height={120}
            />
            <Bold>UK</Bold>
            <PText>M1 1AD, Manchester, United Kingdom <br/>
             +447447823873
            </PText>
          </div>

          <div style={{padding:20,width:"32%"}} className='text-center'>
            <Image
              src='/images/nig.png'
              alt=''
              width={200}
              height={120}
            />
            <Bold>Nigeria</Bold>
            <PText>
                Plot 32 Pasali New Extension, Off Lanto Road, PMB 09 Kuje.FCT. NIGERIA
            </PText>
          </div>

          <div style={{padding:20,width:"32%"}} className='text-center'>
            <Image
              src='https://blockchaintechs.io/wp-content/uploads/2022/04/Dubai-2.webp'
              alt=''
              width={200}
              height={120}
            />
            <Bold>Dubai</Bold>
            <PText>
                Binary Tower, 20th Floor, Office Number 96, Business Bay, Dubai , UAE
            </PText>
          </div>
        </div>
        <br/>

        <div style={{padding:20}} className='d-flex m-block justify-content-between'>
          <LogoWithText textStyle={{fontSize:"larger"}} text='ALTAPPLAP'/>

          <div style={{minWidth:"max-content"}}>
              <b style={{fontWeight:"bolder",fontSize:"large",color:"var(--textColor)"}}>Follow us</b>
              <div className='d-flex'>
                  <div className='d-flex ' style={{maxWidth:"max-content",marginLeft:5}}>
                      <a href="https://web.facebook.com/altapplabs/" target="_blank" rel="noopener noreferrer">
                          <FollowIcon><Facebook size={20} fill='rgb(37, 41, 37)' /></FollowIcon>
                      </a>
                  </div>

                  <div className='d-flex' style={{maxWidth:"max-content",marginLeft:5}}>
                      <a href="https://x.com/altapplabs" target="_blank" rel="noopener noreferrer">
                          <FollowIcon><Twitter size={20} fill='rgb(37, 41, 37)' /></FollowIcon>
                      </a>
                  </div>

                  <div className='d-flex' style={{maxWidth:"max-content",marginLeft:5}}>
                      <a href="https://www.instagram.com/altapplabs" target="_blank" rel="noopener noreferrer">
                          <FollowIcon><Instagram size={20} fill='rgb(37, 41, 37)' /></FollowIcon>
                      </a>
                  </div>

                  <div className='d-flex' style={{maxWidth:"max-content",marginLeft:5}}>
                      <a href="https://www.linkedin.com/company/altapplabs/" target="_blank" rel="noopener noreferrer">
                          <FollowIcon><Linkedin size={20} fill='rgb(37, 41, 37)' /></FollowIcon>
                      </a>
                  </div>

                  <div className='d-flex' style={{maxWidth:"max-content",marginLeft:5}}>
                      <a href="https://discord.gg/havDJNyV" target="_blank" rel="noopener noreferrer">
                          <FollowIcon><FaDiscord size={20} fill='rgb(37, 41, 37)' /></FollowIcon>
                      </a>
                  </div>
              </div>
          </div>
        </div>
        <br/>
        <div className='text-center' style={{borderTop:"1px solid dimgrey"}}>
          <br/><br/>
          © Altapplabs 2025-All Right Reserved
        </div>
      </div>
    </>
  );
};

export default ConnectWithUs;
