import { MDBBtn } from 'mdb-react-ui-kit';
import Image from 'next/image';
import React from 'react'
interface Props{
    images:string[],
    title:string
}
const LargeSkillBox:React.FC<Props>=({images,title})=>{
    return (
        
        <div className='skillBox large' style={{padding:10}}>
     <MDBBtn size='lg' color='dark'>{title}</MDBBtn>   
     <div className='d-flex ' style={{flexFlow:"row wrap"}}>
      {images?.map((e,i)=><Image key={i} src={e}
      width={50} 
     height={50}  alt='' style={{marginLeft:5}}/>)}

</div>
        </div>
    )
}

export default LargeSkillBox;