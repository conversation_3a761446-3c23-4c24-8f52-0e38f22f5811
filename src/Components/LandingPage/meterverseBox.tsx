import Bold from '@/utils/pBolder';
import Image from 'next/image';
import Link from 'next/link';
import React from 'react'
interface Props{
    src:string,
    text:string,
    color?:string,
    href?:string
}
const MeterVerseBox:React.FC<Props>=({src,text,color,href})=>{
    return (
        <Link href={href || "#"}><div className='meterVerseBox text-center'>

<div className='imgContainer'>
    <div className={`shadow ${color ? color:''}`}></div> 
    <Image src={src} alt='' width={500} height={500}/>
    </div>
<b style={{color:"white"}}>{text}</b>
        </div></Link> 
    )
}

export default MeterVerseBox;