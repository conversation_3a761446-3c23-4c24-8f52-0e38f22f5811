import Link from "next/link";
import React from "react";
import { whatsappLink } from "../fixed/sideContact";
import ResponsiveImage from "@/utils/ResposiveImage";
import ColoredHeader from "../../utils/coloredHeader";
import { MDBBtn } from "mdb-react-ui-kit";
import useClientWidth from "@/hooks/useClientWidth";

export default function CryptoComponent() {
    const { width, isClient } = useClientWidth();
  return (
    <>
      <div
        className="Home_crypto_wrapper__qkfC3"
        style={{
          margin: "0px",
          padding: "0px",
          WebkitFontSmoothing: "antialiased",
          boxSizing: "border-box",
          marginTop: "-20px",
          position: "relative",
        }}
      >
        <div
          className="Home_thumb__OP31D d-xl-block d-none"
          style={{
            margin: "0px",
            padding: "0px",
            WebkitFontSmoothing: "antialiased",
            boxSizing: "border-box",
            display: "block",
          }}
        >
          <img
            className="Home_metathumb__sruA8 m-block"
            height={637}
            width={850}
            alt="Metaversethumb"
            src="/images/cryptoex.webp"
            style={{
              margin: "0px",
              padding: "0px",
              WebkitFontSmoothing: "antialiased",
              boxSizing: "border-box",
              verticalAlign: "middle",
              height: "auto",
              bottom: "0px",
              maxWidth: "100%",
              position: "absolute",
              zIndex: 11,
              transform: "translate(-15%, -15%)",
              color: "transparent",
            }}
          />
          
        </div>
        <div
          className="Home_Metaverse_Box__nwzWD"
          style={{
            margin: "0px",
            WebkitFontSmoothing: "antialiased",
            boxSizing: "border-box",
            // background: "rgb(243, 242, 234)",
            overflow: "hidden",
            padding: isClient && width > 900 ? "50px" : 10,
            placeContent: "center",
            display: "grid",
            minHeight: "810px",
            position: "relative",
          }}
        >
          <div
            className="justify-content-end row"
            style={{
              margin: "0px",
              padding: "0px",
              WebkitFontSmoothing: "antialiased",
              boxSizing: "border-box",
              display: "flex",
              flexWrap: "wrap",
              marginLeft: "calc(1.5rem*-.5)",
              marginRight: "calc(1.5rem*-.5)",
              marginTop: "calc(0*-1)",
              justifyContent: "flex-end",
            }}
          >
            <div
              className="col-xl-6"
              style={{
                margin: "0px",
                padding: "0px",
                WebkitFontSmoothing: "antialiased",
                boxSizing: "border-box",
                marginTop: "0",
                maxWidth: "100%",
                paddingLeft: "calc(1.5rem*.5)",
                paddingRight: "calc(1.5rem*.5)",
                flex: "0 0 auto",
                flexShrink: 0,
                width: isClient && width < 900 ? '100%' : "50%",
              }}
            >
              <div
                className="Home_Metaverse_service_box__vV9rJ"
                style={{
                  margin: "0px",
                  padding: "0px",
                  WebkitFontSmoothing: "antialiased",
                  boxSizing: "border-box",
                  position: "relative",
                  zIndex: 111,
                }}
              >
                <ColoredHeader
                  style={{
                    margin: "0px",
                    padding: "0px",
                    WebkitFontSmoothing: "antialiased",
                    boxSizing: "border-box",
                    marginTop: "0px",
                    color: "rgb(2, 22, 57)",
                    fontFamily: '"Fontspring-DEMO-nordeco",sans-serif',
                    fontSize: "clamp(25px, 5vw, 40px)",
                    fontStyle: "normal",
                    fontWeight: 700,
                    lineHeight: "130%",
                    marginBottom: "20px",
                  }}
                >
                  Crypto Exchange Development
                </ColoredHeader>
<br/><br/>
   <p
                  style={{
                    margin: "0px",
                    padding: "0px",
                    WebkitFontSmoothing: "antialiased",
                    boxSizing: "border-box",
                    marginTop: "0px",
                    fontFamily: '"Inter",sans-serif',
                    color: "rgb(2, 22, 57)",
                    fontSize: "15px",
                    fontStyle: "normal",
                    fontWeight: 300,
                    lineHeight: "150%",
                    marginBottom: "40px",
                  }}
                >
                  Being a trailblazer in the blockchain industry, we have lifted
                  many cryptocurrency exchange startups through our top-notch{" "}
                  <Link
                    className="Home_Link_Text__EEyuC"
                    href="/cryptoExchange"
                    style={{
                      margin: "0px",
                      padding: "0px",
                      WebkitFontSmoothing: "antialiased",
                      boxSizing: "border-box",
                      color: "inherit",
                      textDecoration: "underline",
                    }}
                  >
                    cryptocurrency exchange development services.
                  </Link>{" "}
                  At AltAppLabs, we are at the forefront of the blockchain revolution, driving the success of
                   cryptocurrency exchange startups with our top-tier development services. As pioneers in the
                    industry, we introduced bug-free, White Label Cryptocurrency Exchange Software and Bitcoin
                     Exchange Software, setting new standards for quality and reliability.


                </p>
                <div
                  className="Home_Service_List__KQZ1z"
                  style={{
                    margin: "0px",
                    padding: "0px",
                    WebkitFontSmoothing: "antialiased",
                    boxSizing: "border-box",
                  }}
                >
                  <ul
                    style={{
                      margin: "0px",
                      padding: "0px",
                      WebkitFontSmoothing: "antialiased",
                      boxSizing: "border-box",
                      marginBottom: "1rem",
                      marginTop: "0px",
                      listStyle: "none",
                      display: "grid",
                      gridTemplateColumns:
                        "repeat(auto-fill, minmax(250px, 1fr))",
                      paddingLeft: "0px",
                    }}
                  >
                    <li
                      style={{
                        margin: "0px",
                        padding: "0px",
                        WebkitFontSmoothing: "antialiased",
                        boxSizing: "border-box",
                        paddingLeft: "0px",
                        listStyle: "none",
                        color: "rgb(2, 22, 57)",
                        fontSize: "15px",
                        fontStyle: "normal",
                        fontWeight: 400,
                        lineHeight: "150%",
                        marginBottom: "40px",
                        position: "relative",
                      }}
                    >
                      <Link
                        href="/CryptopP2P"
                        target="_blank"
                        style={{
                          margin: "0px",
                          padding: "0px",
                          WebkitFontSmoothing: "antialiased",
                          boxSizing: "border-box",
                          textDecoration: "none",
                          color: "inherit",
                        }}
                      >
                        <span
                          style={{
                            margin: "0px",
                            padding: "0px",
                            WebkitFontSmoothing: "antialiased",
                            boxSizing: "border-box",
                            marginRight: "15px",
                          }}
                        >
                          <img
                            className="img-fluid"
                            height={28}
                            width={28}
                            alt="ServiceIcon"
                            src="/images/chip.svg"
                            style={{
                              margin: "0px",
                              padding: "0px",
                              WebkitFontSmoothing: "antialiased",
                              boxSizing: "border-box",
                              verticalAlign: "middle",
                              height: "auto",
                              maxWidth: "100%",
                              color: "white",
                              background:'white',
                              borderRadius:4

                            }}
                          />
                        </span>
                        P2p Crypto Exchange
                      </Link>
                    </li>
                    <li
                      style={{
                        margin: "0px",
                        padding: "0px",
                        WebkitFontSmoothing: "antialiased",
                        boxSizing: "border-box",
                        paddingLeft: "0px",
                        listStyle: "none",
                        color: "rgb(2, 22, 57)",
                        fontSize: "15px",
                        fontStyle: "normal",
                        fontWeight: 400,
                        lineHeight: "150%",
                        marginBottom: "40px",
                        position: "relative",
                      }}
                    >
                      <Link
                        href="/CentralizedExchange"
                        target="_blank"
                        style={{
                          margin: "0px",
                          padding: "0px",
                          WebkitFontSmoothing: "antialiased",
                          boxSizing: "border-box",
                          textDecoration: "none",
                          color: "inherit",
                        }}
                      >
                        <span
                          style={{
                            margin: "0px",
                            padding: "0px",
                            WebkitFontSmoothing: "antialiased",
                            boxSizing: "border-box",
                            marginRight: "15px",
                          }}
                        >
                          <img
                            className="img-fluid"
                            height={28}
                            width={28}
                            alt="ServiceIcon"
                            src="/images/chip.svg"
                            style={{
                              margin: "0px",
                              padding: "0px",
                              WebkitFontSmoothing: "antialiased",
                              boxSizing: "border-box",
                              verticalAlign: "middle",
                              height: "auto",
                              maxWidth: "100%",
                              color: "transparent",
                              
                              background:'white',
                              borderRadius:4
                            }}
                          />
                        </span>
                        Centralized Exchange
                      </Link>
                    </li>
                    <li
                      style={{
                        margin: "0px",
                        padding: "0px",
                        WebkitFontSmoothing: "antialiased",
                        boxSizing: "border-box",
                        paddingLeft: "0px",
                        listStyle: "none",
                        color: "rgb(2, 22, 57)",
                        fontSize: "15px",
                        fontStyle: "normal",
                        fontWeight: 400,
                        lineHeight: "150%",
                        marginBottom: "40px",
                        position: "relative",
                      }}
                    >
                      <Link
                        href="/DecentralizedExchange"
                        target="_blank"
                        style={{
                          margin: "0px",
                          padding: "0px",
                          WebkitFontSmoothing: "antialiased",
                          boxSizing: "border-box",
                          textDecoration: "none",
                          color: "inherit",
                        }}
                      >
                        <span
                          style={{
                            margin: "0px",
                            padding: "0px",
                            WebkitFontSmoothing: "antialiased",
                            boxSizing: "border-box",
                            marginRight: "15px",
                          }}
                        >
                          <img
                            className="img-fluid"
                            height={28}
                            width={28}
                            alt="ServiceIcon"
                            src="/images/chip.svg"
                            style={{
                              margin: "0px",
                              padding: "0px",
                              WebkitFontSmoothing: "antialiased",
                              boxSizing: "border-box",
                              verticalAlign: "middle",
                              height: "auto",
                              maxWidth: "100%",
                              color: "transparent",
                              
                              background:'white',
                              borderRadius:4
                            }}
                          />
                        </span>
                        Decentralized Exchange
                      </Link>
                    </li>
                    <li
                      style={{
                        margin: "0px",
                        padding: "0px",
                        WebkitFontSmoothing: "antialiased",
                        boxSizing: "border-box",
                        paddingLeft: "0px",
                        listStyle: "none",
                        color: "rgb(2, 22, 57)",
                        fontSize: "15px",
                        fontStyle: "normal",
                        fontWeight: 400,
                        lineHeight: "150%",
                        marginBottom: "40px",
                        position: "relative",
                      }}
                    >
                      <Link
                        href="/HyperledgerBlockchain"
                        target="_blank"
                        style={{
                          margin: "0px",
                          padding: "0px",
                          WebkitFontSmoothing: "antialiased",
                          boxSizing: "border-box",
                          textDecoration: "none",
                          color: "inherit",
                        }}
                      >
                        <span
                          style={{
                            margin: "0px",
                            padding: "0px",
                            WebkitFontSmoothing: "antialiased",
                            boxSizing: "border-box",
                            marginRight: "15px",
                          }}
                        >
                          <img
                            className="img-fluid"
                            height={28}
                            width={28}
                            alt="ServiceIcon"
                            src="/images/chip.svg"
                            style={{
                              margin: "0px",
                              padding: "0px",
                              WebkitFontSmoothing: "antialiased",
                              boxSizing: "border-box",
                              verticalAlign: "middle",
                              height: "auto",
                              maxWidth: "100%",
                              color: "transparent",
                              
                              background:'white',
                              borderRadius:4
                            }}
                          />
                        </span>
                        Hyper ledger Blockchain
                      </Link>
                    </li>
                  </ul>
                  <div
                    className="Home_service_btn_grp__C7SCg"
                    style={{
                      margin: "0px",
                      padding: "0px",
                      WebkitFontSmoothing: "antialiased",
                      boxSizing: "border-box",
                      gap: "50px",
                      display: "flex",
                      flexWrap: "wrap",
                      marginTop: "25px",
                      position: "relative",
                      zIndex: 11,
                    }}
                  >
                    <MDBBtn color='success' size='lg' style={{width: isClient && width < 900 ? "100%" : "50%"}}>
                     <Link href={whatsappLink}> Create your Exchange</Link>

                    </MDBBtn>
                    </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
}
