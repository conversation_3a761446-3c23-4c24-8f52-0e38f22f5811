import Bold from '@/utils/pBolder';
import PText from '@/utils/pText';
import { IconButton } from '@mui/material';
import Image from 'next/image';
import React, { useEffect, useState } from 'react'
import { FaAngleLeft, FaAngleRight } from 'react-icons/fa';
/**Our Client's Testimonials
"They quickly accommodated any changes and feedback"

So far, AltAppLabs has successfully delivered everything we asked for. They’ve been outstandingly efficient and swift, understanding the assignment well and being transparent about their capabilities.

"They are professional and friendly at the same time."

It is glad to work with AltAppLabs. They delivered the product exactly what we discussed; their timely response is appreciated. Good team to work with, so thankful in the ways they supported me in making my dream to reality. Good luck for your all upcoming works

 */
interface Props{
    nextClick:()=>void;
    prevClick:()=>void;
    image:string,
    text:string;
    title:string;
    currentIndex:number,
    length:number
}
const TestimonialsCard:React.FC<Props>=(prop)=>{
    const [animation,setAnimation]=useState("");
    return (
        <>
        <div className='TestimonialsCard' style={{animationName:animation}}>
<div className='d-flex align-items-center justify-content-between'>
<Image src={prop.image} width={100} height={100} alt=''/>
<div className='textContainer'>
    <b>{prop.title}</b>
    <PText>
{prop.text}
    </PText>
    
</div>


</div>

<div style={{width:'100%'}} className='d-flex justify-content-end'>
        <div className='d-flex align-items-center justify-content-end' style={{width:200,}}>
            <IconButton style={{color:"white",background:"var(--green)"}}  onClick={()=>{
                prop.prevClick()
setAnimation("swipeLeft");

setTimeout(()=>{
    setAnimation("");
},1000)
            }} ><FaAngleLeft/> </IconButton>
            
            <span style={{fontWeight:"bolder",color:"white",display:'block',padding:"0px 10px"}}>{prop.currentIndex}/{prop.length}</span>
            
            <IconButton style={{color:"white",background:"var(--green)"}} onClick={()=>{
                prop.nextClick()
setAnimation("swipeRight");
setTimeout(()=>{
    setAnimation("");

},1000)
            
            }}><FaAngleRight/> </IconButton>
            
        </div>
    </div>




        </div>
        </>
    )
}

export default TestimonialsCard;