/* Advanced Contact Form Styles */
.advanced-form {
  background: linear-gradient(135deg, rgba(2, 11, 18, 0.95) 0%, rgba(22, 19, 30, 0.95) 100%);
  border-radius: 20px;
  padding: 40px;
  margin: 40px auto;
  max-width: 800px;
  box-shadow: 0 20px 60px rgba(0, 212, 108, 0.1);
  border: 1px solid rgba(0, 212, 108, 0.2);
  position: relative;
  overflow: hidden;
}

.advanced-form::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, var(--green), var(--blue), var(--green));
  background-size: 200% 100%;
  animation: shimmer 3s ease-in-out infinite;
}

@keyframes shimmer {
  0% { background-position: -200% 0; }
  100% { background-position: 200% 0; }
}

/* Progress Indicator */
.form-progress {
  margin-bottom: 40px;
}

.progress-steps {
  display: flex;
  justify-content: space-between;
  margin-bottom: 20px;
  position: relative;
}

.progress-step {
  display: flex;
  flex-direction: column;
  align-items: center;
  flex: 1;
  position: relative;
}

.progress-step::after {
  content: '';
  position: absolute;
  top: 20px;
  left: 50%;
  width: 100%;
  height: 2px;
  background: rgba(255, 255, 255, 0.1);
  z-index: 1;
}

.progress-step:last-child::after {
  display: none;
}

.progress-step.completed::after {
  background: var(--green);
}

.step-number {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.1);
  border: 2px solid rgba(255, 255, 255, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  color: var(--textColor);
  transition: all 0.3s ease;
  position: relative;
  z-index: 2;
}

.progress-step.active .step-number {
  background: var(--green);
  border-color: var(--green);
  color: white;
  transform: scale(1.1);
}

.progress-step.completed .step-number {
  background: var(--green);
  border-color: var(--green);
  color: white;
}

.step-label {
  margin-top: 10px;
  font-size: 12px;
  color: var(--textColor);
  opacity: 0.7;
  text-align: center;
}

.progress-step.active .step-label {
  opacity: 1;
  color: var(--green);
  font-weight: 600;
}

.progress-bar {
  width: 100%;
  height: 4px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 2px;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, var(--green), var(--blue));
  transition: width 0.5s ease;
  border-radius: 2px;
}

/* Form Steps */
.form-step {
  animation: slideIn 0.5s ease-out;
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateX(20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

.step-title {
  display: flex;
  align-items: center;
  gap: 12px;
  color: var(--textColor);
  font-size: 24px;
  font-weight: 700;
  margin-bottom: 8px;
}

.step-title svg {
  color: var(--green);
}

.step-description {
  color: var(--textColor);
  opacity: 0.8;
  margin-bottom: 30px;
  font-size: 16px;
}

/* Form Grid */
.form-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
  margin-bottom: 30px;
}

.form-group {
  display: flex;
  flex-direction: column;
}

.form-group.full-width {
  grid-column: 1 / -1;
}

.form-group label {
  color: var(--textColor);
  font-weight: 600;
  margin-bottom: 8px;
  font-size: 14px;
}

.form-group input,
.form-group textarea {
  background: rgba(255, 255, 255, 0.05);
  border: 2px solid rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  padding: 16px;
  color: var(--textColor);
  font-size: 16px;
  transition: all 0.3s ease;
  outline: none;
}

.form-group input:focus,
.form-group textarea:focus {
  border-color: var(--green);
  background: rgba(0, 212, 108, 0.05);
  box-shadow: 0 0 0 4px rgba(0, 212, 108, 0.1);
}

.form-group input.error,
.form-group textarea.error {
  border-color: #ff4444;
  background: rgba(255, 68, 68, 0.05);
}

.form-group input::placeholder,
.form-group textarea::placeholder {
  color: rgba(215, 221, 226, 0.5);
}

.error-message {
  color: #ff4444;
  font-size: 12px;
  margin-top: 4px;
  display: flex;
  align-items: center;
  gap: 4px;
}

/* Services Grid */
.services-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 16px;
  margin-bottom: 30px;
}

.service-card {
  background: rgba(255, 255, 255, 0.03);
  border: 2px solid rgba(255, 255, 255, 0.1);
  border-radius: 16px;
  padding: 20px;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 16px;
  position: relative;
}

.service-card:hover {
  border-color: var(--green);
  background: rgba(0, 212, 108, 0.05);
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 212, 108, 0.15);
}

.service-card.selected {
  border-color: var(--green);
  background: rgba(0, 212, 108, 0.1);
  box-shadow: 0 0 0 4px rgba(0, 212, 108, 0.1);
}

.service-icon {
  width: 48px;
  height: 48px;
  background: linear-gradient(135deg, var(--green), var(--blue));
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  flex-shrink: 0;
}

.service-content h4 {
  color: var(--textColor);
  font-size: 16px;
  font-weight: 600;
  margin: 0 0 4px 0;
}

.service-content p {
  color: var(--textColor);
  opacity: 0.7;
  font-size: 14px;
  margin: 0;
}

.service-check {
  position: absolute;
  top: 12px;
  right: 12px;
  width: 24px;
  height: 24px;
  background: var(--green);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  opacity: 0;
  transform: scale(0);
  transition: all 0.3s ease;
}

.service-card.selected .service-check {
  opacity: 1;
  transform: scale(1);
}

/* Custom Dropdown */
.custom-dropdown {
  position: relative;
}

.dropdown-header {
  background: rgba(255, 255, 255, 0.05);
  border: 2px solid rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  padding: 16px;
  color: var(--textColor);
  cursor: pointer;
  display: flex;
  justify-content: space-between;
  align-items: center;
  transition: all 0.3s ease;
}

.dropdown-header:hover {
  border-color: var(--green);
  background: rgba(0, 212, 108, 0.05);
}

.dropdown-header.error {
  border-color: #ff4444;
  background: rgba(255, 68, 68, 0.05);
}

.dropdown-header svg {
  transition: transform 0.3s ease;
}

.dropdown-header svg.rotated {
  transform: rotate(180deg);
}

.dropdown-options {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background: rgba(2, 11, 18, 0.98);
  border: 2px solid rgba(0, 212, 108, 0.2);
  border-radius: 12px;
  margin-top: 4px;
  z-index: 1000;
  max-height: 300px;
  overflow-y: auto;
  backdrop-filter: blur(10px);
  box-shadow: 0 10px 40px rgba(0, 0, 0, 0.3);
}

.dropdown-option {
  padding: 16px;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid rgba(255, 255, 255, 0.05);
}

.dropdown-option:last-child {
  border-bottom: none;
}

.dropdown-option:hover {
  background: rgba(0, 212, 108, 0.1);
}

.dropdown-option.selected {
  background: rgba(0, 212, 108, 0.15);
  color: var(--green);
}

.dropdown-option strong {
  color: var(--textColor);
  font-size: 14px;
}

.dropdown-option p {
  color: var(--textColor);
  opacity: 0.7;
  font-size: 12px;
  margin: 4px 0 0 0;
}

/* Priority Options */
.priority-options {
  display: flex;
  gap: 12px;
  flex-wrap: wrap;
}

.priority-option {
  cursor: pointer;
}

.priority-option input {
  display: none;
}

.priority-label {
  display: inline-block;
  padding: 8px 16px;
  border-radius: 20px;
  font-size: 14px;
  font-weight: 600;
  transition: all 0.3s ease;
  border: 2px solid transparent;
}

.priority-low {
  background: rgba(34, 197, 94, 0.1);
  color: #22c55e;
  border-color: rgba(34, 197, 94, 0.2);
}

.priority-medium {
  background: rgba(251, 191, 36, 0.1);
  color: #fbbf24;
  border-color: rgba(251, 191, 36, 0.2);
}

.priority-high {
  background: rgba(249, 115, 22, 0.1);
  color: #f97316;
  border-color: rgba(249, 115, 22, 0.2);
}

.priority-urgent {
  background: rgba(239, 68, 68, 0.1);
  color: #ef4444;
  border-color: rgba(239, 68, 68, 0.2);
}

.priority-option input:checked + .priority-label {
  transform: scale(1.05);
  box-shadow: 0 0 0 4px rgba(0, 212, 108, 0.1);
}

/* Form Actions */
.form-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 40px;
  gap: 16px;
}

.btn-prev,
.btn-next,
.btn-submit {
  padding: 16px 32px;
  border-radius: 12px;
  font-weight: 600;
  font-size: 16px;
  border: none;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 8px;
  outline: none;
}

.btn-prev {
  background: rgba(255, 255, 255, 0.1);
  color: var(--textColor);
  border: 2px solid rgba(255, 255, 255, 0.2);
}

.btn-prev:hover {
  background: rgba(255, 255, 255, 0.15);
  transform: translateY(-2px);
}

.btn-next,
.btn-submit {
  background: linear-gradient(135deg, var(--green), var(--blue));
  color: white;
  border: 2px solid transparent;
  box-shadow: 0 4px 15px rgba(0, 212, 108, 0.3);
}

.btn-next:hover,
.btn-submit:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 212, 108, 0.4);
}

.btn-submit:disabled {
  opacity: 0.7;
  cursor: not-allowed;
  transform: none;
}

/* Responsive Design */
@media (max-width: 768px) {
  .advanced-form {
    padding: 24px;
    margin: 20px;
  }
  
  .form-grid {
    grid-template-columns: 1fr;
    gap: 16px;
  }
  
  .services-grid {
    grid-template-columns: 1fr;
  }
  
  .service-card {
    padding: 16px;
  }
  
  .form-actions {
    flex-direction: column;
  }
  
  .btn-prev,
  .btn-next,
  .btn-submit {
    width: 100%;
    justify-content: center;
  }
  
  .priority-options {
    justify-content: center;
  }
  
  .step-title {
    font-size: 20px;
  }
  
  .progress-steps {
    flex-direction: column;
    gap: 20px;
  }
  
  .progress-step::after {
    display: none;
  }
}
