
import TechnologyItem from '@/utils/technologyItem';
import Link from 'next/link';
import React, { useEffect, useRef } from 'react';
import AliceCarousel from 'react-alice-carousel';
import 'react-alice-carousel/lib/alice-carousel.css';

const TextScroll: React.FC = () => {

    const items = [
        
    { name: "Centralized exchange", href: "/CentralizedExchange" },
    { name: "Margin Trading", href: "/MarginTrading" },
    { name: "Crypto Market Making", href: "/CryptoMarketMaking" },
    { name: "Crypto Arbitrage Bot", href: "/CryptoArbitrageBot" },
    { name: "Crypto Arbitrage Bot", href: "/CryptoArbitrageBot" },
    { name: "Bot Development", href: "/BotDevelopment" },
        { name: "Blockchain", href: "/blockchainAppDevelopment" },
    { name: "Blockchain App Development", href: "/blockchainAppDevelopment" },
    { name: "Blockchain Digital Certificate", href: "/blockchainDigitalCertificationServices" },
    { name: "Private Blockchain Development", href: "/privateServices" },
    { name: "Smart Contract Development", href: "/smartContract" },
    { name: "Blockchain POC Development", href: "/pocDevelopment" },
    { name: "Blockchain DApps Development", href: "/DAppsDevelopment" },
    { name: "Smart Contract Audit", href: "/smartAudit" },
    { name: "Blockchain Airdrop", href: "/Airdrops" },
    { name: "Hyperledger Blockchain Development Company", href: "/HyperledgerBlockchain" },
    { name: "Crypto Wallet Development", href: "/CryptoWalletDev" },
    { name: "Crypto Payment Gateway Development", href: "/PayementGateway" },
    { name: "Crypto Exchange Platform Development", href: "/cryptoExchange" },
    { name: "Crypto Cross Border Payment", href: "/CryptoBorder" },
    { name: "Crypto Portfolio Management", href: "/CryptoPortfolio" },
    { name: "Crypto Trading Bots Development", href: "/CryptoTrading" },
    { name: "Crypto Digital Marketing", href: "/CryptoDigital" },
    { name: "Cryptocurrency Development", href: "/CryptoDevelopment" },
    { name: "Crypto P2P Payment Development", href: "/CryptopP2P" },
    { name: "Best Platforms For Launching An ICO", href: "/IOCDevelopment" },
    { name: "ICO Development", href: "/IOCDevelopment" },
    { name: "DeFi App Development", href: "/DefiAppDevelopement" },
    { name: "DeFi Development", href: "/DefiBorrowing" },
    { name: "Crypto currency exchange", href: "/cryptoExchange" },
    { name: "P2P Crypto Exchange", href: "/P2PCryptoExchange" },
    { name: "Derivatives exchange", href: "/DerivativesExchange" },
    { name: "White label exchange ", href: "/WhiteLabelExchange" },
    { name: "Decentralized exchange", href: "/DecentralizedExchange" }
    ]
    const containerRef = useRef<HTMLDivElement>(null);

    useEffect(() => {
        const container = containerRef.current;
        if (!container) return;

        const scrollWidth = container.scrollWidth;
        let scrollLeft = 0;
        let scrollSpeed = 0.5; // Adjust the scrolling speed as needed

        const scrollInterval = setInterval(() => {
            scrollLeft += scrollSpeed;
            container.scrollTo(scrollLeft, 0);

            if (scrollLeft >= scrollWidth - container.clientWidth) {
                // Reached the end, scroll back to the start
                scrollLeft = 0;
                container.scrollTo(scrollLeft, 0);
            }
        }, 5); // Adjust the interval as needed

        return () => clearInterval(scrollInterval);
    }, []);


    return (
        <div style={{ height: 80, overflow: "hidden" }}>
            <div
                ref={containerRef}
                className='d-flex'
                style={{ overflow: "hidden", paddingBottom: 20, width: '90%', maxWidth: '100vw', margin: '0 auto' }}
            >
                {items.map((item: any, i: number) => (
                    <div className='scrollItem d-flex align-items-center justify-content-center' key={i}>
                        <div>
                            <Link href={item.href}><span style={{color:"lightgrey"}}>{item.name}</span></Link>
                        </div>
                    </div>
                ))}
            </div>
        </div>
        );
};

export default TextScroll;
