.presenter{
    width:100%;

}
.presenter-container {
padding-top: 100px !important;
}
/* Custom CSS */
@media (max-width: 767px) {
  .news_container_outer{
    flex-flow: column-reverse wrap !important;
    justify-content: 
    center !important;
  }
  
  .news_container_outer .appIcon{
    display: none !important;
  }
  .news_container_outer > div{
    width:100% !important;
    margin: 0 auto !important;
    margin-top: 16px;
 text-align: center;

  }
 .presenter-container .presenter{
display:block !important;
  }
  
 .presenter-container .presenter > div{
  display:block !important;
  width:100% !important;
  max-width: 100% !important;
    }

    .presenter-container {
      display: block;
      padding:16px;
      width: 100%;
    }
  }
  
  @media (min-width: 768px) {
.presenter-container .imageContainer{
    margin-top:-40px;
}
    .presenter-container {
      display: flex;
      align-items: center;
      padding:70px;
    }
  }
  
  
  .gradientBox {
    border: 3px solid transparent;
    border-image: linear-gradient(180deg, var(--green),var(--blue), var(--bgColor));
    border-image-slice: 1;
    padding:10px;

    border-radius: 20px !important;
    margin-left: 10px;
    overflow: hidden;

  }
  .gradientBoxUp{
  
  
    border: 3px solid transparent;
    border-image: linear-gradient(360deg, var(--green),var(--blue), var(--bgColor));
    border-image-slice: 1;
    padding:10px;

    border-radius: 20px !important;
    margin-left: 10px;
    overflow: hidden;
}  
  @media screen and (max-width:750px){
  .gradientBox,.gradientBoxUp {
width:90% !important;
margin:0 auto;    
margin-top:10px;

  }

  .Boxs{
  display: block !important;    
  }

  }
  .gradientBox,.gradientBoxUp {
    /* Set the size and position of the box */
    width: 29%;
    min-height: 100px;
  }
  .gradientBox b,.gradientBoxUp b{
color:rgb(239, 238, 243);
font-weight: bolder;
  font-size: large;}

  
  .gradientBox p,.gradientBoxUp p{
    color:rgb(239, 238, 243);
    font-weight: bolder;
    }
    .AwardCard{
      width:90%;
      margin:0 auto;
      min-height: 400px;
      border-radius:20px;
      background: linear-gradient(130deg,rgb(2,28,34),rgb(2,28,34),rgba(0,144,81,255));
padding:20px;
    }
    .AwardCard .awardColorContainer{
border-radius:20px;
border:1px solid white;
height: 100%;
padding: 16px !important;
    }
.awardImagesContainer{
  display: flex;
  max-width: max-content;
  flex-flow:row wrap;
margin:0 auto;  
padding:10px;
}
@media (max-width:900px) {
.awardImagesContainer{
  width:95%;
  margin:0 auto;
}
.awardImagesContainer > div{
  margin:0 auto;
}
}
.awardImage {
  width: 150px;
  cursor:pointer;
  height: 150px;
  filter: grayscale(100%); /* Apply grayscale filter by default */
  transition: all 0.3s ease; /* Add transition for smooth effect */
}
@media (max-width:600px) {
  .awardImage {
width: 80px;
height: 80px;
  }
}
.awardImage:hover {
  filter: none; /* Remove filter on hover */
transform: translate(0px,-10px);
}


.animatedImageContainer{
  overflow: visible;
  width:100%;
height:400px;

}

@media (max-width:700px) {
  .animatedImageContainer{
height:400px;

    width:100%;
  }
    
}
.animatedImageContainer img{
transition:0.6 ease-in-out;
transition-duration: 800ms;
width:400px;
height:400px;
max-width: 90vw;
}

.animatedImageContainer:hover > img{
width:500px;
height:600px;

}


.buildMeterverse{
  width:95%;
  margin:0 auto;
  
}

.buildMeterverse .animatedImageEL{
width:85%;
height:85%;
animation-name: fadeInAndOut;
animation-duration: 3s;
animation-iteration-count: infinite;
}
@keyframes fadeInAndOut {
  0%{
    transform: translate(-0px,-0px);
  }
  50%{    transform: translate(-0px,-10px);

  }
  100%{
    transform: translate(-0px,-0px);
  }
}
@media (max-width:750px) {

.meterVerseBox{
 min-width: 70vw !important;

}

.buildMeterverse{
  flex-flow: column-reverse wrap !important;
}
.buildMeterverse > div {
 width:95% !important;
 margin: 0 auto;
}
.meterVerseContainer{
display:block !important;
padding:20px 10px ;
}
.meterVerseContainer .meterVerseBox{
  width: 100%;
  margin:0 !important;
  margin-top: 20px !important;
}
}



@media (max-width:700px) {
  .buttonContainer{
  margin:0 auto !important;
  width:80% !important;
  justify-content: center !important;
  }
 .buttonContainer *{
    text-align: center !important;
    
  }
  
.buttonContainer button{
   width:100% !important;
   text-align: center !important;
   margin:0 auto !important;
   min-width:100% !important;
  }
  
  .buildMeterverse .animatedImageEL{
    width:100% !important;
    display: block;
    margin: 0 auto;
    height:100% !important;
  }
  
  }
  


.meterVerseContainer{
max-width: max-content;
margin: 0 auto;
}
.meterVerseBox{
  border-radius: 20px;
  width:200px;
 margin-left: 20px; 
  height:170px;
  background:linear-gradient(30deg,rgba(4,19,29),rgba(4,19,29));
box-shadow:1px 1px 10px 0px rgb(10, 10, 10);
--green:rgba(0,225,140);
padding:16px;
}
.meterVerseBox .imgContainer{
 width:100px;
 position: relative;
 height:100px;
 margin:0 auto;
  transform: translate(0px,-30px);
}
.meterVerseBox .imgContainer div,.meterVerseBox .imgContainer img{
width:100%;
height: 100%;
position: absolute;
top:0;
z-index:1;
left:0;
}

.meterVerseBox .imgContainer div {
  position: absolute; /* Position the div absolutely */
  top: 50%; /* Move to 50% from the top */
  left: 50%; /* Move to 50% from the left */
  transform: translate(-50%, -50%); /* Center it precisely */
  z-index: -1;
  border-radius: 50%;
  width: 10px;
  margin-top:10px;

  height: 10px;
  background:rgba(0, 225, 139, 0.63);
  box-shadow: 1px 1px 10px 5px rgba(0, 225, 139, 0.63),
  1px 1px 50px 40px rgba(0, 225, 139, 0.63)
   !important;
}
.meterVerseBox .imgContainer div.blue {
  background:rgba(0, 191, 225, 0.63);
  box-shadow: 1px 1px 10px 5px rgba(0, 225, 214, 0.63),
  1px 1px 50px 40px rgba(0, 199, 225, 0.63)
   !important;

}

.meterVerseBox b{
  transform: translate(0px,-10px) !important;
  display: block;

}
.meterverseBox{
  max-width: 30vw;
}
.meterverseBox *{
  text-align: start !important;
}
.spaceManImage{
  width:100% !important;
  height:400px !important;
}
.spaceManImage img{
width: 100% !important;
height: 100% !important;

}
@media (max-width:700px) {
  .discoverMeterverse{
    display: block !important;

  }
  .discoverMeterverse > *{
    width:100%;
text-align: center;
  }
  .discoverMeterverse .meterverseBox{
    display:block !important;
    width:100% !important;
    max-width:100% !important;

  }
  .discoverMeterverse .meterverseBox > *{
    margin: 0 auto;
    text-align: center;
  }
  .discoverMeterverse .meterverseBox  *{
    text-align: center !important;
    width: 100%;
  }

}


.smallLargeContainer{
  gap:10px;
padding:22px;
margin:0 auto;
max-width: 92%;

}
.smallLargeContainer .smallComponentsContainer{
  width:60%;
  min-height:200px;
}

.smallLargeContainer .largeComponentsContainer{
  width:40%;
  min-height:200px;
}

@media (max-width:700px) {
 
.smallLargeContainer{
display: block !important;
padding:17px;
}
.smallLargeContainer > div{
width:100% !important;
margin-top:20px;
}
.skillBox{
  width:100% !important;
}
.skillBox div{
 max-width: max-content !important;
 margin: 0 auto !important; 
}
.skillBox div img{
  text-align: center !important;
}
}

.skillBox{
  width:46%;
  min-height:100px;
  border: 1px solid var(--textColor);
  border-radius: 20px;
text-align: center;
margin:10px;
}
.skillBox.large{
width:100%;
}
.skillBox.large img{
  width:100px !important;
  height:100px !important;

  }
  @media (max-width:800px) {
    .skillBox{
      margin-left: 0px !important;
    }
  }
.skillBox button{
  box-shadow: 1px 1px 10px 0px rgb(15, 22, 15);
  background:rgb(8, 11, 14) !important;
  color:rgb(36, 196, 36);
  border-radius:10px;
  width:80%;
  margin:0 auto;
  font-weight: bolder;
  padding:16px;
  transform:translate(0px,-20px);
}

.yourBusiness{
  max-width: 95%;
  margin: 0 auto;
}
.yourBusiness img{
  width:100% !important;
  height: 100% !important;
}
@media (max-width:700px) {
  *.m-block{
    display: block !important;
  }
  *.m-block > div{
    width:96% !important;
    margin: 0 auto !important;
  }
  .yourBusiness{
    display: block !important;
  }
  .yourBusiness button{
    width:100% !important;
    margin:0 auto !important;
  }
.yourBusiness div{
width:95% !important;
margin:0 auto;
}
.whyChooseContainer{
  flex-flow: column-reverse wrap!important;
}

.whyChooseContainer > div{
  width:95% !important;
margin:0 auto;
}

}
.whyChooseContainer{
  max-width:95%;
  margin:0 auto;
}
.whyChooseContainer .imageContainer img{
width:100%;
height:100%;
max-height: 600px;
animation-name: fadeInAndOut;
animation-duration: 3s;
animation-iteration-count: infinite;
}
.whyChooseContainer .imageContainer{
  width:40%;
  min-height: 100px;
}
.whyChooseContainer .contentContainer{
width:60%;
min-height: 100px;
}
.whyChooseContainer  .whyChooseUsItem b{
  color:var(--green);
}
.whyChooseContainer .whyChooseUsItem{
border-radius: 20px;
box-shadow: 1px 1px 10px 0px black;
width:95% !important;
margin:0 auto !important;
padding: 26px;
margin-top: 10px !important;
transition:all 0.6 ease

}
.whyChooseContainer .whyChooseUsItem.active{
background:linear-gradient(var(--green),rgb(120, 231, 203),var(--green));
}
.whyChooseContainer .whyChooseUsItem.active  *{
  font-weight: bolder;
  text-align: start !important;
  color:var(--bgColor) !important;
}
@media (max-width:700px) {
.TestimonialsCard > div{
display: block !important;  
}
.TestimonialsCard >div > div{
  width:100% !important;
}
.TestimonialsCard > div img{
  margin:0 auto;
  display: block;
  
}
.TestimonialsCard .textContainer{
padding-top: 30px;
}
.TestimonialsCard b{
display: block;
  }
}
.TestimonialsCard{
  width:95%;
  margin:0 auto;
  border-radius: 10px;
  background:#264e3b6e;
  box-shadow: 1px 1px 10px 0px rgba(38, 78, 59, 0.144);
  min-height:200px;
  max-width: 900px;
  padding:20px;
  animation-duration:800ms;

}

@keyframes swipeLeft {
  0%{
    transform: translate(-100vw,0px);
  }
  100%{
    transform:translate(0px,0px);
  }
  
}

@keyframes swipeRight {
  0%{
    transform: translate(100vw,0px);
  }
  100%{
    transform:translate(0px,0px);
  }
  
}
.TestimonialsCard img{
  border-radius:50%;
  width:150px;
  height: 150px;
  box-shadow: 1px 1px 10px 0px rgba(7, 197, 109, 0.945);

}

.TestimonialsCard .textContainer{
  padding-left:20px;
}
.TestimonialsCard .textContainer b{
  padding-left:32px;
  font-size: large;
  color:var(--textColor);
}
.TestimonialsCard .textContainer *{
text-align: start !important;
}

/* Testimonials section spacing */
.testimonials {
  padding: 40px 0;
  margin-bottom: 20px;
}

/* Connect with us section spacing */
.connectWithUs {
  padding-top: 20px;
  margin-top: 20px;
}

.blogCard{
  border-radius: 10px;
  box-shadow: 1px 1px 10px 0px rgb(17, 10, 10);
  max-width: 400px;
  background:rgb(10, 27, 24) !important

}
.blogCard .img-container{
  width: 100%;
  min-height:200px;
  position: relative;
}
.blogCard.large img{
min-height:360px;
max-height:400px !important;

}


.blogCard button{
  box-shadow: 1px 1px 10px 0px rgb(17, 10, 10) !important;
}
.blogCard .blogCardTextContainer{
  padding:20px;
}
.blogCard .blogCardTextContainer *{
  text-align: start !important;
}
.blogsLists{
flex-flow: row wrap !important;
}
@media (max-width:700px) {
.blogsLists{
 width:100% !important;
}

.blogsLists .blogCard{
width:100% !important;
} 

.BlogMainContainer{
  display: block !important;
}
.BlogMainContainer div{
  width:100% !important;
}
}


.contactCard {
  margin-top:10px;
  justify-content: flex-start !important;
}

.contactCard span:nth-child(1){
  color:rgb(227, 227, 236);
}

.contactCard span:nth-child(2){
  color:rgb(248, 240, 240);
}

.form input,.form textarea{
  padding:16px;
  border-radius: 10px;
  background:linear-gradient(30deg,rgb(37, 41, 37),rgb(10, 27, 24));
  margin:20px;
  outline:none;
  color:white;
  border:none;
} 
@media (max-width:700px) {
.form input,.form textarea{
 margin-left: 0px !important;
 width:100%;
 min-width: 100% !important;
}
.form button{
  width:100% !important;
  min-width:100% !important;
 margin-left: 0px !important;
  max-width:100% !important;
}

}
.form input::placeholder,.form textarea::placeholder{
color:var(--textColor)
}

@media (min-width:700px) {
.comapaniesList *{
  text-align: start !important;
  } 
}
  
.comapaniesList img{
  margin-left:20px;
}
.comapaniesList h3{
  padding-left: 23px !important;
}


.BlogMainContainer .buttonContainer *{
  text-align:center !important;
  }
.BlogMainContainer .buttonContainer button:hover{
  color:var(--green) !important;
}
.BlogMainContainer .buttonContainer button:active{
  border-bottom: 1px solid var(--green);
}


.buttonContainer .text-center{
  text-align: center !important;
}
.scrollItem{
  height:80px;
  
padding:5px;
min-width:max-content;
width:max-content;
box-sizing: padding-box;
}

.scrollItem div{
  border-radius: 3px;
  border:2px solid var(--yellow);
  padding:8px 15px !important;
  border-radius: 10px;
  margin-left:3px;
  min-width:max-content;
 word-break: keep-all !important;
  word-wrap: keep-all; 
}

.scrollItem span{
 font-weight: bold;
 font-size: xx-large;
}
@media (max-width:600px) {
.scrollItem span{
 font-size:medium;
}

}
.scrollItem span:hover{
  cursor:pointer;
}
.scrollItem div a{
  color:rgb(237, 235, 243);
}

.explore_card_section{
  background:rgb(4, 22, 36) !important;
  margin:0 auto;
  margin-top:20px !important;
}
.explore_card_section h3{
  font-family: system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
  
}
@media (max-width:800px) {
  .explore_card_section{
    width:49% !important; 
   }
   .explore_card_section{
    width:90% !important; 
   }
      
}

.explore_card_section .exploreImage {
  filter: hue-rotate(33deg) saturate(100%);
}
.Home_crypto_wrapper__qkfC3 *{
  color:rgb(243, 246, 250) !important;
  z-index:1 !important;
}