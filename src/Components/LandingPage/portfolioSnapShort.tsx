import ColoredHeader from '@/utils/coloredHeader';
import React, { useEffect, useState } from 'react'
import SnapShotAnimation from './snapShortAnimtion';
import SwipeAll from './snapshotSwiper/swipeAll';

const PortfolioSnapShort:React.FC=()=>{
    const [width,setWidth]=useState<number>(0);

    useEffect(()=>{
setWidth(window?.innerWidth)
    },[])
    return (
        <>
        <div className='portfolioSnapShort'>
<ColoredHeader>
Snapshot of Our Blockchain Portfolio
</ColoredHeader>
<br/><br/><br/><br/><br/>
<SwipeAll/>

<br/><br/>
        </div>
        </>
    )
}
export default PortfolioSnapShort;