import Bold from '@/utils/pBolder';
import PText from '@/utils/pText';
import { TextRotationAngledownSharp } from '@mui/icons-material';
import Image from 'next/image';
import React, { useState } from 'react'
import { ArrowDownCircle } from 'react-feather';
interface Props{
    image:string,
    content:string,
    name:string
}


const WhyChooseUsItem:React.FC<Props>=({image,content,name})=>{
    const [isShowContent,setIsShowContent]=useState<boolean>(false);
    return (
        <>
        <div className={`whyChooseUsItem  ${isShowContent ? 'active':''}`} style={{width:"100%"}}>

<div style={{cursor:"pointer"}} onClick={()=>setIsShowContent(!isShowContent)} className='d-flex justify-content-between align-items-center'>
    <div className={`d-flex align-items-center`}> 
<Image src={image} 
alt='' width={20} height={20}/>
<b style={{padding:10}}>{name}</b>
</div>
<ArrowDownCircle/>
</div>

{isShowContent && <PText>
    {content}
</PText>}


        </div>
        </>
    )
}

export default WhyChooseUsItem;