'use client'

import { useEffect } from 'react';

interface ImagePreloaderProps {
  images: string[];
  priority?: boolean;
}

const ImagePreloader: React.FC<ImagePreloaderProps> = ({ images, priority = false }) => {
  useEffect(() => {
    const preloadImages = () => {
      images.forEach((src, index) => {
        const img = new Image();
        
        // Set loading priority
        if (priority && index < 3) {
          img.loading = 'eager';
        } else {
          img.loading = 'lazy';
        }
        
        // Preload with different strategies based on priority
        if (priority) {
          // High priority - preload immediately
          img.src = src;
        } else {
          // Low priority - preload after a delay
          setTimeout(() => {
            img.src = src;
          }, 1000 + (index * 200));
        }
        
        // Optional: Add to cache
        img.onload = () => {
          // Image loaded successfully
          console.log(`Preloaded: ${src}`);
        };
        
        img.onerror = () => {
          // Handle error silently
          console.warn(`Failed to preload: ${src}`);
        };
      });
    };

    // Start preloading based on connection speed
    if ('connection' in navigator) {
      const connection = (navigator as any).connection;
      const effectiveType = connection?.effectiveType;
      
      if (effectiveType === 'slow-2g' || effectiveType === '2g') {
        // Skip preloading on very slow connections
        return;
      } else if (effectiveType === '3g') {
        // Delay preloading on 3G
        setTimeout(preloadImages, 2000);
      } else {
        // Preload immediately on fast connections
        preloadImages();
      }
    } else {
      // Fallback - preload with delay
      setTimeout(preloadImages, 1000);
    }
  }, [images, priority]);

  return null; // This component doesn't render anything
};

// Critical images that should be preloaded immediately
export const CRITICAL_IMAGES = [
  '/images/logo.png',
  '/images/hero-bg.webp',
  '/images/exchange.svg',
  '/images/nft.svg',
];

// Non-critical images that can be preloaded later
export const NON_CRITICAL_IMAGES = [
  '/images/testimonials.webp',
  '/images/portfolio.webp',
  '/images/metaverseMen.webp',
];

export default ImagePreloader;
