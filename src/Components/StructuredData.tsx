import React from 'react';

const StructuredData: React.FC = () => {
  const organizationSchema = {
    "@context": "https://schema.org",
    "@type": "Organization",
    "name": "AltAppLabs",
    "alternateName": "Alt App Labs",
    "url": "https://altapplabs.com",
    "logo": "https://altapplabs.com/images/logo.png",
    "description": "Leading blockchain and cryptocurrency development company specializing in DeFi, NFTs, smart contracts, and automated trading systems.",
    "foundingDate": "2020",
    "industry": "Blockchain Technology",
    "address": {
      "@type": "PostalAddress",
      "addressCountry": "US"
    },
    "contactPoint": {
      "@type": "ContactPoint",
      "contactType": "customer service",
      "availableLanguage": "English"
    },
    "sameAs": [
      "https://twitter.com/altapplabs",
      "https://linkedin.com/company/altapplabs",
      "https://github.com/altapplabs"
    ],
    "services": [
      "Blockchain Development",
      "Cryptocurrency Development",
      "DeFi Development",
      "NFT Development",
      "Smart Contract Development",
      "Crypto Trading Bots",
      "Market Making Solutions",
      "Arbitrage Bots"
    ]
  };

  const websiteSchema = {
    "@context": "https://schema.org",
    "@type": "WebSite",
    "name": "AltAppLabs",
    "url": "https://altapplabs.com",
    "description": "Transform your crypto markets with innovative blockchain solutions. Expert development in DeFi, NFTs, smart contracts, and automated trading systems.",
    "publisher": {
      "@type": "Organization",
      "name": "AltAppLabs"
    },
    "potentialAction": {
      "@type": "SearchAction",
      "target": "https://altapplabs.com/search?q={search_term_string}",
      "query-input": "required name=search_term_string"
    }
  };

  const serviceSchema = {
    "@context": "https://schema.org",
    "@type": "Service",
    "name": "Blockchain Development Services",
    "description": "Comprehensive blockchain development services including DeFi, NFTs, smart contracts, and cryptocurrency solutions.",
    "provider": {
      "@type": "Organization",
      "name": "AltAppLabs"
    },
    "serviceType": "Blockchain Development",
    "areaServed": "Worldwide",
    "hasOfferCatalog": {
      "@type": "OfferCatalog",
      "name": "Blockchain Services",
      "itemListElement": [
        {
          "@type": "Offer",
          "itemOffered": {
            "@type": "Service",
            "name": "DeFi Development"
          }
        },
        {
          "@type": "Offer",
          "itemOffered": {
            "@type": "Service",
            "name": "NFT Development"
          }
        },
        {
          "@type": "Offer",
          "itemOffered": {
            "@type": "Service",
            "name": "Smart Contract Development"
          }
        },
        {
          "@type": "Offer",
          "itemOffered": {
            "@type": "Service",
            "name": "Crypto Trading Bots"
          }
        }
      ]
    }
  };

  return (
    <>
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify(organizationSchema),
        }}
      />
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify(websiteSchema),
        }}
      />
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify(serviceSchema),
        }}
      />
    </>
  );
};

export default StructuredData;
