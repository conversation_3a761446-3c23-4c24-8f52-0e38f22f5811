import React, { Suspense, lazy } from 'react';
import Loading from './Loading';

interface LazyWrapperProps {
  children: React.ReactNode;
  fallback?: React.ReactNode;
  minHeight?: string;
}

const LazyWrapper: React.FC<LazyWrapperProps> = ({ 
  children, 
  fallback,
  minHeight = '200px'
}) => {
  const defaultFallback = (
    <div style={{ minHeight, display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
      <Loading variant="minimal" />
    </div>
  );

  return (
    <Suspense fallback={fallback || defaultFallback}>
      {children}
    </Suspense>
  );
};

// Lazy load heavy components
export const LazyTechnologies = lazy(() => import('./LandingPage/technologies'));
export const LazyPortfolioSnapShort = lazy(() => import('./LandingPage/portfolioSnapShort'));
export const LazyBriefApps = lazy(() => import('./LandingPage/briefApps'));
export const LazyTestimonials = lazy(() => import('./LandingPage/testimonials'));
export const LazyExploreCards = lazy(() => import('./LandingPage/exploreCards'));
export const LazySwipeAll = lazy(() => import('./LandingPage/swiper/swipeAll'));
export const LazyDiscoverMeterverse = lazy(() => import('./LandingPage/disCoverMeterverse'));
export const LazyWhyChooseUs = lazy(() => import('./LandingPage/whyChooseUs'));

export default LazyWrapper;
