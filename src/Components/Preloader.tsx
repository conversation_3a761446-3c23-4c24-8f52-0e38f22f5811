import React, { useEffect, useState } from 'react';

const Preloader: React.FC = () => {
  const [isVisible, setIsVisible] = useState(true);
  const [progress, setProgress] = useState(0);

  useEffect(() => {
    // Simulate loading progress
    const progressInterval = setInterval(() => {
      setProgress(prev => {
        if (prev >= 100) {
          clearInterval(progressInterval);
          // Hide preloader after a short delay
          setTimeout(() => setIsVisible(false), 500);
          return 100;
        }
        return prev + Math.random() * 20;
      });
    }, 100);

    // Ensure preloader is hidden after maximum time
    const maxTimeout = setTimeout(() => {
      setIsVisible(false);
    }, 3000);

    return () => {
      clearInterval(progressInterval);
      clearTimeout(maxTimeout);
    };
  }, []);

  if (!isVisible) return null;

  return (
    <div className="preloader">
      <div className="preloader-content">
        {/* Logo animation */}
        <div className="logo-animation">
          <div className="logo-ring ring-1"></div>
          <div className="logo-ring ring-2"></div>
          <div className="logo-center">
            <span className="logo-text">A</span>
          </div>
        </div>

        {/* Brand name */}
        <h1 className="brand-name">AltAppLabs</h1>
        
        {/* Progress bar */}
        <div className="progress-container">
          <div className="progress-bar">
            <div 
              className="progress-fill" 
              style={{ width: `${Math.min(progress, 100)}%` }}
            ></div>
          </div>
          <div className="progress-text">{Math.round(Math.min(progress, 100))}%</div>
        </div>

        {/* Loading text */}
        <p className="loading-text">Initializing Blockchain Solutions...</p>
      </div>

      <style jsx>{`
        .preloader {
          position: fixed;
          top: 0;
          left: 0;
          width: 100%;
          height: 100%;
          background: linear-gradient(135deg, #020b12 0%, #16131e 50%, #020b12 100%);
          display: flex;
          align-items: center;
          justify-content: center;
          z-index: 10000;
          animation: ${isVisible ? 'fadeIn' : 'fadeOut'} 0.5s ease-in-out;
        }

        .preloader-content {
          text-align: center;
          color: #d7dde2;
        }

        .logo-animation {
          position: relative;
          width: 100px;
          height: 100px;
          margin: 0 auto 30px;
        }

        .logo-ring {
          position: absolute;
          border: 3px solid transparent;
          border-radius: 50%;
          animation: rotate 2s linear infinite;
        }

        .ring-1 {
          width: 100px;
          height: 100px;
          border-top-color: #00d46c;
          border-right-color: #00d46c;
          animation-duration: 1.5s;
        }

        .ring-2 {
          width: 70px;
          height: 70px;
          top: 15px;
          left: 15px;
          border-bottom-color: #2096d4;
          border-left-color: #2096d4;
          animation-duration: 1s;
          animation-direction: reverse;
        }

        .logo-center {
          position: absolute;
          top: 50%;
          left: 50%;
          transform: translate(-50%, -50%);
          width: 40px;
          height: 40px;
          background: linear-gradient(135deg, #00d46c, #2096d4);
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          box-shadow: 0 0 20px rgba(0, 212, 108, 0.5);
          animation: pulse 2s ease-in-out infinite;
        }

        .logo-text {
          color: white;
          font-weight: bold;
          font-size: 18px;
          text-shadow: 0 0 10px rgba(255, 255, 255, 0.5);
        }

        .brand-name {
          font-size: 2.5rem;
          font-weight: 700;
          margin: 0 0 40px 0;
          background: linear-gradient(90deg, #00d46c, #2096d4, #00d46c);
          background-size: 200% 100%;
          -webkit-background-clip: text;
          background-clip: text;
          -webkit-text-fill-color: transparent;
          animation: textShine 3s ease-in-out infinite;
        }

        .progress-container {
          width: 300px;
          margin: 0 auto 20px;
        }

        .progress-bar {
          width: 100%;
          height: 4px;
          background: rgba(255, 255, 255, 0.1);
          border-radius: 2px;
          overflow: hidden;
          margin-bottom: 10px;
        }

        .progress-fill {
          height: 100%;
          background: linear-gradient(90deg, #00d46c, #2096d4);
          transition: width 0.3s ease;
          border-radius: 2px;
        }

        .progress-text {
          font-size: 14px;
          color: #00d46c;
          font-weight: 500;
        }

        .loading-text {
          font-size: 16px;
          color: #d7dde2;
          opacity: 0.8;
          margin: 0;
          animation: fadeInOut 2s ease-in-out infinite;
        }

        @keyframes rotate {
          from { transform: rotate(0deg); }
          to { transform: rotate(360deg); }
        }

        @keyframes pulse {
          0%, 100% { transform: translate(-50%, -50%) scale(1); }
          50% { transform: translate(-50%, -50%) scale(1.1); }
        }

        @keyframes textShine {
          0% { background-position: -200% 0; }
          100% { background-position: 200% 0; }
        }

        @keyframes fadeInOut {
          0%, 100% { opacity: 0.6; }
          50% { opacity: 1; }
        }

        @keyframes fadeIn {
          from { opacity: 0; }
          to { opacity: 1; }
        }

        @keyframes fadeOut {
          from { opacity: 1; }
          to { opacity: 0; }
        }

        @media (max-width: 768px) {
          .brand-name {
            font-size: 2rem;
          }
          
          .progress-container {
            width: 250px;
          }
          
          .logo-animation {
            width: 80px;
            height: 80px;
          }
          
          .ring-1 {
            width: 80px;
            height: 80px;
          }
          
          .ring-2 {
            width: 56px;
            height: 56px;
            top: 12px;
            left: 12px;
          }
          
          .logo-center {
            width: 32px;
            height: 32px;
          }
          
          .logo-text {
            font-size: 14px;
          }
        }
      `}</style>
    </div>
  );
};

export default Preloader;
