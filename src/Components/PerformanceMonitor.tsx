'use client'

import { useEffect } from 'react';

const PerformanceMonitor: React.FC = () => {
  useEffect(() => {
    // Only run in production and if performance API is available
    if (process.env.NODE_ENV !== 'production' || typeof window === 'undefined' || !window.performance) {
      return;
    }

    const measurePerformance = () => {
      try {
        // Core Web Vitals measurement
        const observer = new PerformanceObserver((list) => {
          list.getEntries().forEach((entry) => {
            const metricName = entry.name;
            const metricValue = (entry as any).value || entry.duration;

            // Log to console in development, send to analytics in production
            console.log(`${metricName}: ${metricValue}`);

            // You can send these metrics to your analytics service
            // Example: analytics.track('web_vital', { metric: metricName, value: metricValue });
          });
        });

        // Observe different performance metrics
        try {
          observer.observe({ entryTypes: ['largest-contentful-paint'] });
        } catch (e) {
          // LCP not supported
        }

        try {
          observer.observe({ entryTypes: ['first-input'] });
        } catch (e) {
          // FID not supported
        }

        try {
          observer.observe({ entryTypes: ['layout-shift'] });
        } catch (e) {
          // CLS not supported
        }

        // Measure page load time
        window.addEventListener('load', () => {
          const loadTime = performance.now();
          console.log(`Page load time: ${loadTime}ms`);
          
          // Measure specific timing metrics
          const navigation = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming;
          if (navigation) {
            const metrics = {
              dns: navigation.domainLookupEnd - navigation.domainLookupStart,
              tcp: navigation.connectEnd - navigation.connectStart,
              ttfb: navigation.responseStart - navigation.requestStart,
              download: navigation.responseEnd - navigation.responseStart,
              domParse: navigation.domContentLoadedEventEnd - navigation.domContentLoadedEventStart,
              domReady: navigation.domContentLoadedEventEnd - navigation.fetchStart,
              pageLoad: navigation.loadEventEnd - navigation.fetchStart,
            };

            console.log('Performance metrics:', metrics);
          }
        });

        // Measure resource loading times
        const measureResources = () => {
          const resources = performance.getEntriesByType('resource');
          const slowResources = resources.filter((resource) => resource.duration > 1000);
          
          if (slowResources.length > 0) {
            console.warn('Slow loading resources:', slowResources.map(r => ({
              name: r.name,
              duration: r.duration,
              size: (r as any).transferSize || 'unknown'
            })));
          }
        };

        // Check for slow resources after page load
        setTimeout(measureResources, 2000);

        // Memory usage monitoring (if available)
        if ('memory' in performance) {
          const memory = (performance as any).memory;
          console.log('Memory usage:', {
            used: Math.round(memory.usedJSHeapSize / 1048576) + ' MB',
            total: Math.round(memory.totalJSHeapSize / 1048576) + ' MB',
            limit: Math.round(memory.jsHeapSizeLimit / 1048576) + ' MB'
          });
        }

        // Monitor long tasks (if available)
        if ('PerformanceObserver' in window) {
          try {
            const longTaskObserver = new PerformanceObserver((list) => {
              list.getEntries().forEach((entry) => {
                console.warn('Long task detected:', {
                  duration: entry.duration,
                  startTime: entry.startTime
                });
              });
            });
            longTaskObserver.observe({ entryTypes: ['longtask'] });
          } catch (e) {
            // Long task API not supported
          }
        }

      } catch (error) {
        console.error('Performance monitoring error:', error);
      }
    };

    // Start monitoring after a short delay
    setTimeout(measurePerformance, 100);

    // Cleanup function
    return () => {
      // Disconnect observers if needed
    };
  }, []);

  // This component doesn't render anything
  return null;
};

export default PerformanceMonitor;
