.about-page {
    background: var(--bgColor);
    min-height: 100vh;
    color: var(--textColor);
}

.about-content {
    padding: 60px 0;
}

/* Section Titles */
.section-title {
    color: var(--green);
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 20px;
    position: relative;
}

.title-underline {
    width: 80px;
    height: 4px;
    background: linear-gradient(90deg, var(--green), var(--blue));
    margin: 0 auto 40px;
    border-radius: 2px;
}

/* Company Overview */
.company-overview {
    margin-bottom: 80px;
}

.lead-text {
    font-size: 1.2rem;
    line-height: 1.8;
    color: #ffffff;
    margin-bottom: 20px;
    font-weight: 500;
}

.about-text p {
    font-size: 1rem;
    line-height: 1.7;
    color: rgba(255, 255, 255, 0.9);
    margin-bottom: 20px;
}

.about-image img {
    border-radius: 12px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
    transition: transform 0.3s ease;
}

.about-image img:hover {
    transform: translateY(-5px);
}

/* Mission, Vision, Values Cards */
.mission-vision-values {
    margin-bottom: 80px;
}

.mission-card,
.vision-card,
.values-card {
    background: rgba(0, 0, 0, 0.3);
    border: 1px solid rgba(0, 212, 108, 0.2);
    border-radius: 12px;
    transition: all 0.3s ease;
    height: 100%;
}

.mission-card:hover,
.vision-card:hover,
.values-card:hover {
    transform: translateY(-10px);
    border-color: var(--green);
    box-shadow: 0 15px 40px rgba(0, 212, 108, 0.2);
}

.mission-card {
    border-left: 4px solid var(--green);
}

.vision-card {
    border-left: 4px solid var(--blue);
}

.values-card {
    border-left: 4px solid var(--yellow);
}

.icon-wrapper {
    width: 80px;
    height: 80px;
    margin: 0 auto 20px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
}

.mission-icon {
    background: linear-gradient(135deg, var(--green), rgba(0, 212, 108, 0.3));
}

.vision-icon {
    background: linear-gradient(135deg, var(--blue), rgba(0, 123, 255, 0.3));
}

.values-icon {
    background: linear-gradient(135deg, var(--yellow), rgba(255, 193, 7, 0.3));
}

.icon-wrapper img {
    width: 40px;
    height: 40px;
    filter: brightness(0) invert(1);
}

.mission-card h4,
.vision-card h4,
.values-card h4 {
    color: var(--green);
    font-weight: 600;
    margin-bottom: 15px;
}

.mission-card p,
.vision-card p,
.values-card p {
    color: rgba(255, 255, 255, 0.9);
    line-height: 1.6;
}

/* What Sets Us Apart */
.what-sets-apart {
    margin-bottom: 80px;
}

.feature-item {
    display: flex;
    align-items: flex-start;
    margin-bottom: 30px;
    padding: 20px;
    background: rgba(0, 0, 0, 0.2);
    border-radius: 12px;
    border: 1px solid rgba(255, 255, 255, 0.1);
    transition: all 0.3s ease;
}

.feature-item:hover {
    background: rgba(0, 212, 108, 0.1);
    border-color: var(--green);
    transform: translateX(10px);
}

.feature-icon {
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg, var(--green), var(--blue));
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 20px;
    flex-shrink: 0;
}

.feature-icon img {
    width: 30px;
    height: 30px;
    filter: brightness(0) invert(1);
}

.feature-content h5 {
    color: var(--green);
    font-weight: 600;
    margin-bottom: 10px;
    font-size: 1.2rem;
}

.feature-content p {
    color: rgba(255, 255, 255, 0.9);
    line-height: 1.6;
    margin: 0;
}

/* Our Expertise */
.our-expertise {
    margin-bottom: 60px;
}

.expertise-item {
    text-align: center;
    padding: 20px;
    background: rgba(0, 0, 0, 0.2);
    border-radius: 12px;
    border: 1px solid rgba(255, 255, 255, 0.1);
    transition: all 0.3s ease;
    height: 100%;
}

.expertise-item:hover {
    background: rgba(0, 212, 108, 0.1);
    border-color: var(--green);
    transform: translateY(-5px);
}

.expertise-item img {
    width: 60px;
    height: 60px;
    margin-bottom: 15px;
    border-radius: 8px;
}

.expertise-item h6 {
    color: #ffffff;
    font-weight: 600;
    font-size: 0.9rem;
    margin: 0;
}

/* Responsive Design */
@media (max-width: 768px) {
    .section-title {
        font-size: 2rem;
    }
    
    .about-content {
        padding: 40px 0;
    }
    
    .lead-text {
        font-size: 1.1rem;
    }
    
    .feature-item {
        flex-direction: column;
        text-align: center;
    }
    
    .feature-icon {
        margin-right: 0;
        margin-bottom: 15px;
    }
    
    .company-overview,
    .mission-vision-values,
    .what-sets-apart {
        margin-bottom: 60px;
    }
}

@media (max-width: 576px) {
    .section-title {
        font-size: 1.8rem;
    }
    
    .expertise-item img {
        width: 50px;
        height: 50px;
    }
    
    .icon-wrapper {
        width: 60px;
        height: 60px;
    }
    
    .icon-wrapper img {
        width: 30px;
        height: 30px;
    }
}

/* Journey & Achievements */
.journey-achievements {
    margin-bottom: 80px;
}

.achievement-item {
    text-align: center;
    padding: 30px 20px;
    background: rgba(0, 0, 0, 0.3);
    border-radius: 12px;
    border: 1px solid rgba(0, 212, 108, 0.2);
    transition: all 0.3s ease;
    height: 100%;
}

.achievement-item:hover {
    background: rgba(0, 212, 108, 0.1);
    border-color: var(--green);
    transform: translateY(-10px);
}

.achievement-number {
    font-size: 3rem;
    font-weight: 700;
    color: var(--green);
    margin-bottom: 15px;
    background: linear-gradient(135deg, var(--green), var(--blue));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.achievement-item h6 {
    color: #ffffff;
    font-weight: 600;
    margin-bottom: 10px;
    font-size: 1.1rem;
}

.achievement-item p {
    color: rgba(255, 255, 255, 0.8);
    font-size: 0.9rem;
    line-height: 1.5;
    margin: 0;
}

/* Why Choose Us */
.why-choose-us {
    margin-bottom: 80px;
}

.why-choose-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 30px;
}

.why-choose-item {
    text-align: center;
    padding: 30px 20px;
    background: rgba(0, 0, 0, 0.2);
    border-radius: 12px;
    border: 1px solid rgba(255, 255, 255, 0.1);
    transition: all 0.3s ease;
}

.why-choose-item:hover {
    background: rgba(0, 212, 108, 0.1);
    border-color: var(--green);
    transform: translateY(-10px);
}

.why-choose-icon {
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, var(--green), var(--blue));
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 20px;
}

.why-choose-icon img {
    width: 40px;
    height: 40px;
    filter: brightness(0) invert(1);
}

.why-choose-item h5 {
    color: var(--green);
    font-weight: 600;
    margin-bottom: 15px;
    font-size: 1.2rem;
}

.why-choose-item p {
    color: rgba(255, 255, 255, 0.9);
    line-height: 1.6;
    margin: 0;
}

/* Call to Action */
.cta-section {
    margin-bottom: 60px;
}

.cta-content {
    background: linear-gradient(135deg, rgba(0, 212, 108, 0.1), rgba(0, 123, 255, 0.1));
    border: 1px solid rgba(0, 212, 108, 0.3);
    border-radius: 20px;
    padding: 60px 40px;
    text-align: center;
}

.cta-content h2 {
    color: var(--green);
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 20px;
}

.cta-content p {
    color: rgba(255, 255, 255, 0.9);
    font-size: 1.2rem;
    margin-bottom: 40px;
    line-height: 1.6;
}

.cta-buttons {
    display: flex;
    gap: 20px;
    justify-content: center;
    flex-wrap: wrap;
}

.cta-btn {
    padding: 15px 30px;
    border-radius: 50px;
    font-weight: 600;
    font-size: 1rem;
    border: none;
    cursor: pointer;
    transition: all 0.3s ease;
    text-decoration: none;
    display: inline-block;
}

.cta-btn.primary {
    background: linear-gradient(135deg, var(--green), var(--blue));
    color: white;
}

.cta-btn.primary:hover {
    transform: translateY(-3px);
    box-shadow: 0 10px 25px rgba(0, 212, 108, 0.3);
}

.cta-btn.secondary {
    background: transparent;
    color: var(--green);
    border: 2px solid var(--green);
}

.cta-btn.secondary:hover {
    background: var(--green);
    color: white;
    transform: translateY(-3px);
}

/* Additional Responsive Styles */
@media (max-width: 768px) {
    .achievement-number {
        font-size: 2.5rem;
    }

    .cta-content {
        padding: 40px 20px;
    }

    .cta-content h2 {
        font-size: 2rem;
    }

    .cta-buttons {
        flex-direction: column;
        align-items: center;
    }

    .cta-btn {
        width: 100%;
        max-width: 300px;
    }

    .why-choose-grid {
        grid-template-columns: 1fr;
    }
}
