'use client'

import { useEffect, useState } from 'react';

interface PerformanceMetrics {
  loadTime: number;
  firstContentfulPaint: number;
  largestContentfulPaint: number;
  cumulativeLayoutShift: number;
  firstInputDelay: number;
  connectionType: string;
}

const PerformanceSummary: React.FC = () => {
  const [metrics, setMetrics] = useState<PerformanceMetrics | null>(null);
  const [showSummary, setShowSummary] = useState(false);

  useEffect(() => {
    // Only show in development mode
    if (process.env.NODE_ENV !== 'development') {
      return;
    }

    const measurePerformance = () => {
      const navigation = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming;
      const connection = (navigator as any).connection;
      
      const performanceMetrics: PerformanceMetrics = {
        loadTime: navigation ? navigation.loadEventEnd - navigation.navigationStart : 0,
        firstContentfulPaint: 0,
        largestContentfulPaint: 0,
        cumulativeLayoutShift: 0,
        firstInputDelay: 0,
        connectionType: connection?.effectiveType || 'unknown'
      };

      // Measure Core Web Vitals
      const observer = new PerformanceObserver((list) => {
        list.getEntries().forEach((entry) => {
          switch (entry.entryType) {
            case 'paint':
              if (entry.name === 'first-contentful-paint') {
                performanceMetrics.firstContentfulPaint = entry.startTime;
              }
              break;
            case 'largest-contentful-paint':
              performanceMetrics.largestContentfulPaint = entry.startTime;
              break;
            case 'layout-shift':
              if (!(entry as any).hadRecentInput) {
                performanceMetrics.cumulativeLayoutShift += (entry as any).value;
              }
              break;
            case 'first-input':
              performanceMetrics.firstInputDelay = (entry as any).processingStart - entry.startTime;
              break;
          }
        });
        
        setMetrics({ ...performanceMetrics });
      });

      // Observe different metrics
      try {
        observer.observe({ entryTypes: ['paint'] });
        observer.observe({ entryTypes: ['largest-contentful-paint'] });
        observer.observe({ entryTypes: ['layout-shift'] });
        observer.observe({ entryTypes: ['first-input'] });
      } catch (e) {
        // Some metrics might not be supported
      }

      setMetrics(performanceMetrics);
    };

    // Measure after page load
    if (document.readyState === 'complete') {
      measurePerformance();
    } else {
      window.addEventListener('load', measurePerformance);
    }

    // Show summary after 3 seconds
    const timer = setTimeout(() => {
      setShowSummary(true);
    }, 3000);

    return () => {
      window.removeEventListener('load', measurePerformance);
      clearTimeout(timer);
    };
  }, []);

  if (!showSummary || !metrics || process.env.NODE_ENV !== 'development') {
    return null;
  }

  const getScoreColor = (value: number, thresholds: [number, number]) => {
    if (value <= thresholds[0]) return '#00d46c'; // Good
    if (value <= thresholds[1]) return '#ffa500'; // Needs improvement
    return '#ff4444'; // Poor
  };

  const formatTime = (time: number) => `${Math.round(time)}ms`;

  return (
    <div style={{
      position: 'fixed',
      bottom: '20px',
      right: '20px',
      background: 'rgba(0, 0, 0, 0.9)',
      color: 'white',
      padding: '15px',
      borderRadius: '8px',
      fontSize: '12px',
      zIndex: 10000,
      maxWidth: '300px',
      border: '1px solid #00d46c'
    }}>
      <div style={{ fontWeight: 'bold', marginBottom: '10px', color: '#00d46c' }}>
        🚀 Performance Summary
      </div>
      
      <div style={{ marginBottom: '8px' }}>
        <strong>Load Time:</strong> {formatTime(metrics.loadTime)}
        <span style={{ 
          color: getScoreColor(metrics.loadTime, [2000, 4000]),
          marginLeft: '5px'
        }}>
          {metrics.loadTime <= 2000 ? '✅' : metrics.loadTime <= 4000 ? '⚠️' : '❌'}
        </span>
      </div>

      {metrics.firstContentfulPaint > 0 && (
        <div style={{ marginBottom: '8px' }}>
          <strong>FCP:</strong> {formatTime(metrics.firstContentfulPaint)}
          <span style={{ 
            color: getScoreColor(metrics.firstContentfulPaint, [1800, 3000]),
            marginLeft: '5px'
          }}>
            {metrics.firstContentfulPaint <= 1800 ? '✅' : metrics.firstContentfulPaint <= 3000 ? '⚠️' : '❌'}
          </span>
        </div>
      )}

      {metrics.largestContentfulPaint > 0 && (
        <div style={{ marginBottom: '8px' }}>
          <strong>LCP:</strong> {formatTime(metrics.largestContentfulPaint)}
          <span style={{ 
            color: getScoreColor(metrics.largestContentfulPaint, [2500, 4000]),
            marginLeft: '5px'
          }}>
            {metrics.largestContentfulPaint <= 2500 ? '✅' : metrics.largestContentfulPaint <= 4000 ? '⚠️' : '❌'}
          </span>
        </div>
      )}

      <div style={{ marginBottom: '8px' }}>
        <strong>Connection:</strong> {metrics.connectionType}
        <span style={{ marginLeft: '5px' }}>
          {['4g', 'fast'].includes(metrics.connectionType) ? '🚀' : 
           ['3g'].includes(metrics.connectionType) ? '🐌' : '🔴'}
        </span>
      </div>

      <div style={{ 
        fontSize: '10px', 
        color: '#888', 
        marginTop: '10px',
        borderTop: '1px solid #333',
        paddingTop: '5px'
      }}>
        Optimizations Applied:
        <br />✅ Lazy Loading • ✅ Code Splitting
        <br />✅ Image Optimization • ✅ Caching
        <br />✅ Bundle Optimization • ✅ PWA
      </div>

      <button
        onClick={() => setShowSummary(false)}
        style={{
          position: 'absolute',
          top: '5px',
          right: '5px',
          background: 'none',
          border: 'none',
          color: '#888',
          cursor: 'pointer',
          fontSize: '16px'
        }}
      >
        ×
      </button>
    </div>
  );
};

export default PerformanceSummary;
