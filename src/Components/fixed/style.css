.sideContact {
    left: 0;
    border-radius:50px;
    position: fixed;
    z-index: 99!important;
    width:max-content;
    height: 150px;
    top: 50%; /* Position from the top */
    transform: translateY(-50%); /* Move it up by half of its own height */
    color:white;
    margin-left:10px;
    padding:5px;
  }
  @media (max-width:700px) {
    .sideContact {
      left: 0;
      border-radius:50px;
      position: fixed;
      z-index: 99!important;
      width:max-content;
      height: 150px;
      top: 95%; /* Position from the top */
      left: 95%; /* Position from the top */
      

      transform: translateY(-95%) translateX(-95%); /* Move it up by half of its own height */
      color:white;
      margin-left:10px;
      padding:5px;
      display: none!important;
    }      
  }


.subNav{
  position:fixed;
  top:0;
  margin-top:80px;
  background: rgba(2, 11, 18, 0.95);
  backdrop-filter: blur(10px);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
  border: 1px solid rgba(0, 212, 108, 0.2);
  width:65%;
  border-radius:12px;
  min-height:200px;
  animation-duration:500ms;
  animation-name: showSubNav;
  overflow: hidden;
}

@keyframes showSubNav {
  0%{
    opacity: 0.1;
    margin-top: 13%;
  }
  100%{
    opacity:1;
    margin-top: 80px;
  }
}


.heading {
font-family:Roboto "Rubik, sans-serif";
font-size: "48px";
font-weight: 700;
line-height: "60px";


}