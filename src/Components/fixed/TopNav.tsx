import React from 'react'
import "@/css/Nav.css";
import LogoWithText from '@/utils/LogoWithText';
import NavButtons from '../Nav/navButtons';
import MenuButton from '../Nav/MenuButton';
import useClientWidth from '@/hooks/useClientWidth';

const TopNav: React.FC = () => {
    const { width, isClient } = useClientWidth();

    return (
        <nav className='topNav' role="navigation" aria-label="Main navigation">
            <div className='d-flex align-items-center justify-content-between'>
                <div className='d-flex align-items-center'>
                    <LogoWithText text='AltAppLabs' />
                    {isClient && width > 780 && <NavButtons />}
                </div>
                <MenuButton />
            </div>
        </nav>
    )
}

export default TopNav;