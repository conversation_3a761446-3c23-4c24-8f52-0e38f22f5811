import React from 'react'
import './style.css';
import WhatsAppIcon from '@/utils/whatsApp';
import TelegramIcon from '@/utils/telegram';
import MessageIcon from '@/utils/Mail';
import Link from 'next/link';
import { Call } from '@mui/icons-material';
export const phoneNumber = '447447823873'; // UK phone number in international format without the '+'
export const phoneNumberUk = '447447823873'; // Same UK phone number for consistency

const message = 'Welcome to AltAppLab, where innovation meets excellence. How may we assist you today?';
const formattedMessage = encodeURIComponent(message); // Encode the message for the URL
export const whatsappLink = `https://wa.me/${phoneNumber}?text=${formattedMessage}`; // Generate the WhatsApp link

export const telegramLink = "https://t.me/blcdevs";
export const email="<EMAIL>";
export const emailLink="mailto:<EMAIL>";
const SideContact:React.FC=()=>{
   
    return (
        <>
        <div className='sideContact'>
<Link href={whatsappLink}> <WhatsAppIcon/>
</Link>

<div style={{marginTop:5}}>
<Link href={telegramLink}>
<TelegramIcon/>
</Link>
</div>


<div style={{marginTop:5}}>
    <Link href={emailLink}>
<MessageIcon/>
</Link>

</div>

<div style={{marginTop:5}}>
    <Link href={phoneNumberUk}><Call/>
</Link>

</div>

        </div>
        </>
    )
}

export default SideContact;