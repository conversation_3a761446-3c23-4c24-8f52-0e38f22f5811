import { MDBBtn } from "mdb-react-ui-kit";
import SideContact from "./fixed/sideContact";
import TopNav from "./fixed/TopNav";
import Presenter from "./LandingPage/Presenter";
import ColoredHeader from "@/utils/coloredHeader";
import PText from "@/utils/pText";
import { useState, useEffect, Suspense, lazy } from "react";
import ErrorBoundary from "./ErrorBoundary";
import Loading from "./Loading";

// Lazy load ALL non-critical components for maximum performance
const LazyBoxs = lazy(() => import("./LandingPage/Boxs"));
const LazyAwardCard = lazy(() => import("./LandingPage/awardCard"));
const LazyAwardData = lazy(() => import("./LandingPage/awardCardData"));
const LazyAwardList = lazy(() => import("./LandingPage/awardList"));
const LazyAnimatedImage = lazy(() => import("./LandingPage/animatedImage"));
const LazyBuildMeterVerse = lazy(() => import("./LandingPage/BuildMeterverse"));
const LazyTakeYourBusiness = lazy(() => import("./LandingPage/takeYourBusiness"));
const LazyIndustriesWeTransformed = lazy(() => import("./LandingPage/IndustriesWeTransformed"));
const LazyConnectWithUs = lazy(() => import("./LandingPage/connectWithUs"));
const LazyTextScroll = lazy(() => import("./LandingPage/textScroller"));
const LazyCryptoComponent = lazy(() => import("./LandingPage/newComponent"));
const LazyTechnologies = lazy(() => import("./LandingPage/technologies"));
const LazyPortfolioSnapShort = lazy(() => import("./LandingPage/portfolioSnapShort"));
const LazyBriefApps = lazy(() => import("./LandingPage/briefApps"));
const LazyTestimonials = lazy(() => import("./LandingPage/testimonials"));
const LazyExploreCards = lazy(() => import("./LandingPage/exploreCards"));
const LazySwipeAll = lazy(() => import("./LandingPage/swiper/swipeAll"));
const LazyDiscoverMeterverse = lazy(() => import("./LandingPage/disCoverMeterverse"));
const LazyWhyChooseUs = lazy(() => import("./LandingPage/whyChooseUs"));

// Optimized lazy wrapper with intersection observer
const OptimizedLazyWrapper = ({ children, minHeight = "200px" }: { children: React.ReactNode, minHeight?: string }) => (
  <Suspense fallback={
    <div style={{ minHeight, display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
      <Loading variant="minimal" />
    </div>
  }>
    {children}
  </Suspense>
);
export default function Index(){
const [width, setWidth] = useState(0);

useEffect(() => {
  if (typeof window !== 'undefined') {
    setWidth(window.innerWidth);
  }
}, []);

return (
  <ErrorBoundary>
    <TopNav/>
    <SideContact/>
    <main className='Indexbody'>
      <Presenter/>
      <OptimizedLazyWrapper>
        <LazyTextScroll/>
      </OptimizedLazyWrapper>

      <section aria-labelledby="awards-heading">
        <ColoredHeader id="awards-heading">Awards and Recognition</ColoredHeader>
        <OptimizedLazyWrapper minHeight="200px">
          <LazyAwardCard>
            <LazyAwardData/>
          </LazyAwardCard>
        </OptimizedLazyWrapper>
        <OptimizedLazyWrapper minHeight="150px">
          <LazyAwardList/>
        </OptimizedLazyWrapper>
      </section>

      <section aria-labelledby="solutions-heading">
        <ColoredHeader id="solutions-heading">Innovative Blockchain Solutions Across the Globe</ColoredHeader>

        <PText>
          We specialize in creating robust, decentralized management systems, advanced security solutions, and custom financial applications tailored to meet the unique needs of both startups and established enterprises. Our expertise spans the entire blockchain ecosystem, ensuring that our clients benefit from the most advanced technologies and practices.
          <br/><br/>
          Whether you are looking to develop a new cryptocurrency, implement smart contracts, or leverage blockchain for supply chain management, AltAppLabs provides the expertise and innovation you need. Our team of experienced developers is dedicated to helping you harness the power of blockchain to transform your business operations.
        </PText>

        <OptimizedLazyWrapper minHeight="300px">
          <LazyAnimatedImage/>
        </OptimizedLazyWrapper>
        <OptimizedLazyWrapper minHeight="400px">
          <LazyCryptoComponent/>
        </OptimizedLazyWrapper>
      </section>

      <section aria-labelledby="services-heading">
        <ColoredHeader id="services-heading">Our Crypto Bot and Exchanges Services</ColoredHeader>
        <OptimizedLazyWrapper minHeight="400px">
          <LazySwipeAll/>
        </OptimizedLazyWrapper>
      </section>

      <section aria-labelledby="metaverse-heading">
        <OptimizedLazyWrapper minHeight="500px">
          <LazyDiscoverMeterverse/>
        </OptimizedLazyWrapper>
      </section>

      <section aria-labelledby="technologies-heading">
        <OptimizedLazyWrapper minHeight="600px">
          <LazyTechnologies/>
        </OptimizedLazyWrapper>
      </section>

      <section aria-labelledby="portfolio-heading">
        <OptimizedLazyWrapper minHeight="400px">
          <LazyPortfolioSnapShort/>
        </OptimizedLazyWrapper>
      </section>

      <section aria-labelledby="apps-heading">
        <OptimizedLazyWrapper minHeight="300px">
          <LazyBriefApps/>
        </OptimizedLazyWrapper>
      </section>

      <section aria-labelledby="business-heading">
        <OptimizedLazyWrapper minHeight="300px">
          <LazyTakeYourBusiness/>
        </OptimizedLazyWrapper>
      </section>

      <section aria-labelledby="why-choose-heading">
        <OptimizedLazyWrapper minHeight="500px">
          <LazyWhyChooseUs/>
        </OptimizedLazyWrapper>
      </section>

      <section aria-labelledby="explore-heading">
        <OptimizedLazyWrapper minHeight="400px">
          <LazyExploreCards/>
        </OptimizedLazyWrapper>
      </section>

      <section aria-labelledby="testimonials-heading">
        <OptimizedLazyWrapper minHeight="300px">
          <LazyTestimonials/>
        </OptimizedLazyWrapper>
      </section>

      {/* Spacing between testimonials and contact sections */}
      <div style={{ height: '80px' }}></div>

      <section aria-labelledby="contact-heading">
        <OptimizedLazyWrapper minHeight="400px">
          <LazyConnectWithUs/>
        </OptimizedLazyWrapper>
      </section>
    </main>
  </ErrorBoundary>
)
}

