import React from "react";
interface Props{
    children:any,
    onClick?:()=>void
}
 const Li:React.FC<Props>=({children,onClick})=>{
  return (
<li style={{
    listStyle:"none",
    padding: "2px 0",
    margin: "2px 0"
}} onClick={()=>{
    if(onClick)onClick();
}}>
    <span style={{
        padding: "8px 12px",
        display: "block",
        borderRadius: "6px",
        fontSize: "14px",
        fontWeight: "500"
    }} className="textColor">
{children}
</span></li>
);
}
export default Li