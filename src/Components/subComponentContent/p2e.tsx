import { MDBBtn } from 'mdb-react-ui-kit';
import "@/Components/subComponentContent/style.css";
import React, { useState } from 'react';
import Li from './li';
import { useRouter } from 'next/router';

const P2E: React.FC = () => {
const route=useRouter();

    const [currentText, setCurrentText] = useState([
        { name: 'Sorare Like NFT Marketplace Development', href: '/SorereLikeDevelopment' },
        { name: 'Play to Earn Game Like Zed Run', href: '/ZedRun' }
    ]);

    const [links, setLinks] = useState([
        { name: 'NFT Gaming Platform Like Axie Infinity', href: '/NFTGamePlatform' }
    ]);

    return (
        <>
            <div className='subService d-flex'>
                <div className='buttonContainer' style={{ width: "20%" }}>
                    {/* This section is empty in P2E component */}
                </div>

                <div className='textContainer' style={{ width: "40%" }}>
                    <p style={{ color: "var(--textColor)" }}>
                        {currentText.map((item, index) => <span  key={index} onClick={()=>{
                        route.push(item.href)
                    }}><Li >{item.name}</Li></span>)}
                    </p>
                </div>

                <div className='list' style={{ width: "40%",boxShadow:"none"}} >
                    {links.map((item, index) => <span  key={index} onClick={()=>{
                        route.push(item.href)
                    }}><Li>{item.name}</Li></span>)}
                </div>
            </div>
        </>
    );
};

export default P2E;
