import { MDBBtn } from 'mdb-react-ui-kit';
import "@/Components/subComponentContent/style.css";
import React, { useEffect, useState } from 'react'
import Li from './li';


const Meterverse:React.FC=()=>{
 
const [currentText,setCurrentText]=useState([
    'NFT Ticketing Services',
    'NFT Marketplace Development',
    'NFT Miniting Platform Development',
    'Fractional NFT Marketplace Development'
]);
const [links,setLinks]=useState([
    "NFT Token Development",
    "NFT Ticketing Services",
    "NFT Game Development",
    "NFT Marketplace Development",
    "NFTs in DeFi Services",
    "NFT Minting Platform Development",
    "Solana NFT Marketplace Development",
    "Fractional NFT Marketplace Development",
    "Crypto Dynasty Like NFT Game Development"
]);


    return (
        <>
        <div className='subService d-flex'>
<div className='buttonContainer' style={{width:"30%"}}>
<h5 style={{color:"var(--yellow)"}}><b>Metaverse Development Company</b>
</h5>
<p style={{color:"var(--colorText)"}}>
We are a top metaverse development company,
 developing future-ready metaverse for gaming, real estate, fashion, education, and more
 </p>

</div>

<div className='textContainer' style={{width:"30%"}}>

<p style={{color:"var(--textColor)"}}>
    {currentText.map((e,i)=> <Li key={i}>{e}</Li>)}
</p>
</div>
    
    <div className='list' style={{width:"40%",boxShadow:"none"}}>


    {links.map((e,i)=> <Li key={i}>{e}</Li>)}
    </div>
    
    
        </div>
        
        </>
    )
}


export default Meterverse;