import { MDBBtn } from 'mdb-react-ui-kit';
import "@/Components/subComponentContent/style.css";
import React, { useEffect, useState } from 'react'
import Li from './li';
import { useRouter } from 'next/router';


const ServicesContent: React.FC = () => {
    const blockChainList = "Blockchain Services,Blockchain App Development,Blockchain Digital Certificate Services,Private Blockchain Development Services,Smart Contract Development,Blockchain POC Development,Blockchain Consulting Services,Blockchain DApps Development,Smart Contract Audit Services,Blockchain Airdrop Services,Hyperledger Blockchain Development Company,Supplychain Management Services,Cardano Blockchain Development Services";
    const route = useRouter()
    const [subText, setSubtext] = useState([{
        title: "Blockchain Development Company",
        text: "We are the leading blockchain service provider and develops cutting-edge blockchain solutions that bring greater value to your business.",
        button: 'blockChain',
        links: [
            { name: "Blockchain Services", href: "/blockchainAppDevelopment" },
            { name: "Blockchain App Development", href: "/blockchainAppDevelopment" },
            { name: "Blockchain Digital Certificate Services", href: "/blockchainDigitalCertificationServices" },
            { name: "Private Blockchain Development Services", href: "/privateServices" },
            { name: "Smart Contract Development", href: "/smartContract" },
            { name: "Blockchain POC Development", href: "/pocDevelopment" },
            { name: "Blockchain DApps Development", href: "/DAppsDevelopment" },
            { name: "Smart Contract Audit Services", href: "/smartAudit" },
            { name: "Blockchain Airdrop Services", href: "/Airdrops" },
            { name: "Hyperledger Blockchain Development Company", href: "/HyperledgerBlockchain" },

        ]
    },
    {
        title: "Crypto",
        text: "They develop Over-The-Counter (OTC) cryptocurrency exchange platforms that facilitate large-volume trades directly between buyers and sellers.",
        button: 'Crypto',
        links: [
            { name: "Crypto Wallet Development", href: "/CryptoWalletDev" },
            { name: "Crypto Payment Gateway Development", href: "/PayementGateway" },
            { name: "Crypto Exchange Platform Development", href: "/cryptoExchange" },
            { name: "Crypto Cross Border Payment Services", href: "/CryptoBorder" },
            { name: "Crypto Portfolio Management Services", href: "/CryptoPortfolio" },
            { name: "Crypto Trading Bots Development", href: "/CryptoTrading" },
            { name: "Crypto Digital Marketing Services", href: "/CryptoDigital" },
            { name: "Cryptocurrency Development Services", href: "/CryptoDevelopment" },
            { name: "Crypto P2P Payment Development", href: "/CryptopP2P" },
            { name: "Best Platforms For Launching An ICO", href: "/IOCDevelopment" },
            { name: "ICO Development Services", href: "/IOCDevelopment" },
        ]
    },
    {
        title: "DEFI Development",
        text: "This service involves the development of decentralized finance (DeFi) applications, which typically include features such as cryptocurrency wallets, decentralized exchanges (DEX), lending platforms, and more, aimed at providing financial services without the need for traditional intermediaries.",
        button: 'DEFI',
        links: [
            { name: "DeFi App Development Services", href: "/DefiAppDevelopement" },
            { name: "DeFi Development Services", href: "/DefiBorrowing" },
        ]

    },
    {
        title: "Exchange",
        text: "Enter the market with the best-in-class crypto exchange development services    ",
        button: 'Exchange',
        links: [
            { name: "Crypto currency exchange", href: "/cryptoExchange" },
            { name: "P2P Crypto Exchange", href: "/P2PCryptoExchange" },
            { name: "Derivatives exchange", href: "/DerivativesExchange" },
            { name: "White label exchange ", href: "/WhiteLabelExchange" },
            { name: "Decentralized exchange", href: "/DecentralizedExchange" },
            { name: "Centralized exchange", href: "/CentralizedExchange" },
            { name: "Margin Trading", href: "/MarginTrading" },
            { name: "Crypto Market Making", href: "/CryptoMarketMaking" },


        ]

    },
    {
        title: "Bots Development",
        text: "Unlock the Potential of Your Business: Discover Superior Bot Development Services for Seamless Automation and Enhanced Customer Engagement",
        button: 'Bots',
        links: [
            { name: "Crypto Arbitrage Bot", href: "/CryptoArbitrageBot" },
        { name: "Crypto Trading Bots Development", href: "/CryptoTrading" },
            { name: "Bot Development", href: "/BotDevelopment" }
        ]

    },
    ]

    );
    const [currentText, setCurrentText] = useState(subText[0].title);
    const [heading, setHeading] = useState(subText[0].text);
    const [links, setLinks] = useState(subText[0].links);
    const [currentObj, setCurrentObj] = useState(subText[0]);

    useEffect(() => {
        setCurrentText(currentObj.text);
        setHeading(currentObj.title);
        setLinks(currentObj.links);
    }, [currentObj])

    return (
        <>
            <div className='subService d-flex'>
                <div className='buttonContainer' style={{ width: "20%" }}>
                    <MDBBtn color='link' className={currentObj.button === 'blockChain' ? 'active' : ''} onMouseEnter={(e) => {
                        const obj = subText.filter((e) => e.button == 'blockChain')[0];
                        setCurrentObj(obj);
                    }}
                    >Blockchain</MDBBtn>
                    <MDBBtn className={currentObj.button === 'Crypto' ? 'active' : ''} color='link' onMouseEnter={(e) => {
                        const obj = subText.filter((e) => e.button == 'Crypto')[0];
                        setCurrentObj(obj);
                    }}>Crypto</MDBBtn>
                    <MDBBtn onMouseEnter={(e) => {
                        const obj = subText.filter((e) => e.button == 'DEFI')[0];
                        setCurrentObj(obj);
                    }} className={currentObj.button === 'DEFI' ? 'active' : ''} color='link'>DEFI</MDBBtn>

                    <MDBBtn onMouseEnter={(e) => {
                        const obj = subText.filter((e) => e.button == 'Exchange')[0];
                        setCurrentObj(obj);
                    }} className={currentObj.button === 'Exchange' ? 'active' : ''} color='link'>Exchange</MDBBtn>


<MDBBtn onMouseEnter={(e) => {
                        const obj = subText.filter((e) => e.button == 'Bots')[0];
                        setCurrentObj(obj);
                    }} className={currentObj.button === 'Bots' ? 'active' : ''} color='link'>Bots Development</MDBBtn>


                </div>

                <div className='textContainer' style={{ width: "40%" }}>
                    <h5 style={{ color: "var(--yellow)" }}><b>{heading}</b>
                    </h5>
                    <p style={{ color: "#ffffff", opacity: 0.9, lineHeight: 1.6 }}>
                        {currentText}
                    </p>
                </div>

                <div className='list' style={{ width: "40%", boxShadow: "none" }}>

                    {links.map((e, i) => <span key={i} onClick={() => route.push(e.href)}><Li >{e.name}</Li> </span>)}

                </div>


            </div>

        </>
    )
}


export default ServicesContent;