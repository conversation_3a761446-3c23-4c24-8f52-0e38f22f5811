.buttonContainer button{
    width:100%;
    text-align: start !important;
    color: #ffffff !important;
    border-radius:10px;
    margin-top:5px;
    transition: all 0.3s ease;
    border: 1px solid transparent;
}
.buttonContainer button.active{
    color: #ffffff !important;
    background: var(--green);
    border: 1px solid var(--green);
}

.buttonContainer button:hover{
    color: #ffffff !important;
    background: rgba(0, 212, 108, 0.1);
    border: 1px solid var(--green);
}
.subService{
    padding:10px;
    margin-top:-5px;
}
.textColor:hover{
    background: rgba(0, 212, 108, 0.1);
    border-radius: 6px;
    color: var(--green) !important;
    transition: all 0.3s ease;
}
.textColor{
    color: #ffffff;
    padding: 8px 12px;
    border-radius: 6px;
    transition: all 0.3s ease;
    cursor: pointer;
    display: inline-block;
}
.subService > div{
    padding:16px;
}
.subService .textContainer {
    padding: 20px !important;
    background: rgba(0, 0, 0, 0.1);
    border-radius: 8px;
    border-left: 3px solid var(--green);
}
.subService .textContainer h5 {
    margin-bottom: 12px;
    font-weight: 600;
}
.subService .buttonContainer {
    padding: 16px !important;
    background: rgba(0, 0, 0, 0.1);
    border-radius: 8px;
    border-right: 3px solid var(--blue);
}
.subService .list{
  max-height:70vh !important;
  overflow:auto;
  padding: 16px;
  background: rgba(0, 0, 0, 0.2);
  border-radius: 8px;
  border: 1px solid rgba(0, 212, 108, 0.1);
}
.subService .list::-webkit-scrollbar {
  width: 6px;
}
.subService .list::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 3px;
}
.subService .list::-webkit-scrollbar-thumb {
  background: var(--green);
  border-radius: 3px;
}
.subService .list::-webkit-scrollbar-thumb:hover {
  background: var(--blue);
}
.Benefits{
    width:100%;
    align-items: center;;
}
.Benefits img{
min-width:100%;
min-height:100%;
}

.Benefits b{
    font-size:larger;
    color:var(--blueText);
}

.Benefits .item1{
    padding:5px;
}
