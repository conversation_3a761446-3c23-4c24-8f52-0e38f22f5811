import { MDBBtn } from 'mdb-react-ui-kit';
import "@/Components/subComponentContent/style.css";
import React, { useState } from 'react';
import Li from './li';
import { useRouter } from 'next/router';

const NFTs: React.FC = () => {
    const route=useRouter()
    const [currentText, setCurrentText] = useState([
        { name: 'NFT Ticketing Services', href: '/NFTTicketingServices' },
        { name: 'NFT Marketplace Development', href: '/NftMarket' },
        { name: 'NFT Minting Platform Development', href: '/NftMinting' },
        { name: 'Fractional NFT Marketplace Development', href: '/FractionalNFT' }
    ]);

    const [links, setLinks] = useState([
        { name: 'NFT Token Development', href: '/NftToken' },
        { name: 'NFT Game Development', href: '/NFTGame' },
        { name: 'NFT Marketplace Development', href: '/NftMarket' },
        { name: 'NFTs in DeFi Services', href: '/NftDeFiServices' },
        { name: 'Solana NFT Marketplace Development', href: '/SolalanNftDevelopment' },
        { name: 'Crypto Dynasty Like NFT Game Development', href: '/NftCryptoDynesty' }
    ]);

    return (
        <>
            <div className='subService d-flex'>
                <div className='buttonContainer' style={{ width: "20%" }}>
                    {/* This section is empty in NFTs component */}
                </div>

                <div className='textContainer' style={{ width: "40%",boxShadow:"none" }}>
                    <p style={{ color: "var(--textColor)" }}>
                        {currentText.map((item, index) => <Li onClick={()=>{
                            route.push(item.href)
                        }} key={index}>{item.name}</Li>)}
                    </p>
                </div>

                <div className='list' style={{ width: "40%",boxShadow:"none" }}>
                    {links.map((item, index) => <Li onClick={()=>{
                            route.push(item.href)
                        }} key={index}>{item.name}</Li>)}
                </div>
            </div>
        </>
    );
};

export default NFTs;
