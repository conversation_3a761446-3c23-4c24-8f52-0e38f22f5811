import React, { useState } from 'react';
import Image from 'next/image';

interface OptimizedImageProps {
  src: string;
  alt: string;
  width?: number;
  height?: number;
  className?: string;
  style?: React.CSSProperties;
  priority?: boolean;
  quality?: number;
  fill?: boolean;
  sizes?: string;
  placeholder?: 'blur' | 'empty';
  blurDataURL?: string;
  objectFit?: 'contain' | 'cover' | 'fill' | 'none' | 'scale-down';
  loading?: 'lazy' | 'eager';
}

const OptimizedImage: React.FC<OptimizedImageProps> = ({
  src,
  alt,
  width,
  height,
  className = '',
  style = {},
  priority = false,
  quality = 85,
  fill = false,
  sizes,
  placeholder = 'blur',
  blurDataURL,
  objectFit = 'cover',
  loading = 'lazy',
  ...props
}) => {
  const [isLoading, setIsLoading] = useState(true);
  const [hasError, setHasError] = useState(false);

  // Generate a simple blur placeholder if none provided
  const defaultBlurDataURL = "data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQABAAD/2wBDAAYEBQYFBAYGBQYHBwYIChAKCgkJChQODwwQFxQYGBcUFhYaHSUfGhsjHBYWICwgIyYnKSopGR8tMC0oMCUoKSj/2wBDAQcHBwoIChMKChMoGhYaKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCj/wAARCAAIAAoDASIAAhEBAxEB/8QAFQABAQAAAAAAAAAAAAAAAAAAAAv/xAAhEAACAQMDBQAAAAAAAAAAAAABAgMABAUGIWGRkqGx0f/EABUBAQEAAAAAAAAAAAAAAAAAAAMF/8QAGhEAAgIDAAAAAAAAAAAAAAAAAAECEgMRkf/aAAwDAQACEQMRAD8AltJagyeH0AthI5xdrLcNM91BF5pX2HaH9bcfaSXWGaRmknyJckliyjqTzSlT54b6bk+h0R//2Q==";

  const handleLoad = () => {
    setIsLoading(false);
  };

  const handleError = () => {
    setHasError(true);
    setIsLoading(false);
  };

  if (hasError) {
    return (
      <div
        className={`image-error ${className}`}
        style={{
          ...style,
          backgroundColor: 'var(--bgColor)',
          border: '1px solid var(--green)',
          borderRadius: '8px',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          color: 'var(--textColor)',
          fontSize: '14px',
          minHeight: height || 200,
          width: width || '100%',
        }}
        role="img"
        aria-label={`Failed to load image: ${alt}`}
      >
        <div style={{ textAlign: 'center', padding: '20px' }}>
          <div style={{ fontSize: '24px', marginBottom: '8px' }}>📷</div>
          <div>Image unavailable</div>
        </div>
      </div>
    );
  }

  const imageProps: any = {
    src,
    alt,
    className: `optimized-image ${className} ${isLoading ? 'loading' : 'loaded'}`,
    style: {
      ...style,
      transition: 'opacity 0.3s ease',
      opacity: isLoading ? 0.7 : 1,
    },
    onLoad: handleLoad,
    onError: handleError,
    quality,
    priority,
    placeholder: placeholder === 'blur' ? 'blur' : 'empty',
    blurDataURL: blurDataURL || defaultBlurDataURL,
    loading: priority ? 'eager' : loading,
    ...props,
  };

  if (fill) {
    imageProps.fill = true;
    imageProps.style = {
      ...imageProps.style,
      objectFit,
    };
    if (sizes) {
      imageProps.sizes = sizes;
    }
  } else {
    if (width) imageProps.width = width;
    if (height) imageProps.height = height;
    if (sizes) imageProps.sizes = sizes;
  }

  return (
    <div className="optimized-image-container" style={{ position: 'relative' }}>
      <Image {...imageProps} alt={alt} />
      {isLoading && (
        <div
          className="image-loading-overlay"
          style={{
            position: 'absolute',
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            backgroundColor: 'rgba(2, 11, 18, 0.5)',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            borderRadius: '8px',
          }}
          aria-hidden="true"
        >
          <div
            style={{
              width: '20px',
              height: '20px',
              border: '2px solid var(--green)',
              borderTop: '2px solid transparent',
              borderRadius: '50%',
              animation: 'spin 1s linear infinite',
            }}
          />
        </div>
      )}
      
      <style jsx>{`
        @keyframes spin {
          0% { transform: rotate(0deg); }
          100% { transform: rotate(360deg); }
        }
        
        .optimized-image {
          transition: opacity 0.3s ease;
        }
        
        .optimized-image.loading {
          opacity: 0.7;
        }
        
        .optimized-image.loaded {
          opacity: 1;
        }
      `}</style>
    </div>
  );
};

export default OptimizedImage;
