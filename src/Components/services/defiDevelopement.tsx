import React from 'react'
import { MDBBtn } from "mdb-react-ui-kit";
import TopNav from "../fixed/TopNav";
import SideContact from "../fixed/sideContact";
import TakeYourBusiness from "../LandingPage/takeYourBusiness";
import WhyChooseUs from "../LandingPage/whyChooseUs";
import Testimonials from "../LandingPage/testimonials";
import Blogs from "../LandingPage/Blogs";
import ConnectWithUs from "../LandingPage/connectWithUs";
import "../LandingPage/style.css";

import H3 from "../Portfolio/h3";
import { useState } from "react";
import Header from "./header";
import "../Portfolio/style.css"
import TextItem from "./textItem";
import PText from "@/utils/PText2";
import "./style.css";
import NativeAppDevelopment from "./nativeAppDevelopment";
import Bold from "@/utils/pBolder";
import WeOffer from "./weOffer";
import Industries from "./Industries";
import Contacts from "./contact";
import WhyChoose from "./whyChoose";
import Image from 'next/image';
import WhiteBold from '@/utils/WhiteBold';
import CertificationApproach from './certificationApproach';
import Items, { techImages } from './items';
import TechCarousel from '../LandingPage/TechCarousal';

export default function DefiAppDevelopement() {
    const [width, setWidth] = useState(typeof window == 'object' ? window.innerWidth : 0);

    const LItems = [
        {
            title: `Fully Automatic
            `,
            name: `DeFi cuts off intermediaries in lending, investment, trading and other financial processes. Further, repetitive tasks are automated with smart contract protocols.

            `,
            image: "data:image/png;base64,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"

        }, {
            title: `Boundaryless Operations
            `,
            name: `The financial sector can be executed with the integrity and consensus protocols by leveraging the DeFi ecosystem. Third-party tech administrators and sub-organizations can be authorized with transparency in the platform.

            `,
            image: "data:image/png;base64,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"

        }, {
            title: `Simple Infrastructure
            `,
            name: `Decentralized Finance (DeFi) system has globalized members and operates as a distributed network. Henceforth, the requirement for shift-based representatives, security team and the workspace is diminished thereby expanding your benefit rates.

            `,
            image: "data:image/png;base64,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"

        },

    ]

    const DeFiItems=[
        {
            title:`User Access And Control
            `,
            name:`Users have complete control over their private keys in a DeFi architecture. They will be able to manage their assets individually and from any location. Users can use their crypto wallets to interact directly with the decentralized network, without any restriction or delayed process.

            `,
            image:""
        },{
            title:`Immutability
            `,
            name:`The transactions that happen in DeFi are tamper-proof and are stored immutably on the network thanks to Blockchain. The transactions can be validated and verified by any user, resulting in increased security and audibility.

            `,
            image:""
        },{
            title:`Interoperability
            `,
            name:`All DeFi equipment and applications can connect with one another thanks to compatible standards. Lego, the building bricks, and DeFi applications, for example, can be used to create new apps and products.

            `,
            image:""
        },{
            title:`Programmable Assets
            `,
            name:`Our DeFi apps are controlled by Smart Contracts with rules put in them. Smart Contracts are used to generate all financial instruments, digital assets, and more, and all transactions are carried out in a peer-to-peer network without the use of middlemen. Smart Contracts keep track of all transactions, ensuring great transparency and accountability.

            `,
            image:""
        },{
            title:`Immutability
            `,
            name:`The transactions that happen in DeFi are tamper-proof and are stored immutably on the network thanks to Blockchain. The transactions can be validated and verified by any user, resulting in increased security and audibility.

            `,
            image:""
        },{
            title:`Interoperability`,
            name:`All DeFi equipment and applications can connect with one another thanks to compatible standards. Lego, the building bricks, and DeFi applications, for example, can be used to create new apps and products.

            `,
            image:""
        }
    ]

    const DAppsItems=[
        {
            title:`No downtime
            `,
            name:`Smart Contracts are the backbone of Dapps. It can normally serve clients for their various requirements once they are sent. It can't be shut down for maintenance or anything like that. It is never an option to refuse service.

            `,
            image:""
        },
        {
            title:`Complete information respectability
            `,
            name:`Because information is stored on cryptographic natives and calculations, it is both permanent and indisputable. Since the information on exchanges was just released, it can't be faked.

            `,
            image:""
        },
        {
            title:`Disseminated Applications
            `,
            name:`Because all decentralized programs are dispersed, they are extremely difficult to shut down, unlike centralized or traditional systems. While the fundamental blockchain is working, it is quite difficult to remove Dapps from a critical issue.

            `,
            image:""
        },
        {
            title:`All Dapps are decentralized
            `,
            name:`In contrast to its partners, all Dapps are decentralized - all data and information are stored on a public decentralized record.

            `,
            image:""
        },
        {
            title:`Open-source
            `,
            name:`Dapps are all free and open-source. In an ideal world, these apps would be overseen by a body that makes decisions on improvements. The Dapp's codebase will be made available for inspection.

            `,
            image:""
        }
    ]

    const dApps2Items=[
        {
            title:`DeFi Exchanges
            `,
            name:`To yield high profits and consumer base for your business, we develop DeFi Dex, the decentralized exchange platform which are automated and customized with consensus protocols.

            `,
            image:""
        },{
            title:`DeFi Crypto Banking System
            `,
            name:`Our advanced crypto-banking add-ons will help the user’s base to leverage the excellent financial services with a well-designed interface.
            `,
            image:""
        },{
            title:`DeFi P2P Lending
            `,
            name:`The DeFi platform we offer is functioned with the instant loan approvals, calculative interest rates and more. With all these functionality, there are no transaction bounds in lending services.

            `,
            image:""
        },{
            title:`DeFi Staking Applications
            `,
            name:`Our customized DeFi staking platforms will solidify more benefits and security to their customer base by providing profitable yields based on the asset value, timeframe, inflation and other consensus protocols.

            `,
            image:""
        },{
            title:`DeFi Finance Management
            `,
            name:`Our crypto management marketers utilizes brand-new trading techniques to manage your assets with better yields and high-end security.
            `,
            image:""
        },{
            title:`DeFi Crypto Tokens
            `,
            name:`Our end-to-end flawless DeFi tokens will reward consumers with transparency. Moreover, the DeFi tokens will issue applications for your business to incentivize your customer base.
            `,
            image:""
        },{
            title:`DeFi Crypto Wallet
            `,
            name:`We offer DeFi Wallets for crypto users to have complete control over your funds. Moreover, to enhance the security of your assets, we offer you customized private keys that are backed up in our hardware devices.
            `,
            image:""
        },{
            title:`DeFi Yield Farming Applications
            `,
            name:`Yield farming in Decentralized Finance is a way to gain rewards such as crypto coins with cryptocurrency holdings. In simple terms, it means locking up cryptocurrencies in pools and getting rewards. It may also provide other economic incentives to attract more capital to their platform.

            `,
            image:""
        },{
            title:`DeFi Voting and Lottery System
            `,
            name:`Our blockchain based DeFi Lottery system offers a decentralized lottery system where users can get lucrative benefits with no potential loss. Among the group of investors, one random user will be selected for the rewards while the other users will retrieve their investment amount.

            `,
            image:""
        },{
            title:`DeFi Smart Contracts
            `,
            name:`The DeFi platform is rapidly growing now because of DeFi smart contract development. Our team of experts will build advanced automation techniques for your fintech services.
            `,
            image:""
        },{
            title:`DeFi Stable Coin Creation
            `,
            name:`To attract community users, we build feature-rich stable coins for brands that can be issued at the DeFi market space to improve the liquidity of business assets.
            `,
            image:""
        },{
            title:`DeFi Derivatives Use Cases
            `,
            name:`To avoid the risk of financial democracy and crypto validity, our team builds advanced DeFi based derivative platforms where every function is operated by smart contracts. Consult our blockchain experts now to devise your business strategy.

            `,
            image:""
        },{
            title:`DeFi Crowdfunding Platform
            `,
            name:`To support business and start-ups, crowdfunding is the new trend to aggregate your funds. Our team of blockchain architect will support in all your fundraising campaign by building ICO, IEO, IDEO, stable coin and issuance software development.

            `,
            image:""
        },{
            title:`DeFi for Heavy Industries
            `,
            name:`Our DeFi solution embarks in all industries like banking, e-commerce, gaming, federations, supply chain and even in healthcare.
            `,
            image:""
        },{
            title:`DeFi Based dApps
            `,
            name:`Our proficient developers build sophisticated DeFi Apps for various enterprises with enriched features.
            `,
            image:""
        },{
            title:`DeFi for Insurance
            `,
            name:`Our team has hands-on experience to build feature enrich DeFi insurance application for every business sector with complete automation.
            `,
            image:""
        },{
            title:`DeFi Based Commodity Trading
            `,
            name:`Crypto community is now sailing high in this DeFi finance development service. So we build DeFi leverage trading platforms with automated smart contracts and unique trading techniques. Leverage your trading platform now with us.

            `,
            image:""
        },{
            title:`DeFi Based Asset Management
            `,
            name:`To grab the crypto community attention, we customize platforms based on future trend and build a highly-secured asset trading platform which can help you to yield high return on investment with new strategic trading techniques.

            `,
            image:""
        },
    ]
    return (<>
        <TopNav />
        <SideContact />

        {/* page start */}

        <div style={{ width: '100%', minWidth: "100vw", background: "white", minHeight: "100vh", height: "auto" }}>

            <br /><br />
            <br /><br />

            <Header image={"https://blockchaintechs.io/wp-content/uploads/2021/09/<EMAIL>"}
                title='Become part of the financial revolution by making use of professional solutions from a DeFi Development Services!'
                text=''
                textColor='black' />

            <div style={{ padding: 10 }}>

                <br />
                <div style={{ maxWidth: 1524, margin: "0 auto", width: "90%" }}>
                    <Items title={`Decentralized Finance (DeFi) Development
`} p={`We help enterprises and evolving startups to replace conventional financial system with decentralized finance platforms. The main scope of DeFi is delivering equitable monetary services to vast population of the world. This blockchain-based DeFi system gives access to major functions such as lending or borrowing loans, trading, tokenization and lot more. Moreover, DeFi application provides more security, cuts down transaction fees, provides high transparency and can even attribute in the absence of an intermediary.

Along with the aforementioned functionalities, some of the main features are transparency, P2P transactions, data integrity, distributed ledger, consensus protocols and immutable. We are the leading DeFi Development Service provider with hands-on experience in building a successful Defi application roadmap for your business. Our team of experts will help you to replace the next-gen industrial revolution of Decentralized Finance Development with expertise skill.`} items={[]} />
                    <br /><br />

                    <Items items={LItems} title="Leverage on DeFi for Your Business" p={``} />


                    <br /><br />
<Items items={DeFiItems} title='Advantages of DeFi Development Services' p={``}/>
                    <br/><br/>
                    <Items items={DAppsItems} title='Benefits of DeFi Application Development Services' p={''}/> 
                    <br /><br />
                    <Contacts />
                    <br /><br />

<br/><br/>
<Items items={dApps2Items} title="Blockchain Technologies for DeFi Development Solutions
" p={``} />
<br/><br/>

<H3>Our Technology Development Stack
</H3>

<TechCarousel techImages={techImages}/>
                </div>


            </div>



        </div>


        <br />

        <br /><br />
        <Testimonials />
        <br /><br />
        <ConnectWithUs />
        <br /><br />
    </>)
}


