import Bold from '@/utils/pBolder';
import Image from 'next/image'
import React from 'react'
interface Props{
    title:string,
    image:string,
    text:string
}
const TextItem:React.FC<Props>=({title,image,text})=>{
    return (
        <div className='textItem'>
        <Image width={500} height={500} src={image} alt='' />
        <br/>
        <Bold>{title}</Bold>
        <p>
       {text}.
        </p>
        </div>
    )
}
export default TextItem;