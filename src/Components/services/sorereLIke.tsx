import React from 'react'
import { MDBBtn } from "mdb-react-ui-kit";
import TopNav from "../fixed/TopNav";
import SideContact, { telegramLink, whatsappLink } from "../fixed/sideContact";
import TakeYourBusiness from "../LandingPage/takeYourBusiness";
import WhyChooseUs from "../LandingPage/whyChooseUs";
import Testimonials from "../LandingPage/testimonials";
import Blogs from "../LandingPage/Blogs";
import ConnectWithUs from "../LandingPage/connectWithUs";
import "../LandingPage/style.css";

import H3 from "../Portfolio/h3";
import { useState } from "react";
import Header from "./header";
import "../Portfolio/style.css"
import TextItem from "./textItem";
import PText from "@/utils/PText2";
import "./style.css";
import NativeAppDevelopment from "./nativeAppDevelopment";
import Bold from "@/utils/pBolder";
import WeOffer from "./weOffer";
import Industries from "./Industries";
import Contacts from "./contact";
import WhyChoose from "./whyChoose";
import Image from 'next/image';
import WhiteBold from '@/utils/WhiteBold';
import CertificationApproach from './certificationApproach';
import Items, { techImages } from './items';
import { Forward10Sharp } from '@mui/icons-material';
import { FaAngleLeft } from 'react-icons/fa6';
import TechCarousel from '../LandingPage/TechCarousal';
import Link from 'next/link';

export default function SorereLikeDevelopment(){
const [width,setWidth]=useState(typeof window=='object' ? window.innerWidth :0);
const items1=[
    {
        title:``,
        name:`Football fans must create an account on the Sorare clone NFT marketplace by entering their email address, phone number, and cryptocurrency wallet. By syncing their Facebook and Google accounts, they can also connect with one another. Football supporters are subjected to smart KYC/AML verification via the Ramp network.`,
        image:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAIAAAACAEAYAAACTrr2IAAAABGdBTUEAALGPC/xhBQAAACBjSFJNAAB6JgAAgIQAAPoAAACA6AAAdTAAAOpgAAA6mAAAF3CculE8AAAABmJLR0QAAAAAAAD5Q7t/AAAACXBIWXMAAABgAAAAYADwa0LPAAAAB3RJTUUH5QkPEB8cGlOjFwAAGxxJREFUeNrtnXtclNXWx39ruBkMmJe0kspMTwUziEpeCjGTUlQYUDBTyjwZpal1KDRveVcSD72KJ00tTcULeGEGb3lU8lZqqOCMeL+llppZMQMqMLPfP5iHPMAjl2HmmWH29x8/uvfsZ+3ls9ez9tp7rw1wOBwOh8PhcDgcDofDcQbIdo/yS4uJdnf3ui4rKfaMjKTRaGzqFRlJmRRFe9q3Z8loihhfXxqDh5Hi5SW1YpwVNgd/YlRBAcXjFtKvXmXhbBPrdvQom4/bsu0ZGQWPmlzdCjMygLwB6euLiqSWl2MZVjcA8on+myPk/fqhu+xTbE9KokgQJbVqJXXHObWDZSEIuefP4xS7gJ8TEgyxuj1q06ZNUsvFqR1WMQCTmUwmL1HMzDmQmEiNaC2SEhKk7ijHSniwdKaYM0d/S/dcuxnjxgHAVDKZpBaLUz1c6rpB+dUAz4c1c+bQE/gFJ/jAr/cYKZ1uvvSSx7PNm19/r0GDops3b56+t3On1GJxqkedeQCCq0/zZOOox4YNohXfQggU9+6xPzERd776ipbQKmxcvdpTBRSqdbob/z2e8N+2BQVSK8bR8BkZcFHVr2dP9i2LgHH7dtGKVehfqFaoBjxVCgV7ke2k4thYUqKA+cTFIQN/4R/u7mLNsyy2DBlRUYYgXZCaMjKk1gvnwdSBASgN7sl3uUy799HJk6Jz/JFsJ96/ds34AVsnK+nTp9D3xOhN4bm5UivA8enGujFXV/mu28qGB06ftpb+PdP80lQsMNDlmKwbtm/ejAUUikUtWpSvx45iOJTnzhnauC9tfsPPDzhydPGS4mKptcSpHJmlDZRF9cVePPMXhw986yAf8Edoo/YqlbX1Xzggb4CacnKM91x82erwcESiIc5UXAWg9lgIbevW8sCiGTdOqlRS64fzYCw2AMJynli54GrygW8lXmS9TDG2039hcm6xZuWxYxjHduK9JUtEK/ZFMoaIy8WxDyw2AFgFXxrWoYPoA87LplKb1FSpO1pvCUYeLe7SRazYavq/RLtN/1i1SqyY/o0DyA4Kklo9nAdjuQFIo09x9/HHxYo9nih+xW1XXp7UHa237GL/Rthjj4kVW0v/DZYZDQ2++TtoWB62BD2wpGKMgGNfWBwE9PZWKlUqxsTK9XqtVq0mG+44dC6k1r/Uz+dYhuUeAIfDcVi4AeBwnBhuADgcJ4YbAA7HieEGgMNxYlylFoBHkTkc6eAeAIfjxEjuAXDqN1V5ePaGs2VE4h4Ah3MfZSnpXNEa1599lrZRAq174w1ZG5rN/lq3Tp7tsq0oOy9PvkrRTSWLipJaXkuR3APgc3yOI0HdkY22zzwDENB240bvpopTEQ6cEYl7AByOJdyjGNKNGeOtVCpzGs6aJbU4NUVyD4BTv7GVhyd5RqRLALqPHSvPVmSrMg4edJSMSNwAcByc0oxIpqjbSnbgyy/pWxAlVVKtYkakxzaF5+bCfFBaDwBt76vvCQCHDpX+5dAhz/F+aaqrX3/t4vvgjEhoSIcxKSkJ6NA+7t0tW+w9I5LkBsDRosRVfdHquj88RvJg7s+IxJ60fkYkz/i2bhFvhoe7RJo60ZmDB8t7BPdlRPr8xu8qlSEHvYH166XWkxg8BsBxbHhGJIvgBoDj2PCMSBbBDQDHseEZkSxC8hhAfZvj1rf+2Ds0g7rhF09PsfLf0vMGpK83GOr6uUK7pTGfSuQajssIl8sBAGqptSQO9wA4HCeGGwAOx4nhBoDDcWIkjwHwfQCWPY/DsQTuAXA4Tgw3AByLEM7Pi5U/EuOXFhNtjobXIY3DWs8Pa+3jIyqXBpMxOD9fav3YO9wAcCzjBYRi2PXrYsV3v3W9XJTt71/Xjy0ObPC6R6Z4u9Qbb2LXr79KrR57R/IYQH2b49a3/lTJfvixuB9/BJBNh555pnwxi2Re7MfBgwEAjwmHa+qAV7GXtYuNxQIAPSsWs9mYxp766Sd8hASpVWTPcA+AYxk/0HZZuvixV2qDE7QgLs4zzS9NxQIDLX1cw9iApn2z27fHMpqHp4cNE624HP60VW3HW3DsA24AOBZhSHu2sVvrjAyWhSDknj9focIK7IXOw8PlWOkx2toaAmHgm3xMK13mZ2aKnctn51k8ks6eNeS4T2z+PDcAVSH5FIDj6KSnp6cbjTKd/8+Rn4wYwSDrw85+912Faubz8y6RLg0Rf+iQPFDZM7LF4sUUxlZg5+rVDc6Z9rhN0mqF6ndby7oVT1cqBVfftIzNw5vDhmEljak0IYc3lFAxRh2pP7v5ySfAkaH2fA7fXuC3Azs4jraPwmp0QQxiEhP1O7SfqWNLc/PZAkd///kUgOOYmL/4aAkg6/PP9Tu0nwUOnjBBarEcDT4F4DgUwhwfctpt+i0hweCp1Wbml8711Xb7nbVfuAHg2BVsIZ5CpsFAM/A8++vKFfY+miHlyBEhqm9o5jGxeZJabe+59hwFbgDqOfY+B61ALIRA4GZ4A/iokjr7pRay/sBjAByOEyO5B+DoUVQOx5HhHgCH48RI7gFwnIPmrwYkvZrr5XVnvqmowaMvvmiaS7/QnE6dMBED8UKnTjSOtURQy5Z4Gu50tlEj+CAPQxo3LmsgH3749vZtXEQRa/PHH2w2XUL2pUuYgbX46dAhdh17aMnBgz4R7h7G3T/88Ot7R45sDioslLrf9g43ABwrQOQ1Rnk6MviVV+gVtp6FvvVWwS22B+369aOOdBnhcnnZnC4Ne3EOAAhYU66Z8sm+zTfxEFq0oC0AoFCUFvTtSwChO6B/o+gxl44Ggzxbka1iGzawPFmWacGKFQVvHU/IHJWVVVqfb5wSkNwA8Dl+/cCrj1Kh8gkNpe4gIDGRpiKaNenQAQsJOGauFG59Of7OxktDETlkCIFBhiFDvAcqrkSc0+lYJoXhX9OmGQq0Ok1merrUepMayQ0AxzF5qLVya79evr4uQ9kxo9vSpZSItZD17Im9UksmwhbqTR8rFCQDQZaWJl+o2KfauX278apLaskTw4bdmZm7aMuz165JLaat4UFATo0oda0jI10v4bTx15wcSqTSgV9Nyk4Nqtln+O7LLymaTtGLgwYZ35WdYj7t22NkcQglNm2q17u7NW/m7i78Kfy7UI/8EYOYwYOxmLVn7RcuZBlgLOHCherKQWNoBFJ69XJdaOzuuj4nx7ulMj9if0SE1Pq1NfwwkINjK/3LFwY8r9o5fjyNYa5ImTmzyh88y2bid6ORtYI7G5GWRr/iA8pesEC/T3denfzDD9bRBpG3q/87EY+9+CJ+pEb09ciRGI4QzIqJwWmagCYuLuKKLD1bwDQsnkWOH28I0gVpGicm2ov+rYXkUwBHO83mbFmBvYcoT0cGz5qFMSyapVR9yo69h0ns/3bsoPX0Mq6NHm3I1vpogk+fto20jOlLTnyt+fXAAbwAoPeBA/KJ/i9H+U+bRsOx17g7JQUfUQjF9OhR4ad6aKEmou40lNSzZ3unKBarpsvl+lG6OPWkiRNtrHabwacAnEqRT1QeUjWbMAEbEc2aPGDgj2fz8eOdO2wZ4lmPuDjDXO0AzdM9e+ov2XLgi2OYcaLvphMnT+rf0TXSuIeGsu7saZYwfDhGYhQ+u3tXvF+UguwJE+RqxaiInrY7XmxruAHg/A/eaxRDIqb26UPpyGJ506aJVmzCNrCE336jrviCbQwJMURrh2pGP+C6bDvBoNFpNMGLFlGW7Cgd7tYNbmwiOt66JVafxlALpM6Y4eOu/DJybViY1PLXNdwAcAAAjR72S+ub/eSTrA1GYVRqKn5BKr0jq/h+mAc+W498U4OQkPxgnUYTnJ0ttfw1Jf9g7qKM4YcPsxHMS7Y6JETUEJj1YPoZC9mfqanC6ofU8tcVkscApJ7j8v6UUhIjK5E9kpxM3WkoDW3YsEIFs6tPXfEJ0Lu3Pkin2Rx06pTUcluKMEXwLgxIUi3p2xfT2Shs+P57LEAKpjVoINSjNgC2NWrkOgVjjLOTk/ExgO0DBkgtv6VwD8DJ8TmpmBl+49VXsZZm0+j+/cXqsTZ0iU388ENH/eJXhd7zeIL60qFDbBttpzXx8aIVp4AwJSamTG8ODl8GdHCsvYoiRPWF4J7U/a0pXtfbLg8f5u9f8Gju25lLT5yo7u+8kxQa1c1duzCNJuDdV16xmoAT8AdbcfUqfYbxsm1xcflF2hEZA7dts5V+uAfAqRzzOj6tRy9cGz1aanFqitxLqYgIj4mRDTK+LHs+J8dbqVSqfKpe1xdgqaa76DdqlKAHqwk6E43oLV9fNg/fsRVffWVrPUkeA3C0fQC2RioPir2Avri6bp1hoVZmD8t51UUY+NSJbaaXV6/GTxSOva6l73n3sWO9lUqlKgvQa7Vadf6nn4q1Y8jJG6Den5fnvUL5r4j9GzbgA4CSrDjnH4MLcHviCVvri3sAnEqhh/EbbVuwQGo5qovXAcUzqvjXX6904N/PIywTkz/+2GtFQFLUJ8JpQnFYAW2nu46jh5oiuQfAsS7lPQivHP9+Efu7d5d1lZ2lpN27y9cX9uobgnTNM54/eFBq+aui7Is/iW1Gi1WrRAe+MKU5Sq1Yp7ffLth93LQpWKerqn3D8OMn1aH798uXKw6oXrh0iUbR+3i8Zcvy9Uz7TG1YwiuvFASe2KgJFo4dV8TePF7uATgZdJlaYHrnzqLl+aw3rgs3+9jPi1qeB7r69yMM/HMUyRKHDMn/U2vSBKem1uxpjNFp2gx9JTceCXorofHw69RJar3UFMk9AL5KYFtoHN3Cto4dAQBRlZSvlA2iqftL8+6OlVraith24N+nl4X4nC3dt48B/pT03nsVyv9Fx2iGWa8OBPcAnI0Q7KGQitd4C5SElaSyMSdPSi1meaQa+ALE4C+b+4CNT31xm/Vr1UpqPdUUbgCcjUbsGCbcl2uvHC7HTHvo1JUrlj5GWH+3uJ3qBvdeYJkIKSkxzcO/0HHw4Loa+ALGp13+4+b688+iFRhSoGzSpK6eZyu4AXA6SI1m4i+qfrasm9skvb62rXszJVOxKVNkfY2NZadyc71vKGaG34iNrWk7Zev4k7CrRsG9l3Tn1cnr1tW11gw5hhxDTn6+aAXGVlIbxzMAkscA7C0qam/UeYzkKlYg1nzI5x91J6cw8OGDAEROngwQ0ARAHzZT1n/58lJDAOib6yZkNl+1SqwdqV39WpNHk8oSjtyUTIoawz0AZ2MN+sNd/AvvPc60p3i6t3d1mytbT+/BLiO5ktt5hUw8b2CwbNWyZYJLX6EdO3H1xZAHygPlgT4+tdWrvcINgLPhhvHoLH7+3XjPY7Fp1pNPVre5greOJ2yaq9Ox25SHTW+8IQzQChXNA1r2Ib7A4dRUYWpgb66+GC4XjR8UlzxAL6HsSUy/ft1W8tQV3AA4Gew6mwvNmTNi5a4/mCZRz+efr2m7hhxtb/X+9etN09ED12JjRQ1BOY/AXr/45TGdYQONXz1AL8doBMLPnbO1XJYieQyA7wOwLRRDvyPJfCouPbxCpn72ITvPzgcH4yAArF5d0/aFL7M8UJmnCjYa6QWWiag1ayoMcLEBL1Dhi681aYJt98WvwDhaSzu6dhUtv47rQE4OyPITtraEewBOBuXTVrZgzx6xctaMjqCHcOy39oZZ8AiqnBqUx96Ce2Y9sEB8js7ix6FNY3GGuu7bJ62cNYcbACfD44mSeA/f/fvFkmJSJIiSWrUqS69tIdU2BHY58IV06MHBNAFj6ZOnnipfzjSYjMH5+QVz7nx377p5B6UDwQ2Ak/Fbet6A9PUGA7rCFx+q1aIVhbz6dUSZIZiDW4iPiRESYWAOWqH4yhW2CI9jX3S0vQx8ASpkLmgyapRo+UB8gJisLODc6G3n7t2TWt6aInkMgO8DeDDWipHQNJrGSpYvZ2AgVFyWEy7UEPLqC7nzLH2uIUgXpKaMjNK/ZWQAuGouUtvT7LlseXMmMxqf6N8fQGql4vmzQ0ju08dro6KHKv711229OmEp3ANwUvIPHj+oObh9O5sMIOvIkQoVzNF6CpKNM700f77U8toa2Q3Ts6Yx8+aJZkcWEFnelFr+avdTagE4EvMyW4a/ZswQLX8TwI3QUHmEIiJi//vvSy2utfHprHwoovPIkTXOBVjNDU/2BjcATo7gkrNP2UCYHnDe3Z/60kNffOHTue37kQsd79hrVXiPU9yIPNmlC+uAf1KPuXNFK/ZhW9m/dboqNzyZNzbZuyGQPAbA9wHYBy5zaK3LzBEjjFlsWYnv0aMV7gcw58lnbsaJrOOWLfKJ/n2j/ENC6io2IBVlpxZ9TavZwMxMPIS9aOnhUaGi+eIQ47WSxi4NX3uNEl0DTNdCQmST2C6EVLKDscwQlJZ7HVD0UMUD6AXgvNS9/hvuAXAAAH/9pdVuDLhwgfnijOyjuDjRisU0A4ebNqUvqcA0aO9e78KAJFVLx8uEI3zxZb6mP2Q/7dmDh/ANWlZymu9xDGZfm0zsKm02vTxsWOG+U16bHvn1VyHYV+XOx3IegdT9Lg83AJz/oeBR3aCMFWlpyMdxZEydKlrRbAgwnTXEhu+/lwcG9Iv8ZPhwqeV/MERlc/x8msZWZWWJDnyBfmwo1ickGDy1KzNfqrhsWlNDILUGysMNAKdS9KQlNU2ZwrpgOFuUkiJa0Tw1oPPsLDv75ZfChRryQL80VbCfn9T9EJbzvN9TzlM12b2bnUBrap6SghXYC10lrr5AF8QgJjFRP1vXXPN+cnJVz6m2IbAzuAHgPBDDDu0IzWOjR1fpEQiYo+d0x+UCy9Bqvbcqt6qC166VZwfcjno4OLi0krViPkSlO/e6dvX+t1KhOpaWJpvJjMZPc3OxGksR/PLLoj81u/qlG5Pi4/U7tJ+pY2t+LbijGQJ+NZiDY2v9C8d3sYctx7dLloheJioCW8F+QveLF+kwTcWiHTuEZJtCzj3TiOIQzL50qXxmInn2Q4l3PvXxcelRVOw646mnyk7nmQ/psBC2iOa/9ppY2m5RhFuBfSiMdXvnHf0lrY8mWKOpK30JqwBVHnc2Y9rOCvHMwIG22lBU5Ysh3BeP45hDHb/6CgsoFItatLC2YPWWOr4LTioD3HB6QFJ4ytNPG9uwsdTuP/+hd+FPSWFhtlKjxZiX84SovhDcs9bjqm0IhOPPZg/C2oag6inAZdqFjxYt4gO/jpD4Lri64q9JxxMyR128aBioNWmCe/dGExxgCSoVe5t9gS7HjkktX5XchJHUzz1Hia4BptkhIdZ+XG1XDay9j6BqA2B+Ya2tIKdDorvgrIXgOhtSdKHqTzt0oBTTFmrTsycLY0ns9TVrMJ7Nx4937lhbDuF0Hu6wtQjWaOxloAnYmyHgQUCOFWAs/+0TT2bM3bHDkKbrpRk0aJC8sccnxq1NmwqGASPZo2zJrFnwxE1kqNVogV5s1cmT7AYC2ZzbtxGJhjhTVCT8yc4CCPvjj7KdeObfMS/mz8KnTDENx3r6PTTU0P3OL0XjmzXTl+j81Qkqlb0MtPLU1hDIBwT0iGzXv39dyVF1DIAH+SzC2vrj/z/VQx6o3KoKjo6mxsyv0gxFAsJ14FugNW14++2qshjbXD5zHgdyNV0wTQgJyZ9+IjFz+08/1fa53APgOAXVTkwiHOrpA+Xf6cytf7qv2vIJW7LP0ge04JtvAGAyk9V6HFf5Q7YKKniIpzt+JMYvLSZaLre2gjicuqC2yUttNTUQ5MMapJpihw6FN5SozMPbQr3pY4XC67pide6Q6OjaPq9qyxHGBmKz+PLI3W9dLxdlW34FFIdjS+wtGFceYerBzsEFPyxfLlZPNg89WFFMTG2fU7UB+BB+aJadLVbMIpkX+3HwYGsrxFEpC16J0ET97D8j9lf/Ig6BRg93aB8TLb4BpyrPjVOKvRsC023TCzLVvHmiFbZSK+yr/TJm1VOAyVhLIeK546gNTtCCuDjPNL80FQsMtLZCHI4F7Ef0v3hRrPie3u08hYSG1rTZ4teKGxWfF/8drWHvsVmXL0vdfUfBXg1Boe+J0ZvCjx8vWxUpz29QYfojjwB+aTHR7u41bb9KA1DwqMnVrTAjg2UhCLnnK55kNh+qcDkm64btmzdzQ1COLvQh7nz/vVgxvUuH8e7s2T6d/dJiYsRv7RUoq7fAFMvWzZ4tVo+9SVPIfccOqbvvaNirIbAW1V4ekq9SdFPJoqJoON1G+MaNohWFdVstvCh/8WIKYyuwc/XqBudMe9wmabVlWWmdBJ/OfmkRnVu3Zn+4XMDm06dFc8yZtwiz6XgYsfHx7iF34opP/p2hp8jbU+3h/eqrwsCnZygZCW3aVGjHvIzFSkx3gYAAQ07eAPX+vDyp9eCoSL2FV/igurzjMh2RleywDMTDeObGDf0+7T518qOP1rT9Gq8PezdVnIqY8PnnuEcxpBszpu5UzakL2DAWiox58wxf6L5Q00cfSS1PfUEqQyDPVmSr2PLl1J2GInLIkAoVpoBhSnq6/mOtTt1uwICatl/j9UP9Ld1z7WaMGwcPls4Uc+bUvao5tWIlgOY7dxq+8NjTPC4hQWpx6hu1nRrUdh+Bd0PF+xFv/vOfFEHJWPbWW2L12GcA+yw9vbb9qtUGgqlkMulv6Z7TzBw7lmWxZciIimJHMRxKx7sc0WExv4DCF18f6e7WnPXuDRw5unhJcbHU4tVXapwBqB2dkG1ZsUJeqHxTNeybbzyv+s+Pymzbtnx1wdUXvvjwonzqvnQp9NCisp2cI9EaEcePGwq0unaaDRtq25863CLaoX3cu25u8sCiGTdOqlToi2QMiYykpSiCf4cObC58EOzrS8NxGeF841B1YQvxFDINBtrMFrIzly6VBfcmGHti0Ndf8zm+tNT0vH8Z5aP6GfgL/6g6is8msj14vLDQdMOlOTMEBxcm5xZrVtb+9KXN94jzveuc+kjZXv4wXMW8lSuFLbt19gDzGQDWn7VH+zfe+N8blmoPPwvA4dQBwhZeypIdpcPdupWdWrQUczvC4Z+6GvgC3ABwOHVI/sHcRRnDDx/Wr30+3mNcYCALo2b0c3Q0RrKdeGj9+iobMNcTfie0Y+mpPzH4FIDDsSH29v5zD4DDcWK4AeBwnBhuADgcJ4YbAA7HieEGgMNxYrgB4HCcGG4AOBwnhhsADseJsbv7yqvaKMHhcOoO7gFwOE6M7Q2AOfWV1B3ncOyKOWiF4itXbP1YmxsA4Vpsbgg4HJQNfPqWbrDbcXFSi8PhcDgcDofD4XA4HA6nfvL/hPBEh58whjgAAAAldEVYdGRhdGU6Y3JlYXRlADIwMjEtMDktMTVUMTY6MzE6MjgrMDA6MDACCPM0AAAAJXRFWHRkYXRlOm1vZGlmeQAyMDIxLTA5LTE1VDE2OjMxOjI4KzAwOjAwc1VLiAAAAABJRU5ErkJggg=="
    }, {
        title:``,
        name:`They can join a team made up of five collectibles that represent defenders, goalkeepers, strikers, and midfielders to play games and competitions. The winner will earn prizes and awards based on the total number of points accumulated at the end of the tournament`,
        image:"data:image/png;base64,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"
    }, {
        title:``,
        name:`New tournament updates can be found on the sorare clone selling website. With any combination of their player cards, football gamers can also compete in one tournament available as part of a weekly job. The winners will receive Ethereum cryptocurrency and crypto-collectibles in their digital wallets as soon as possible.`,
        image:"/images/gaming.svg"
    }
]

const items2=[
    {
        title:`Crypto Wallet Added
        `,
        name:`We have equipped our Sorare clone with advanced wallets such as Metamask, MyEtherWallet, and others, which allow for instant and flawless trade.

        `,
        image:"data:image/png;base64,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"
    },{
        title:`Assets Collection
        `,
        name:`The trading cards that reflect real-life football players are the marketplace's distinguishing feature. Each card is one-of-a-kind, and NFTs are used to trade them.

        `,
        image:"/images/asset.svg"
    },{
        title:`Attracting Gaming Interface
        `,
        name:`Users can build their own team and compete in leagues, series, and tournaments thanks to the simple gaming interface.

        `,
        image:"/images/gaming.svg"
    },{
        title:`Attracting Store Front
        `,
        name:`The shop has a nice selection of trading cards and digital artifacts from the players. Not to mention that the transactions are all decentralized.

        `,
        image:"/images/storeFont.svg"
    },{
        title:`Inside Exchange Market
        `,
        name:`Users can not only buy assets, but they can also sell them, thanks to this functionality built into our solution.

        `,
        image:"/images/market.svg"
    }
]
return (<>
<TopNav/>
<SideContact/>

{/* page start */}

<div style={{width:'100%',minWidth:"100vw",background:"white",minHeight:"100vh",height:"auto"}}>
    
<br/><br/>
<br/><br/>

<Header image={"https://blockchaintechs.io/wp-content/uploads/2022/06/NFT-Defi-platform-Copy-4.webp"}
title='Sorare Like NFT Marketplace Development'
 text='We provide best-in-class SoRare Like NFT marketplace development services and have extensive expertise in delivering business-specific solutions'
 textColor='black' />

 <div style={{padding:10}}>

<br/>
<div style={{maxWidth:1524,margin:"0 auto",width:"90%"}}> 
<Items title={`Create an NFT Fantasy Football Marketplace similar to Sorare`} p={`You’re all aware that the fantasy industry and the NFT marketplace company are currently the two hottest digital business options. What if they combined their efforts to provide a double treat for business aficionados and entrepreneurs? It is currently taking place. Sorare is the name of the creature. Sorare is a fantasy football NFT game in which you can trade players for thrilling prizes and digital collections. 

This is for you if you are an entrepreneur or a startup looking to build an interesting NFT business on the fantasy sports platform. In this article, we’ll show you how to build your own NFT marketplace like Sorare, where you can attract and entertain your users while also earning a lot of money.
`} items={[]}/>

<div className='d-flex m-block ' style={{maxWidth:"max-content",margin:"0 auto",gap:20}}>
<Link href={whatsappLink}>

<MDBBtn style={{background:"var(--green)"}}>Contact with whatsapp</MDBBtn>
</Link>
<Link href={telegramLink}>
<MDBBtn style={{background:"var(--blue)"}}>Contact with our experts</MDBBtn>
</Link>
</div>

<br/><br/>
<H3>White Label Sorare Development
</H3>
<div className='d-flex align-items-center m-block' style={{width:"max-content",maxWidth:"90vw"}}>
<p>White-label product Sorare Development might be an NFT gaming platform that wants to launch its blockchain-powered NFT gaming platform rapidly but lacks the time and resources to design a custom business solution. Anyone can easily construct a worldwide Fantasy football like Sorare by developing a white-labeled Sorare Development business plan.

</p>
</div>


<br/><br/>
<div className="d-flex m-block ">
<Image src="https://blockchaintechs.io/wp-content/uploads/2022/06/A-New-Crypto-Collectible-To-Be-Cherished-By-The-Football-Fans.webp"
width={500}
height={500}
alt=''
style={{width:"50%"}}
/>

<div className='m-block' style={{width:"50%"}}>
{[
    "A football player is represented by the Sorare Clone card, which is a digital collectible. Users can enter contests and win prizes by using these trading cards. These NFTs can be owned indefinitely once purchased unless the user sells them on a marketplace like OpenSea or Rarible.",
    "Every football player has a card, the worth of which is determined by the star’s performance in the real world. Trading cards of football players such as Messi, Neymar, and Ronaldo, for example, can have a higher value than cards of other players.",
    "We’ve included several extra elements that can increase the value of the cards in addition to the player’s records. The valuable cards are divided into three categories. Meager, Super Sparse, and Sparse are three different types of sparseness."
].map((text,i)=>(<div key={i} className='surItem'>
{text}
    </div>))}
</div>
    </div>

<H3>NFT Marketplace Like Sorare
</H3>
<div className='d-flex m-block align-items-center'>
<p>
A football player is represented by the Sorare Clone card, which is a digital collectible. Users can enter contests and win prizes by using these trading cards. These NFTs can be owned indefinitely once purchased unless the user sells them on a marketplace like OpenSea or Rarible.

Every football player has a card, the worth of which is determined by the star’s performance in the real world. Trading cards of football players such as Messi, Neymar, and Ronaldo, for example, can have a higher value than cards of other players.

We’ve included several extra elements that can increase the value of the cards in addition to the player’s records. The valuable cards are divided into three categories. Meager, Super Sparse, and Sparse are three different types of sparseness.
</p>

<Image src="https://blockchaintechs.io/wp-content/uploads/2022/06/NFT-Marketplace-Like-Sorare.webp"
width={500}
height={500}
alt=''
style={{width:"50%"}}
/>
    </div>

<br/><br/>

<div style={{borderRadius:20,padding:20}}>
<H3>Features of Sorare NFT Marketplace</H3>
{[
    "Transparency at a high level.",
    "For the players, there are no operational risks.",
    "The security of your investment is guaranteed.",
    "Interface and leaderboard are user-friendly.",
    "Excellent Graphics for Gaming.",
    "System of Bonus Points.",
    "While playing, you'll get a realistic feeling.",
    "Earn Real Money and Benefits.",
    "Integrated payment methods make it simple to place bets."
].map((e,i)=>(<div key={i}> <span style={{padding:10}}><Forward10Sharp/></span> {e}
</div>
))}

</div>

<br/><br/>

<div style={{borderRadius:20,padding:20}}>
<H3>How to Buy Collectibles on Sorare Clone Script?
</H3>
{[
    "In the Transfer market, place a bid for collectibles.",
    "Prices are comparable.",
    "The payment method is either a credit card or ETH.",
    "Funds will be reimbursed if the bid is unsuccessful.",
    "The auction is won by the highest bidder.",
    "Within his gallery, the winner lists his collectible.",
    "In the case of credit card transactions, double-checking is required."
].map((e,i)=>(<div key={i}> <span style={{padding:10}}><FaAngleLeft/></span> {e}
</div>
))}

</div>

<br/><br/>
<Items title={`How does the sorare clone function?`} p={``} items={items1}/>

<br/><br/><Items title={`Special features of the Sorare like NFT Marketplace
`} p={`Real-time updates on football player performance are shared, allowing spectators to make informed decisions based on the available data. There is no risk of cheating or fraud because the same crypto collectible cannot be used by players in two different teams.

Football fans can expect a high level of transparency in the information provided about points and scores. The participants have no operational risks, which encourages them to participate on a regular basis and creates trust. Football fans may be certain that their money is safe because the Sorare clone script only allows them to purchase licensed cards from approved and certified clubs.

Other benefits of the sorare clone include a bonus point system. Graphics that pop, teams that can be customized, and a more user-friendly interface.`}
items={[]}/>

<br/><br/>
<Contacts/>
<br/><br/>
<Items title={`What can we do to make our Sorare clone more interesting for the users?
`} p={``} items={items2}/>

<br/><br/>
<H3>Our Technology Development Stack
</H3>
<br/><br/>
<TechCarousel techImages={techImages}/>
</div>


</div>



</div>


<br/>

<br/><br/>
<Testimonials/>
<br/><br/>
<ConnectWithUs/>
<br/><br/>
</>)
}


