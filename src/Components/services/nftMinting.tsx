import React from 'react'
import TopNav from "../fixed/TopNav";
import SideContact from "../fixed/sideContact";
import Testimonials from "../LandingPage/testimonials";
import ConnectWithUs from "../LandingPage/connectWithUs";
import "../LandingPage/style.css";

import { useState } from "react";
import Header from "./header";
import "../Portfolio/style.css"
import "./style.css";
import Contacts from "./contact";
import CertificationApproach from './certificationApproach';
import Items, { techImages } from './items';
import Bold from '@/utils/pBolder';
import WhiteBold from '@/utils/WhiteBold';
import ResponsiveImage from '@/utils/ResposiveImage';
import TechCarousel from '../LandingPage/TechCarousal';

export default function NftMinting(){
const [width,setWidth]=useState(typeof window=='object' ? window.innerWidth :0);

return (<>
<TopNav/>
<SideContact/>

{/* page start */}

<div style={{width:'100%',minWidth:"100vw",background:"white",minHeight:"100vh",height:"auto"}}>
    
<br/><br/>
<br/><br/>

<Header image={"https://blockchaintechs.io/wp-content/uploads/2022/07/NFT-Minting-Platform-Development-Services.webp"} 
title='NFT Minting Platform Development Services'
 text='Users can create and sell NFTs without having to know any programming languages with our NFT Minting Platform. You may generate billions of dollars in revenue using our NFT minting platform.' />

 <div style={{padding:10}}>

<br/><br/><br/>

<div className='d-flex justify-content-center'><WhiteBold>NFT Minting Platform Development Company</WhiteBold>
</div>

<div className='d-flex align-items-center justify-content-between m-block' style={{gap:15}}>
    
<div style={{width:"40%"}} className='d-flex justify-content-center'>
<ResponsiveImage containerSize={300} src='https://blockchaintechs.io/wp-content/uploads/2022/07/NFT-Minting-Platform-Development-Company.webp'/>
</div>

    <div style={{width:'60%'}}>
<br/>
<p>
We offer efficient NFT Minting Platform Development Services and Solutions with cutting-edge features to support users in minting their digital assets, such as music, video, in-game items, and articles, as an NFT to fully utilize their work. You will receive a user-friendly, cutting-edge, We also provide custom white label NFT Minting platform from our experienced blockchain developers that may be tailored to your specific business needs and requirements.
</p>
<br/><br/>

<p>
Due to its cutting-edge technology and in-depth understanding of blockchain technology, We are one of the leaders in NFT Platform Development Services. In order to give you a completely functional and feature-rich NFT minting platform or website, our staff makes use of its high-end knowledge. We support you fully so that you can prosper and earn a lot of money in the NFT sector.
</p>

</div>

</div>
<br/><br/>
<div className='d-flex justify-content-center'><WhiteBold>Token Standards For NFT Minting Platform Development</WhiteBold>
</div>
<div style={{width:"90%",margin:"0 auto",padding:16,gap:10,flexFlow:"row wrap"}} className='d-flex m-block'>
   {[
    {
        name:"ERC 721",
        text:"The unique NFTs assets are designed to be represented digitally using the ERC-721 token standard, which is based on Ethereum. The coin has the newest features, which further increases asset value.        ",
        image:'/images/blackCrypto.svg'
    },{
        name:"ERC 998",
        text:"It is an ERC-721 extension that enables the creation of a new coin inside the same network using a collection of ERC-721 assets. This signifies that a user can buy any form of token, including numerous types, in a single transaction.",
        image:'/images/blackCrypto.svg'
    },{
        name:"ERC 1155",
        text:"In ERC 1155, Multiple tokens can be managed simultaneously by an NFT standard. The semi-fungibility feature makes it simple for users to transfer and trade NFTs. It gives unique assets an amount of possibilities.",
        image:'/images/blackCrypto.svg'
    },{
        name:"TRC 721",
        text:"TRC - 721, a blockchain-based token introduced by TRON Network, offers security, scalability, and transparency. The token standard maintains a distinct identity and permits the transfer of collectibles with a constant value.",
        image:'/images/redCrypto.svg'
    },
   ].map((e:any,i:number)=>(<><div key={i} style={{width:'47%',borderRadius:17}} className='newBoxOfColor'>
<br/>
<div className='iconContainer'>
    <ResponsiveImage src={e.image} containerSize={50}/>
</div>

<br/>

<b>{e.name}</b>
<br/>
<p>
{e.text}
</p>


    </div><br/></>))}
</div>

<br/><br/>
<br/><br/>
<div style={{background:''}}>


<Items title={`Most Trending Mintable NFTs in the Crypto Space`} p={``} items={[{
    title:"Digital Arts & Collectibles",
    name:"The digital arts and pics are unique and rare collectibles, consumers are attracted to them. Minting digital works as NFTs gives your company a large cash flow.",
    image:"/images/digital.svg"
},{
    title:"Gaming Assets",
    name:"They are specific items that were created with the gaming industry in mind. In order to provide the most realistic gaming experience possible while utilizing NFTs, game assets can be either skins, guns, weapons, vehicles, or characters.",
    image:"/images/gaming.svg"
},{
    title:"Sports Accessories",
    name:"NFTs, sports items and their models can be highly valued. Such an NFT type has a lot of potential appeal in the cryptocurrency world. Such NFTs are minted to assist the launch of new items by brands.",
    image:"/images/sport.svg"
},{
    title:"Virtual Land and Infrastructure",
    name:"Investment in digital assets has surged as a result of the craze for NFTs, which were originally regarded as cryptocurrencies due to their intrinsic worth. Virtual lands are resources that offer lands, lodging, dining, and other NFTs in a decentralized environment.",
    image:"/images/vLand.svg"
}]}/>

</div>

<br/>
<br/>
<br/>
<WhiteBold>Blockchain Technologies NFT Minting Software Development</WhiteBold>

<div className='d-flex align-items-center justify-content-between m-block' style={{gap:15}}>
    
<div style={{width:"40%"}} className='d-flex justify-content-center'>
<ResponsiveImage containerSize={300} src='https://blockchaintechs.io/wp-content/uploads/2022/07/Blockchaintechs-NFT-Minting-Software-Development.webp'/>
</div>

    <div style={{width:'60%'}}>
    NFT Minting Software is a market-ready, ready-to-deploy NFT minting software solution that has already been designed, developed, and tested. Within a few weeks, users can establish their own NFT minting platform using the NFT Minting Software’s White Label option. 
    <br/><br/>
    Here, people can create their own digital tokens, such as works of art, music, games, and collectibles. Through its distinctive features, it provides the highest market visibility in the cryptosphere. 
    <br/><br/>
    Our NFT development team can assist you in meeting the rising demand from musicians, artists, and individuals because they are familiar with the NFT industrys.

<br/><br/>


    </div>
</div>


<br/><br/>
<Contacts/>
<br/><br/>







<div style={{maxWidth:1524,margin:"0 auto",width:"90%"}}> 
<Items title={`Features of NFT Minting Platform Development`} p={`Our NFT minting website developers introduce an NFT minting platform for top blockchains networks such Tron, Polygon, Solana, Binance Smart Chain (BSC), Ethereum, and others. Let us see the features in our NFT Minting platform. We can customize based on your needs and requirements.`}
 items={[
    {
        name:"The platform enables users to create a single NFT or multiple NFTs by simply entering the appropriate NFT creator information.",
        title:"Instant Minting        ",
        image:"https://blockchaintechs.io/wp-content/uploads/2022/07/Instant-Minting.svg"
    },{
        name:"A complete understanding of the platform's features, functionalities, and newly created NFTs is available to users.",
        title:"Storefront",
        image:"https://blockchaintechs.io/wp-content/uploads/2022/07/Storefront.svg"
    },{
        name:"The platform has an option for multiple wallet integration, which enables users to effortlessly access different parts of the platform.",
        title:"Multi Wallet Integration",
        image:"https://blockchaintechs.io/wp-content/uploads/2022/07/Multi-Wallet-Integration.svg"
    },{
        name:"With cutting-edge features and capabilities that provide high ROI to NFT minting platform owners.",
        title:"High ROI",
        image:"https://blockchaintechs.io/wp-content/uploads/2022/07/High-ROI.svg"
    },{
        name:"Users can simply sell their newly created NFTs on the site thanks to the marketplace.",
        title:"Marketplace",
        image:"https://blockchaintechs.io/wp-content/uploads/2022/07/Marketplace.svg"
    },{
        name:"Huge amounts of traffic would be allowed on the platform thanks to the multichain interoperability feature, which would also help to ensure smooth revenue flow.",
        title:"Multi-Chain System",
        image:"https://blockchaintechs.io/wp-content/uploads/2022/07/Multi-Chain-System.svg"
    },
 ]}/>
<br/><br/>
<br/>
<div className='d-flex justify-content-center'>
<WhiteBold>Our Technology Development Stack</WhiteBold>
</div>
<br/>

<TechCarousel techImages={techImages}/>
<br/><br/>
<br/><br/>

</div>


</div>



</div>


<br/>

<br/><br/>
<Testimonials/>
<br/><br/>
<ConnectWithUs/>
<br/><br/>
</>)
}


