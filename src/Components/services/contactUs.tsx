import React from 'react'
import TopNav from "../fixed/TopNav";
import SideContact from "../fixed/sideContact";
import Testimonials from "../LandingPage/testimonials";
import ConnectWithUs from "../LandingPage/connectWithUs";
import "../LandingPage/style.css";

import { useState } from "react";
import Header from "./header";
import "../Portfolio/style.css"
import "./style.css";
import Contacts from "./contact";
import CertificationApproach from './certificationApproach';
import Items from './items';

export default function ContactUs(){
const [width,setWidth]=useState(typeof window=='object' ? window.innerWidth :0);

return (<>
<TopNav/>
<SideContact/>

{/* page start */}


<br/>

<br/><br/>
<br/><br/>
<ConnectWithUs/>
<br/><br/>
</>)
}


