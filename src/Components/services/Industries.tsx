import React from 'react'
import H3 from '../Portfolio/h3';
import Image from 'next/image';
const Industries:React.FC=()=>{

    const categories = [
        { name: "Finance", image: "https://blockchaintechs.io/wp-content/uploads/2021/09/Fintech-1.png" },
        { name: "Health Care", image: "https://blockchaintechs.io/wp-content/uploads/2021/09/Healthcare-1.png" },
        { name: "Logistics", image: "https://blockchaintechs.io/wp-content/uploads/2021/09/Supplychain.png" },
        { name: "Real Estate", image: "https://blockchaintechs.io/wp-content/uploads/2021/09/real-estate.png" },
        { name: "Marketplace", image: "https://blockchaintechs.io/wp-content/uploads/2021/09/real-estate.png" },
        { name: "Insurance", image: "https://blockchaintechs.io/wp-content/uploads/2021/09/Insurance-1.png" },
        { name: "Education", image: "https://blockchaintechs.io/wp-content/uploads/2021/09/Education-1.png" },
        { name: "Media & Entertainment", image: "https://blockchaintechs.io/wp-content/uploads/2021/09/Media-entertainment.png" },
        { name: "Social Media", image: "https://blockchaintechs.io/wp-content/uploads/2021/09/Social-networking.png" },
        { name: "Tourism", image: "https://blockchaintechs.io/wp-content/uploads/2021/09/travel.png" },
        { name: "Games", image: "https://blockchaintechs.io/wp-content/uploads/2021/09/Games-1.png" },
        { name: "Lifestyle", image: "https://blockchaintechs.io/wp-content/uploads/2021/09/lifestyle.png" }
    ];

return (
    <>

    <br/>
    <H3>
    Industries We Transformed by Blockchain Application
    </H3>
    <br/>
    <div className='industries d-flex ' style={{flexFlow:"row wrap",gap:15}}>


{categories.map((e,i)=>(<div key={i} className='industryItem d-flex align-items-end '
 style={{backgroundImage:`url(${e.image})`}}>
<h5>{e.name}</h5>
</div>))}


    </div>
    </>
)
}

export default Industries;