import React ,{useState,useEffect} from 'react'
import H3 from '../Portfolio/h3';
import { CheckCircle, CheckSquare, DollarSign, Award, Users, Settings, Zap } from 'react-feather';
import TechCarousel from '../LandingPage/TechCarousal';



const WhyChoose:React.FC=()=>{
const [width,setWidth]=useState<number>(0)

useEffect(()=>{
setWidth(window.innerWidth)
},[])
    const data = [
        {
            name: '5+ Proven Experience',
            icon: <Award size={60} style={{ color: "var(--blue)"}} />
        },
        {
            name: 'Highly Skilled Development Team',
            icon: <Users size={60}  style={{ color: "var(--blue)", fontSize: 'xx-large' }} />
        },
        {
            name: 'Solution for all Use Cases',
            icon: <Settings size={60}  style={{ color: "var(--blue)", fontSize: 'xx-large' }} />
        },
        {
            name: 'Sustainable Partnership',
            icon: <CheckCircle size={60}  style={{ color: "var(--blue)", fontSize: 'xx-large' }} />
        },
        {
            name: 'Post Launch Support',
            icon: <CheckCircle size={60}  style={{ color: "var(--blue)", fontSize: 'xx-large' }} />
        },
        {
            name: 'On-Time Delivery',
            icon: <Zap  size={60}  style={{ color: "var(--blue)", fontSize: 'xx-large' }} />
        },
        {
            name: 'Tailor-made Solutions',
            icon: <CheckSquare size={60}  style={{ color: "var(--blue)", fontSize: 'xx-large' }} />
        }
    ]

    const techImages=[
        "https://blockchaintechs.io/wp-content/uploads/2022/11/Ethereum.webp",
        "https://blockchaintechs.io/wp-content/uploads/2022/11/Polygon-1.webp",
        "https://blockchaintechs.io/wp-content/uploads/2022/11/Ripple.png",
        "https://blockchaintechs.io/wp-content/uploads/2022/11/Tron-1.webp",
        "https://blockchaintechs.io/wp-content/uploads/2022/11/XDC.webp",
        "https://blockchaintechs.io/wp-content/uploads/2022/11/Avalanche.webp",
        "https://blockchaintechs.io/wp-content/uploads/2022/11/Binance-1.webp",
        "https://blockchaintechs.io/wp-content/uploads/2022/11/Fantom.webp",
        "https://blockchaintechs.io/wp-content/uploads/2022/11/Chainlink-1.webp",
        "https://blockchaintechs.io/wp-content/uploads/2022/11/Cardano-1.webp",
        "https://blockchaintechs.io/wp-content/uploads/2022/11/XDC.webp",
        "https://blockchaintechs.io/wp-content/uploads/2022/11/Solana-1.webp",
        "https://blockchaintechs.io/wp-content/uploads/2022/11/Polkadott.webp",
        "https://blockchaintechs.io/wp-content/uploads/2022/11/Solana-1.webp",
        "https://blockchaintechs.io/wp-content/uploads/2022/11/Ripple.png",
        "https://blockchaintechs.io/wp-content/uploads/2022/11/Hyperledger-1.webp",
        "https://blockchaintechs.io/wp-content/uploads/2022/11/Stellar-1.webp"
    ]
    return (
        <>
        {/* <br/><br/>
        <H3>
        Why Choose AltAppLabs Technologies As Your Development Partner
        </H3>
        <br/>
        <div className='d-flex' style={{flexFlow:'row wrap',gap:15,maxWidth:1200,margin:"0 auto"}}>
        {data.map((e,i)=>(<div  key={i} className='whyIcon text-center' style={{margin:10,maxWidth:width< 600 ? '40%':undefined
        }}>
          {e.icon}
            <br/>
            <b style={{color:"var(--blue)"}}>{e.name}</b>
        </div>))}
        </div> */}

<br/>
<TechCarousel techImages={techImages}/>

        </>
    )
}

export default WhyChoose;