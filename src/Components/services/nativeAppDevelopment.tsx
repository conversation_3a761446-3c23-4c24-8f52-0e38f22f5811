import React from 'react'
import H3 from '../Portfolio/h3'
import PText from '@/utils/PText2'
import Image from 'next/image'
import Bold from '@/utils/pBolder'

const NativeAppDevelopment:React.FC=()=>{

const data=[
    {
        title:"Native Android App Development Services        ",
        text:`We are Blockchain Technologies a blockchain android application development company . Our comprehensive services are designed to help businesses make their processes as secure, scalable, and transparent as possible. We have in-depth knowledge and experience in dynamic mobile app development based on blockchain technology and are committed to providing first-class solutions to our clients around the world.

        Our team of skilled blockchain apps developers specializes in native languages like Java & Kotlin and other popular frameworks like React, Ionic, Flutter, and Xamarin. We also use blockchain as a security measure to protect applications from attacks and threats.
        
        `,
        image:"https://blockchaintechs.io/wp-content/uploads/2021/10/Andorid-app-development-1.png"
    },
    {
        title:'iOS App Development Services',
        text:`We are leading blockchain iOS app development company and have extensive experience in Blockchain app development services that help in creating iOS applications.

        Our team of blockchain application developers will create outstanding iOS applications as per your requirements.
        
        Whether it’s refurbishing your existing iOS application or developing an application from scratch, we will pay close attention to every detail you require and provide you with a dynamic application with first-class standards.`,
        image:"https://blockchaintechs.io/wp-content/uploads/2021/10/Ios-development-1.png"
    },
    {
        title:'Cross-Platform Application Development',
        text:`There are various mobile devices and platforms in the market and we are the leading cross-platform application development company , developing mobile applications for multiple platforms and multiple devices.

        We have the technical knowledge to meet your requirements, whether it’s developing cross-platform applications or using any other technology. We take the time to completely comprehend your requirements and employ agile software approaches to produce apps that satisfy the objectives of the company, industry, and users.
        
        Our apps are designed to work in a wide range of environments, making it easier for businesses to fulfill their requirements.`,
        image:"https://blockchaintechs.io/wp-content/uploads/2021/10/Cross-platform-app-develpment-1.png"
    },
    {
        title:'Crypto Wallet Development Services',
        text:`Our expert team of dApp developers can help you develop blockchain-based dApps and bring tangible results to your business and potential users`,
        image:"https://blockchaintechs.io/wp-content/uploads/2022/03/crypto-walet.webp"
    }
]

    return (<>
    <div className='NativeAppDevelopment '>
        <br/>
<H3>Our Blockchain App Development Services Sydney</H3>
<br/>


{data.map((e,i)=>(<div key={i} 
 className={`bigItem d-flex m-block align-items-center justify-content-between ${i%2===0 && 'flip'}`}>
    <div style={{width:"49%"}}>
        <b style={{padding:10,fontSize:'x-large'}}>{e.title}</b>
<PText style={{color:"dimgrey"}}>{e.text}</PText>
   
   </div>
<Image width={500} alt='' height={500} src={e.image}/>

   </div>))}


    </div>
    </>
    )
}

export default NativeAppDevelopment