import Bold from '@/utils/pBolder';
import PText from '@/utils/pText';
import { TextRotationAngledownSharp } from '@mui/icons-material';
import Image from 'next/image';
import React, { useState } from 'react'
import { ArrowDownCircle } from 'react-feather';
interface Props{
    image:string,
    content:string,
    name:string
}


const WhyChooseUsItem:React.FC<Props>=({image,content,name})=>{
    const [isShowContent,setIsShowContent]=useState<boolean>(false);
    return (
        <>
        <div className={`whyChooseUsItem  ${isShowContent ? 'active':''}`} style={{width:"100%",marginTop:10,background:"white",boxShadow:"1px 1px 10px 0px lightgrey",padding:16,borderRadius:10}}>

<div style={{cursor:"pointer",background:"white"}} onClick={()=>setIsShowContent(!isShowContent)} className='d-flex justify-content-between align-items-center'>
    <div className={`d-flex align-items-center`}> 

<b style={{padding:10}}>{name}</b>
</div>
<ArrowDownCircle/>
</div>

{isShowContent && <p>
    {content}
</p>}


        </div>
        </>
    )
}

export default WhyChooseUsItem;

export const whyChooseUsItems = [
    {
        name: "How does a P2P exchange work?",
        image: "",
        content: "A P2P exchange works on the fundamental concept of blockchain, i.e. decentralization. It allows buyers and sellers to trade directly, without any third party involvement."
    }, {
        name: "Why are businesses keen on building a P2P crypto exchange?",
        image: "",
        content: "Given that a P2P exchange eliminates third-party involvement, users enjoy better privacy and security. This is the reason why the majority of people prefer trading on a P2P exchange. More number of users opting for P2P trading means more revenue generation opportunities for platform owners."
    }, {
        name: "How much does it cost to build a P2P crypto exchange?",
        image: "",
        content: "The development cost depends on the features that you want to integrate into your exchange. However, the development cost and time required to build a white label exchange are comparatively less than that required to develop a custom exchange from scratch."
    }, {
        name: "What is the future of P2P trading?            ",
        image: "",
        content: "P2P exchange platforms are more reliable as they eliminate third-party dependency. While centralized exchanges have to adhere to stringent regulations and have high operational overheads and transaction fees, P2P crypto exchanges take up a lot of slack in the system and forge the path for next wave of adoption in this space.            "
    }
]