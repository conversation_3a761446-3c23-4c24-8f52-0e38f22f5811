import React from 'react'
import { MDBBtn } from "mdb-react-ui-kit";
import TopNav from "../fixed/TopNav";
import SideContact from "../fixed/sideContact";
import TakeYourBusiness from "../LandingPage/takeYourBusiness";
import WhyChooseUs from "../LandingPage/whyChooseUs";
import Testimonials from "../LandingPage/testimonials";
import Blogs from "../LandingPage/Blogs";
import ConnectWithUs from "../LandingPage/connectWithUs";
import "../LandingPage/style.css";

import H3 from "../Portfolio/h3";
import { useState } from "react";
import Header from "./header";
import "../Portfolio/style.css"
import TextItem from "./textItem";
import PText from "@/utils/PText2";
import "./style.css";
import NativeAppDevelopment from "./nativeAppDevelopment";
import Bold from "@/utils/pBolder";
import WeOffer from "./weOffer";
import Industries from "./Industries";
import Contacts from "./contact";
import WhyChoose from "./whyChoose";
import Image from 'next/image';
import WhiteBold from '@/utils/WhiteBold';
import CertificationApproach from './certificationApproach';
import Items from './items';
import { Check } from 'react-feather';

export default function CryptoWalletDev(){
const [width,setWidth]=useState(typeof window=='object' ? window.innerWidth :0);

return (<>
<TopNav/>
<SideContact/>

{/* page start */}

<div style={{width:'100%',minWidth:"100vw",background:"white",minHeight:"100vh",height:"auto"}}>
    
<br/><br/>
<br/><br/>

<Header image={"https://blockchaintechs.io/wp-content/uploads/2021/09/<EMAIL>"}
title='A cryptocurrency wallet Development software that allows users to earn, track, and send virtual currencies!'
 text=''
 textColor='black' />

 <div style={{padding:10}}>

<br/>
<div style={{maxWidth:1524,margin:"0 auto",width:"90%"}}> 
<Items items={[]} title={`Cryptocurrency Wallet Development Company`} p={`The desire to create new and secure crypto wallets has been steadily growing with the growing number of cryptocurrencies. Crypto wallets are the only places to do crypto transactions and check transaction history. Wallets do not keep money in a visible way. Instead, they hold transaction details for cryptocurrencies. Crypto wallets are also interactive – they can interact with other blockchain networks.

Blockchain Technologies is a well-known cryptocurrency wallet development company. Constantly trying to achieve something amazing has led us to focus on building high-quality crypto products such as more powerful cryptocurrency wallets, robust P2P crypto exchanges, and protecting crypto payment gateways.

Blockchain-based cryptocurrency wallets fall under various categories: web, mobile, desktop, hardware, and paper. Each fund operates separately, has its own benefits, and is distributed according to the needs of the business.`}/>

<br/><br/>
<Items title={`Security Measures in Cryptocurrency Wallet Development`} p={`Cryptocurrencies are gaining traction in our digital world through efficient power supply and transaction security promises. However, while transactions can be easy and difficult to deceive, cryptocurrency organizations still need to take security measures.

Cutting the middle officer reduces the cost of processing and speeds up the transfer of funds. However, with the exception of the central storage area, digital cryptocurrency rates are at risk of being completely eliminated by computer risk, hacking, and other unforeseen events.`}
items={[]} />

<br/><br/>

{[
    "DDoS Protection",
    "Authentication using two factors (2FA)",
    "Anti-Phishing Software is a program that detects and prevents phishing attacks",
    "Security of a Cold Wallet",
    "Browser Detection for Secure Socket Layer Integration Analysis And Tracking Of Security Logs",
    "End-to-end encrypted transactions with multi-signature vaults",
    "Protection Against Basic Security Vulnerabilities with Biometric Authentication, Automatic Session Logout, Email & OTP Authentication",
    "Implementation of a Hardware Security Module",
    "Implementation of a Key Management Server"
].map((e,i)=>{
return (<div key={i} className='d-flex align-items-center' style={{maxWidth:1024,margin:"0 auto"}}>

<div style={{padding:10}}><Check/></div>

<span>{e}</span>
</div>)
})}
<br/><br/>
<WhyChoose/>
<br/><br/>
<CertificationApproach/>
<br/><br/>
<Contacts/>
<br/><br/>

</div>


</div>



</div>


<br/>

<br/><br/>
<Testimonials/>
<br/><br/>
<ConnectWithUs/>
<br/><br/>
</>)
}


