import React from 'react'
import { MDBBtn } from "mdb-react-ui-kit";
import TopNav from "../fixed/TopNav";
import SideContact from "../fixed/sideContact";
import TakeYourBusiness from "../LandingPage/takeYourBusiness";
import WhyChooseUs from "../LandingPage/whyChooseUs";
import Testimonials from "../LandingPage/testimonials";
import Blogs from "../LandingPage/Blogs";
import ConnectWithUs from "../LandingPage/connectWithUs";
import "../LandingPage/style.css";

import H3 from "../Portfolio/h3";
import { useState } from "react";
import Header from "./header";
import "../Portfolio/style.css"
import TextItem from "./textItem";
import PText from "@/utils/PText2";
import "./style.css";
import NativeAppDevelopment from "./nativeAppDevelopment";
import Bold from "@/utils/pBolder";
import WeOffer from "./weOffer";
import Industries from "./Industries";
import Contacts from "./contact";
import WhyChoose from "./whyChoose";
import Image from 'next/image';
import WhiteBold from '@/utils/WhiteBold';
import CertificationApproach from './certificationApproach';
import Items from './items';

export default function PaymentGateway(){
const [width,setWidth]=useState(typeof window=='object' ? window.innerWidth :0);
const multiCrypto=[
    {
        title:`Exchange        `,
        name:`Develop bespoke bitcoin payment gateway solutions to enable cryptocurrency transactions for your community traders. We create powerful exchange payment gateway solutions with your choice of high-end security features!`,
        image:""
    },{
        title:`Digital Wallet        `,
        name:`If your company has a digital wallet, you can now conduct crypto transactions by simply integrating the cryptocurrency payment gateway. We can assist you with enabling the feature in an existing application without the requirement for application development`,
        image:""
    },{
        title:`Across Globe        `,
        name:`Serving a market that demanded speedier international P2P transactions. With our safe, ready-to-use gateway solutions, you can integrate cross-border cryptocurrency payments into your point-of-sale.        `,
        image:""
    }
]
return (<>
<TopNav/>
<SideContact/>

{/* page start */}

<div style={{width:'100%',minWidth:"100vw",background:"white",minHeight:"100vh",height:"auto"}}>
    
<br/><br/>
<br/><br/>

<Header image={"https://blockchaintechs.io/wp-content/uploads/2021/10/<EMAIL>"}
title='You can build a global customer base for your company with our development services'
 text=''
 textColor='black' />

 <div style={{padding:10}}>

<br/>
<div style={{maxWidth:1524,margin:"0 auto",width:"90%"}}> 
<Items title={`Crypto Payment Gateway Development Services`} items={[]} p={`Do you wish you could expand your business around the globe with no limits on cross-border payments? Here’s an easy-to-use method for making cross-border payments with no intermediary fees! Cryptocurrency payments have changed the modern world, allowing secure blockchain technology to enable peer-to-peer cross-border payments. Even industry behemoths like Amazon, Shopify, Wikipedia, Microsoft, and Overstock have integrated bitcoin payment gateway services into their current POS systems. Accepting, receiving, and storing digital currencies necessitates the use of a secure infrastructure.

We are a cryptocurrency development team with more than 5 years of experience in the field. Accepting several currencies at your point-of-sale will expand your global customer base. Let’s combine our expertise to incorporate bitcoin payment gateway services into your company, which can help you earn money all over the world!`}/>

<br/><br/>
<Items items={multiCrypto} title={`Multi Cryptocurrency Payment Gateway Development`}
p={``}

/>
<br/><br/>
<CertificationApproach/>
<br/><br/>
<Contacts/>
<br/><br/>

</div>


</div>



</div>


<br/>

<br/><br/>
<Testimonials/>
<br/><br/>
<ConnectWithUs/>
<br/><br/>
</>)
}


