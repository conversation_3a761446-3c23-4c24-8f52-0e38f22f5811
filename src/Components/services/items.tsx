import React from 'react'
import H3 from '../Portfolio/h3'
import PText from '@/utils/PText2'
import Image from 'next/image'
import Bold from '@/utils/pBolder'


interface items{
    
        name: string,
         title: string,
        image:string
}
interface Props{
    title:string,
    p:string,
    items:items[],
    itemStyle?:any
}

const Items: React.FC<Props> = ({title,p,items,itemStyle}) => {
   
return (
    <div className='weOffer' style={{ margin: "0 auto" }}>
        <br />
        <H3>{title}</H3>
        <br />
        <p style={{ textAlign: "center" }}>
            {p}      </p>
        <br />
        <br />
        <div style={{ gap: 10, margin: "0 auto", flexFlow: "row wrap", maxWidth: "max-content" }} className='d-flex m-block '>
            {items.map((e, i) => (<div key={i} className='offerItem' style={itemStyle ? itemStyle:{}}>
                <div style={{ height: 80 }}>
                    
                    {e?.image !="" && <Image width={500} height={500} alt='' src={e?.image || ''} />}

                </div>
                <H3 style={{ fontSize: 'large' }}> {e?.title} </H3>
                <p>
                    {e?.name}
                </p>

            </div>))}
        </div>


    </div>
)
}
 export  const techImages=[
    "https://blockchaintechs.io/wp-content/uploads/2022/11/Ethereum.webp",
    "https://blockchaintechs.io/wp-content/uploads/2022/11/Polygon-1.webp",
    "https://blockchaintechs.io/wp-content/uploads/2022/11/Ripple.png",
    "https://blockchaintechs.io/wp-content/uploads/2022/11/Tron-1.webp",
    "https://blockchaintechs.io/wp-content/uploads/2022/11/XDC.webp",
    "https://blockchaintechs.io/wp-content/uploads/2022/11/Avalanche.webp",
    "https://blockchaintechs.io/wp-content/uploads/2022/11/Binance-1.webp",
    "https://blockchaintechs.io/wp-content/uploads/2022/11/Fantom.webp",
    "https://blockchaintechs.io/wp-content/uploads/2022/11/Chainlink-1.webp",
    "https://blockchaintechs.io/wp-content/uploads/2022/11/Cardano-1.webp",
    "https://blockchaintechs.io/wp-content/uploads/2022/11/XDC.webp",
    "https://blockchaintechs.io/wp-content/uploads/2022/11/Solana-1.webp",
    "https://blockchaintechs.io/wp-content/uploads/2022/11/Polkadott.webp",
    "https://blockchaintechs.io/wp-content/uploads/2022/11/Solana-1.webp",
    "https://blockchaintechs.io/wp-content/uploads/2022/11/Ripple.png",
    "https://blockchaintechs.io/wp-content/uploads/2022/11/Hyperledger-1.webp",
    "https://blockchaintechs.io/wp-content/uploads/2022/11/Stellar-1.webp"
]
export default Items