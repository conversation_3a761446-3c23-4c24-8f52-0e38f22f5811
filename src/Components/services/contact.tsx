import Bold from '@/utils/pBolder'
import { MDBBtn } from 'mdb-react-ui-kit'
import Link from 'next/link';
import { useRouter } from 'next/router';
import React from 'react'
import { emailLink, phoneNumberUk, telegramLink, whatsappLink } from '../fixed/sideContact';

const Contacts:React.FC=()=>{
    const route=useRouter()
    return (
        <>
        <br/><br/>
<div className='contacts text-center'>
<Bold>Let’s Build a New Decentralized Future Together – Meet our CTO Now!</Bold>
<br/>
<small style={{color:"white"}}>You bring the idea & we’ll bring the experts!
Unveil the true potential of your business by using our unique blockchain solutions.</small>
<br/>
<br/>
<div className='d-flex align-items-center justify-content-between m-block' style={{gap:20,maxWidth:800,margin:"0 auto"}}>
<Link href={whatsappLink}>
    <MDBBtn size='lg' color='success' rounded>
        Contact via Whatsapp
    </MDBBtn>
    </Link>
<Link href={emailLink}>
    <MDBBtn size='lg' color='warning' onClick={()=>{
        route.push('/contactUs')
    }} rounded>
        Contact expert
    </MDBBtn> 
    </Link>

<Link href={telegramLink}>
    <MDBBtn size='lg' rounded>
        Contact via telegram
    </MDBBtn>
    </Link>

    <Link href={phoneNumberUk}>
    <MDBBtn size='lg' rounded>
        Call
    </MDBBtn>
    </Link>

</div>
</div>

        </>
    )
}

export default Contacts