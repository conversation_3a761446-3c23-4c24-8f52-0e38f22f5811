.textItem{
    width:31%;
    padding:16px;
    border-radius:16px;
background:white;
box-shadow:1px 1px 10px 0px rgb(245, 241, 241);
}
.textItem img{
    width:100px;
    height:100px;
}

.NativeAppDevelopment{
    margin:0 auto;
    border-radius: 10px;
    padding:16px;
}
.NativeAppDevelopment .bigItem {
    padding:10px 20px;
    flex-wrap: nowrap !important;
}
.NativeAppDevelopment .bigItem img{
    width:360px;
    height:360px;
    min-width:49%;
    border-radius:16px;
box-shadow:1px 1px 10px 0px rgb(245, 241, 241);

}


.NativeAppDevelopment .bigItem.flip img {
  
    order: -1; 
}

.weOffer{
    max-width:1800px;
    margin:0 auto;
}
.weOffer .offerItem{
width:32%;
min-height:200px;
border-radius:10px;
box-shadow:1px 1px 10px 0px rgb(238, 235, 235);
padding:17px;
}
@media (max-width:1800px) {
.weOffer .offerItem p{
font-size: medium !important;
}
}
.offerItem{
background:white;
margin:0 auto;
}

.weOffer .offerItem:hover{
box-shadow:1px 1px 10px 0px rgb(230, 225, 225);
}
.weOffer .offerItem img{
   
    animation-duration:1s;
    width:80px;
    height:80px;
    display:block;
    margin:0 auto;
    border-radius:10px;
}
.weOffer .offerItem img:hover{
    animation-name:offerItemAnim;
}
@keyframes offerItemAnim{
    0%{
        width:70px;
        height:70px;
    }
    50%{
        width:80px;
        height:80px;
    }
    100%{
        width:70px;
        height:70px;
    }
}
.industryItem h5{
position: absolute;
font-weight:bolder;
text-align:center;
color:#333333;
}
.industryItem{
    width:160px;
    height:250px;
    background-size:100% 100%;
    border-radius:10px !important;
    padding:16px;
    
}

.contacts{
    width:100%;
    padding:10px 50px;
    margin:0 auto;
    border-radius:6px;
    background:var(--blueText);
}
.list{
    box-shadow:1px 1px 10px 0px rgb(241, 242, 243);
    border-radius:20px;
    padding:16px;
    width:48% !important;
}
.list *{
    cursor: pointer;
}
.portfolioItem{
    width:100%;
}
.portfolioItem img{
max-width:40%;
}

@media (max-width:700px) {
.portfolioItem img{
   max-width:100% !important; 
}

}

.smallItems{
    width:33%;
    min-height:20px;
    border-radius:10px;
    box-shadow:1px 1px 10px 0px rgb(234, 235, 238);
    text-align:center;
    padding:16px;
    display:flex;
    align-items:center;
    justify-content:center;
}

@media screen and (max-width:800px){
    

.smallItems{
    width:48%;
}

}

.CList{
    width:100%;
    padding:10px;
    color:#555555;
    padding:16px;
    cursor:pointer;
}
.CList:hover{
    color:rgb(89, 0, 255);
}

.surItem{
    border-radius:20px;
    box-shadow:1px 1px 10px 0px lightgrey;
    background:rgb(243, 245, 242);
    margin-top:20px !important;

    max-height:max-content !important;
    padding:20px;
}

.newBoxOfColor{
width:47%;
background: white;
border-radius: 3px;
min-height: 100px;
box-shadow: 1px 1px 10px 0px lightgray;
padding:20px;
}

.newBoxOfColor .iconContainer{
    min-height: 46px;
    border-radius: 30px;
background-color: white;
}
.newBoxOfColor *{
    color:var(--blueText);
}
.newBoxOfColor:hover{
    background:var(--blueText);
} 
.newBoxOfColor:hover > *{
    color:rgb(248, 244, 244);
}

.dashedBox{
    width:48%;
    border-radius: 5px ;
    border:4px dashed var(--blueText);
    margin:0 auto;
    padding: 25px;

}

@media (max-width:600px){
    
.dashedBox{
    width:98%;
}
.dashedBox:nth-child(2){
    transform:translate(-0px,20px);
}
}

.techCard{
    width:130px;
    border:1px solid rgb(226, 238, 226);
    border-radius:10px;
    padding:20px 16px;
    border-radius:10px;
    margin:0 auto;
    margin:10px;
}
.techCard:hover{
box-shadow:1px 1px 10px 0px rgb(188, 202, 188);
}

.botBox{
    width:47%;
    min-height: 100px;
    border-radius:20px;
    box-shadow:1px 1px 30px 0px lightgrey;
    position:relative;
    padding:26px;
    margin-top:10px;
}
@media (max-width:700px){
.botBox{

    margin-top:20px !important;
position: statics;
}

}
.botBox .botBoxIcon{
    border-radius:50%;
    padding:10px;
    right:0;
    margin-top:-30px;
    position:absolute;
    width:100px;
    height:100px;
    background:white;
}