import React from 'react'
import TopNav from "../fixed/TopNav";
import SideContact from "../fixed/sideContact";
import Testimonials from "../LandingPage/testimonials";
import ConnectWithUs from "../LandingPage/connectWithUs";
import "../LandingPage/style.css";

import { useState } from "react";
import Header from "./header";
import "../Portfolio/style.css"
import "./style.css";
import Contacts from "./contact";
import CertificationApproach from './certificationApproach';
import Items from './items';

export default function NftMarketPlace(){
const [width,setWidth]=useState(typeof window=='object' ? window.innerWidth :0);

return (<>
<TopNav/>
<SideContact/>

{/* page start */}

<div style={{width:'100%',minWidth:"100vw",background:"white",minHeight:"100vh",height:"auto"}}>
    
<br/><br/>
<br/><br/>

<Header image={"https://blockchaintechs.io/wp-content/uploads/2021/09/<EMAIL>"}
title='NFT Marketplace Development Services'
 text='Build your own NFT marketplace with advanced features and seamless user experience'
 textColor='black' />

 <div style={{padding:10}}>

<br/>
<div style={{maxWidth:1524,margin:"0 auto",width:"90%"}}> 
<Items title={``} p={``} items={[]}/>
<br/><br/>
<CertificationApproach/>
<br/><br/>
<Contacts/>
<br/><br/>

</div>


</div>



</div>


<br/>

<br/><br/>
<Testimonials/>
<br/><br/>
<ConnectWithUs/>
<br/><br/>
</>)
}


