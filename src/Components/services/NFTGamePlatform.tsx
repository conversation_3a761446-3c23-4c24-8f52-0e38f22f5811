import React from 'react'
import TopNav from "../fixed/TopNav";
import SideContact from "../fixed/sideContact";
import Testimonials from "../LandingPage/testimonials";
import ConnectWithUs from "../LandingPage/connectWithUs";
import "../LandingPage/style.css";

import { useState } from "react";
import Header from "./header";
import "../Portfolio/style.css"
import "./style.css";
import Contacts from "./contact";
import CertificationApproach from './certificationApproach';
import Items, { techImages } from './items';
import WhiteBold from '@/utils/WhiteBold';
import ResponsiveImage from '@/utils/ResposiveImage';
import { FaAngleDoubleRight } from 'react-icons/fa';
import TechCarousel from '../LandingPage/TechCarousal';

export default function NFTGamePlatform(){
const [width,setWidth]=useState(typeof window=='object' ? window.innerWidth :0);

return (<>
<TopNav/>
<SideContact/>

{/* page start */}

<div style={{width:'100%',minWidth:"100vw",background:"white",minHeight:"100vh",height:"auto"}}>
    
<br/><br/>
<br/><br/>

<Header image={"https://blockchaintechs.io/wp-content/uploads/2022/06/NFT-Defi-platform-Copy-2.webp"} 
title='NFT Gaming Platform Like Axie Infinity'
 text='We are developing an NFT Gaming Platform like Axie Infinity, we can help you develop them across more scalable blockchains like Binance Smart Chain, Polygon, or Tron also.' />
<br/><br/>


<div style={{width:"1000px",maxWidth:"95%",margin:"0 auto",padding:16}}>

<WhiteBold>NFT Gaming Platform
</WhiteBold>
<br/><p>In the future, Blockchain Technology Development has the potential to disrupt a wide range of industries. Gaming is an excellent example of how blockchain and cryptocurrencies have the potential to upend conventional thinking and put the game out of reach.

</p>
<br/>
<p>
Axie Infinity, an online video game, has become the most popular blockchain-based game, supporting how players earn money in the game. What do you need to know about Axie Infinity, one of the most popular play-to-earn games? We’ve put all of the questions on how to develop an NFT game platform like Axie Infinity here.
</p>
</div>
<br/><br/>

<div className='d-flex justify-content-center'>
    <WhiteBold>How can you build a game like Axie infinity quickly?</WhiteBold>
</div>

<br/><br/>

<div className='d-flex align-items-center justify-content-between m-block' style={{gap:15}}>
 
<div style={{width:"40%"}} className='d-flex justify-content-center'>
<ResponsiveImage containerSize={300} src='https://blockchaintechs.io/wp-content/uploads/2022/06/How-can-you-build-a-game-like-Axie-infinity-quickly_.webp'/>
</div>


<div style={{width:'60%',padding:25}} className=''>
    {
        [

"It should be mentioned that Axie Infinity is essentially a play-to-earn game based on Ethereum. However, it created a side-chain called Ronin to address Ethereum’s gas and scalability difficulties. To avoid gas fee concerns later on when utilizing the Axie Infinity Clone Script, ensure an appropriate side-chain is selected.",
"Axie Infinity is a blockchain-based game. The game’s gameplay is centered on a battle-based pet breeding game. Before they can begin playing the game, players must purchase three characters. They also receive a significant portion of the Axie Infinity platform’s revenue as long as they continue to play the game.",
"An Axie Infinity Clone Script is a white-labeled piece of code that can be used to quickly create a game that looks and plays like Axie Infinity. The code is adaptable, and it cuts the game’s development time in half, from seven to ten days. With a few little adjustments and customizations, one may create a market-competitive game."
        ].map((e:any,i:number)=>(
            <>
<div className='card' key={i} style={{padding:16,width:"90%",margin:"0 auto",marginTop:10,background:"lightgreen"}}>
{e}
</div><br/>
</>
        ))}
</div>

   
</div>
<br/><br/>
<br/><br/>

<div className='d-flex justify-content-center'>
<WhiteBold>The advantages of developing an NFT marketplace like Axie Infinity!</WhiteBold>
</div>
<br/><br/>
<p className='text-center'>
Some of the benefits of NFT game platform development, such as Axie Infinity, are listed below:
</p>
<br/><br/>


<div className='d-flex align-items-center justify-content-between m-block padding_Pc' style={{gap:15}}>




<div style={{width:'60%',padding:25}} className=''>


<div>
    
{[
  "Increasing the value of a digital asset.",
  "Fully 3D environments, planets, and mini games can all be used to exhibit gaming aspects.",
  "Platforms that leverage blockchain technology for NFT gaming are more adaptable and versatile.",
  "In the NFT gaming platform, use NFTs to create rare, digital replicas of treasures.",
  "NFT art may be presented, and identities can be customized with a range of accessories, thanks to blockchain technology.",
  "Improved data security.",
  "The gaming sector is growing increasingly self-sufficient and competitive.",
  "Fully 3D environments, planets, and minigames can all be used to exhibit gaming aspects."
].map((e:string,i:number)=>(<div key={i} style={{gap:10}} className='d-flex align-items-center'>
<FaAngleDoubleRight/>
<p>{e}</p>
    </div>
))}

</div>



</div>

<div style={{width:"40%"}} className='d-flex justify-content-center'>
<ResponsiveImage containerSize={300} src='https://blockchaintechs.io/wp-content/uploads/2022/06/Our-Axie-Infinity-Clone-Scripts-Built-In-Functionalities.webp'/>
</div>


</div>
<br/><br/>
<div className='padding_Pc'>
<Items title={`Our Axie Infinity Clone Script's Built-In Functionalities
`} p={``} items={[
{
    image:"",
    name:"This NFT gaming platform's main goal is to allow players to introduce their own axie into battle. During the combat, each axis will face off against live or environment-based opponents.",
    title:"Battle    "
},{
    image:"",
    name:"Battles take place on lands, which are tokenizable and can be purchased as part of a player's in-game collectibles. Interestingly, once the land has been purchased, players can enhance it using the materials they encounter while playing.",
    title:"Land"
},{
    image:"",
    name:"AXS stands for governance tokens on Axie Infinity, and holders of these tokens can voice their ideas on how to improve the platform. We will generate governance tokens for your Axie Infinity marketplace clone program based on your preferences.",
    title:"Tokens"
},{
    image:"",
    name:"Players can breed their own axes in order to produce children. Any axie may only be bred seven times, and each breeding costs a certain number of AXS and SLP tokens.",
    title:"Breeding"
},{
    image:"",
    name:"The marketplace is where you can buy and sell anime. Other in-game collectibles are also available for purchase. In addition, we may personalize the in-game items or resources so that they are available to your gamers exactly how you want them to be.",
    title:"Marketplace"
}
]} />
</div>
<br/><br/>
<div className='padding_Pc'>
<Contacts/>
</div>
<br/><br/>
<div className='d-flex justify-content-center'>
    
<WhiteBold>How do you create an NFT gaming marketplace like Axie Infinity?
</WhiteBold>

</div>

<br/><br/>
<p className='text-center' style={{padding:16}}>Two important elements must be considered while creating an NFT game platform:
</p>


<div className='d-flex padding_Pc m-block' style={{padding:16,gap:10}}>

<div style={{padding:20,boxShadow:'-10px 10px 0px 3px black',width:"47%",borderRadius:20}}>

<h2 style={{fontWeight:"bolder",color:"dimgrey"}}>1</h2>
<br/>
The marketplace is where you can buy and sell anime. Other in-game collectibles are also available for purchase. In addition, we may personalize the in-game items or resources so that they are available to your gamers exactly how you want them to be.

</div>
<br/>

<div style={{padding:20,boxShadow:'-10px 10px 0px 3px black',marginTop:10,marginLeft:10,width:"47%",borderRadius:20}}>

<h2 style={{fontWeight:"bolder",color:"dimgrey"}}>2</h2>
<br/>
Select the elements that are most important to the NFT gaming platform.


</div>



</div>
<br/><br/>

<br/><br/>
<div className='padding_Pc'>These two factors are by far the most important when it comes to creating the best custom NFT gaming platform possible. But why is it the case? Let’s be crystal clear about this.</div>
<br/><br/>
<div style={{background:"#F2F2F2"}} className='padding_Pc'>

    <div className='d-flex justify-content-center'>
<WhiteBold>What are the features of the Axie Infinity Clone script?</WhiteBold>
</div>
<br/><br/>
<div className=' d-flex m-block ' style={{gap:16}}>

<div style={{background:"white",borderRadius:15,width:"48%",padding:16}}>
<b>Admin Features</b>
<br/><br/>

{
    
    [
        "CMS and CRM systems that are secure",
        "Player Management with a Customizable Dashboard",
        "Management of the referral program",
        "Real-time data handling is now possible."
      
].map((e:string,i:number)=>(
    <div key={i} style={{gap:10}} className='d-flex align-items-center'>
    <FaAngleDoubleRight/>
    <p>{e}</p>
        </div> 
))
}
</div>
<br/>
<div style={{background:"white",borderRadius:15,width:"48%",padding:16}}>
<b>Users Features</b>
<br/><br/>
{
    
    [
        "Playing a realistic game is a realistic experience.",
        "Integration of a wallet",
        "User security is paramount.",
        "Distribution of rewards",
        "Complete and total transparency"
      ].map((e:string,i:number)=>(
        <div key={i} style={{gap:10}} className='d-flex align-items-center'>
        <FaAngleDoubleRight/>
        <p>{e}</p>
            </div> 
    ))
}
</div>


</div>




</div>

<br/><br/>

<div className='d-flex justify-content-center'><WhiteBold>Our Technology Development Stack</WhiteBold></div>
<br/><br/>

<TechCarousel techImages={techImages
}/>

 <div style={{padding:10}}>

<br/>
<div style={{maxWidth:1524,margin:"0 auto",width:"90%"}}> 
<br/><br/>

</div>


</div>



</div>


<br/>

<br/><br/>
<Testimonials/>
<br/><br/>
<ConnectWithUs/>
<br/><br/>
</>)
}


