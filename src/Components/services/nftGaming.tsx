import React from 'react'
import TopNav from "../fixed/TopNav";
import SideContact from "../fixed/sideContact";
import Testimonials from "../LandingPage/testimonials";
import ConnectWithUs from "../LandingPage/connectWithUs";
import "../LandingPage/style.css";

import { useState } from "react";
import Header from "./header";
import "../Portfolio/style.css"
import "./style.css";
import Contacts from "./contact";
import CertificationApproach from './certificationApproach';
import Items, { techImages } from './items';
import WhiteBold from '@/utils/WhiteBold';
import ResponsiveImage from '@/utils/ResposiveImage';
import Bold from '@/utils/pBolder';
import { MDBBtn } from 'mdb-react-ui-kit';
import TechCarousel from '../LandingPage/TechCarousal';

export default function NftGaming() {
    const [width, setWidth] = useState(typeof window == 'object' ? window.innerWidth : 0);

    return (<>
        <TopNav />
        <SideContact />

        {/* page start */}

        <div style={{ width: '100%', minWidth: "100vw", background: "white", minHeight: "100vh", height: "auto" }}>

            <br /><br />
            <br /><br />

            <Header image={"https://blockchaintechs.io/wp-content/uploads/2021/09/<EMAIL>"}
                title='One-stop Play To Earn NFT Games services for making gamers earn a large number of money while having fun with NFTs!'
                text=''
                textColor='black' />
            <div className='padding_Pc'>
                <br /><br />
                <Items title={`Play-to-Earn NFT Gaming Development Services`} p={`Play-to-earn games have a strong connection with the gaming world. The gaming industry is storming towards a height that no one in the digital world could imagine, with a net worth of over hundreds of billions of dollars and a couple of billions of users.

The NFT Game Hub is an environment where we carve and fine-tune every feature of the gaming platform while integrating the digital world’s futuristic vision, the Fungible and Non-Fungible tokens. The Play To Earn NFT game has finally begun its revolution.`} items={[]} />
                <br /><br />
                <Items title={`Future of the Gaming Industry
`} p={`On all possible platforms, the wide gaming industry created its own communities. Most importantly, PC-based gaming and game storylines have a stronger emotional resonance with audiences. According to recent statistics, the United States alone has spent more than ten billion dollars on gaming. The possibilities are unlimited when it comes to mobile games and console games.

The NFT Game Hub will be the place where legacy-based games will be transformed into NFT gaming platforms, ushering in a new era of new beginnings. Prepare for a rollercoaster ride with our Play To Earn gaming options.`}
                    items={[]} />
                <br /><br />
                <Items title={`Play To Earn NFT Gaming Ecosystem`} items={[]}
                    p={`NFT Game Hub is a one-of-a-kind NFT-based game ecosystem that offers everything you need to have a great NFT gaming experience. Do you have any ideas for a blockchain-based gaming platform. Don’t worry, we’ve got you covered! Do you want to make a meaningful difference in the game industry. Please include us! We prefer to create the best and better gaming ecosystem in the digital age.`} />
                <br /><br />
                <br /><br />

                <div className='padding_Pc'>

                    <div className='d-flex justify-content-center'>
                        <WhiteBold>The Play-to-earn NFT Gaming platform we offers
                        </WhiteBold>
                    </div><br/><br/>
<div className='d-flex' style={{margin:"0 auto",flexFlow:"row wrap"}}>
                    {[{
                        name: 'Adventure Game',
                        image: '/images/adventerGame.svg'
                    },
                    {
                        name: 'Action Game',
                        image: '/images/actionGame.svg'
                    },
                    {
                        name: 'Simulation Game',
                        image: '/images/sumulateGame.svg'
                    },
                    {
                        name: 'Sports Game',
                        image: '/images/sportGame.svg'
                    },
                    {
                        name: 'Casino Game',
                        image: '/images/cinoGame.svg'
                    },
                    {
                        name: 'PvP Battle Games',
                        image: '/images/pvpGame.svg'
                    }

                    ].map((e: any, i: number) => {

                        return (<div key={i} className='text-center' style={{margin:"0 auto"}}>
                            <div style={{ margin: "0 auto", width: 'max-content' }}><ResponsiveImage 
                            containerSize={50} src={e.image} /></div>
                            <h5 style={{ padding: 20 }}><b>{e.name}</b></h5>
                        </div>)

                    })}
                </div>
                </div>
                <br /><br />
                <br /><br />
            </div>

            <div style={{ padding: 10 }}>

                <br />
                <div style={{ maxWidth: 1524, margin: "0 auto", width: "90%" }}>
                    <br /><br />
                    <Contacts />
                    <br /><br />


                    <div className='d-flex justify-content-center'>

<WhiteBold>Needs in NFT Gaming Marketplace</WhiteBold>
</div>
<br/><br/>
<p className='text-center'>The NFT gaming industry, more than any other NFT sector, requires a little more efficiency and labor involvement. In the crypto era, when graphics and assets to enhance the graphical platform play a critical role, NFT game development will have a huge impact.</p>
<br/><br/>

<div className='d-flex m-block' style={{gap:20}}>
<div className='dashedBox'>


<div className='d-flex justify-content-center'><ResponsiveImage src='/images/asset2.svg' containerSize={40}/>
</div>
<br/>
<h5 className='text-center'><b>Assets</b></h5>
<br/>
<span>
    {`The assets for the platform will be designed by well-trained specialists with extensive experience depending on the theme's needs. Asset rendering will be more exact and realistic in comparison to real-world graphics.
`}</span></div>

<div className='dashedBox'> 
<div className='d-flex justify-content-center'>
    <ResponsiveImage src='/images/asset3.svg' containerSize={40}/>
    </div>

<br/>
<h5 className='text-center'><b>Platform</b></h5>
<br/>
<span>{`Platform design is crucial in game development, as it allows you to make the most of your assets by creating a platform where 3D or any other type of item interacts with one another to perform an activity. For a better experience, we created a well-designed and ideal high-textured graphics play-to-earn NFT Gaming marketplace.
`}</span></div>

</div>

                </div>
<br/><br/>
<div className='d-flex padding-16n justify-content-between' style={{background:"var(--blueText)",padding:25,borderRadius:10}}>
<Bold>Check Our Fractional NFT Marketplace Also!</Bold>
<MDBBtn>
    Click here
</MDBBtn>
</div>

<br/><br/><br/>
<TechCarousel techImages={techImages}/>

<br/><br/>
            </div>



        </div>


        <br />

        <br /><br />
        <Testimonials />
        <br /><br />
        <ConnectWithUs />
        <br /><br />
    </>)
}


