import React from 'react'
import TopNav from "../fixed/TopNav";
import SideContact from "../fixed/sideContact";
import Testimonials from "../LandingPage/testimonials";
import ConnectWithUs from "../LandingPage/connectWithUs";
import "../LandingPage/style.css";

import { useState } from "react";
import Header from "./header";
import "../Portfolio/style.css"
import "./style.css";
import Contacts from "./contact";
import CertificationApproach from './certificationApproach';
import Items, { techImages } from './items';
import WhiteBold from '@/utils/WhiteBold';
import ResponsiveImage from '@/utils/ResposiveImage';
import { CheckCircle } from 'react-feather';
import Bold from '@/utils/pBolder';
import { MDBBtn } from 'mdb-react-ui-kit';
import TechCarousel from '../LandingPage/TechCarousal';

export default function SolalanNftDevelopment() {
    const [width, setWidth] = useState(typeof window == 'object' ? window.innerWidth : 0);

    return (<>
        <TopNav />
        <SideContact />

        {/* page start */}

        <div style={{ width: '100%', minWidth: "100vw", background: "white", minHeight: "100vh", height: "auto" }}>

            <br /><br />
            <br /><br />

            <Header image={"https://blockchaintechs.io/wp-content/uploads/2022/08/Solana-NFT-Marketplace-Development-Company.webp"}
                title='Solana NFT Marketplace Development Company'
                text='Looking to develop your own NFT Marketplace on Solana? We have got the best tools to digitalize your NFT market with our skilled blockchain developers and boost the effectiveness of your company.' />
            <div className='padding_Pc'>



                <div className='d-flex align-items-center m-block'>
                    <div style={{ width: "60%" }}>
                        <WhiteBold>Why Choose Solana for the NFT Marketplace?</WhiteBold>

                        {`The NFT has experienced a dramatic rise in popularity, which has boosted the number of crypto transactions. Unfortunately, Ethereum, which was not designed for the numbers of transactions, was used to mint the majority of NFT.For the two reasons, The first is scalability and increased armament, which caused significant speed losses.  The second issue is more significant. The cost of a commission per transaction has increased.

At the time Solana entered the stage. It addresses a number of issues at once, starting with its 3000 times greater speed and scalability than Ethereum. Of course, the cost is also low from each transaction. Mass minting can be done rapidly, cheaply, and in large quantities. High Security and privacy are compromised in the process.`}
                    </div>
                    <ResponsiveImage containerSize={width > 700 ? 400 : width - 60} src={'https://blockchaintechs.io/wp-content/uploads/2022/08/Why-Choose-Us-Solana.webp'}
                    />
                </div>


                <br /><br />
                <div className='d-flex justify-content-center'>
                    <WhiteBold>Solana NFT Marketplace Development Company</WhiteBold>
                </div>

                <div className='d-flex align-items-center m-block'>
                    <ResponsiveImage containerSize={width > 700 ? 400 : width - 60} src='https://blockchaintechs.io/wp-content/uploads/2022/08/Solana-NFT-Marketplace-Development.webp' />


                    <div style={{ width: '65%', padding: 16 }} >
                        <p>
                            We are the the top blockchain development company for Solana, we provides quick decentralized applications. Our developers build top-notch software that guarantees security for both small businesses and large corporations. Since NFT sales have been excellent everywhere in the world, Solana has recently accelerated significantly in the cryptocurrency space.
                        </p><br />
                        <p>
                            Our team has experience developing NFT marketplaces on Solana using a hybrid proof-of-history and proof-of-stake strategy. Our main goal is to build platforms that are high-performance, inexpensive, and have a low commission rate.
                        </p>
                    </div>

                </div>

                <br /><br />
                <div className='d-flex justify-content-center'>
                    <WhiteBold>Our Solana NFT Development Services on Various Platforms</WhiteBold>
                </div>

                {[
                    {
                        title: "Solana NFT Marketplace Development",
                        name: "The demand for the Solana NFT Marketplace platform is rapidly increasing alongside the growth of NFTs in the cryptocurrency industry. With remarkable token standards, delicate Smart-contract Auditing services, and full maintenance and support, Blockchaitechs, a pioneer in Solana NFT Marketplace Website Development, assists users in launching the Solana NFT Marketplace platform.                        ",
                        image: "https://blockchaintechs.io/wp-content/uploads/2022/08/Solana-NFT-Marketplace-App-Development.webp"
                    }, {
                        title: "Solana NFT Smart Contract Development",
                        name: "A decentralized network Blockchain called Solana is dedicated to offering excellent user experiences and user-friendly services. NFT Marketplace platforms built on the Solana Blockchain run on pre-written Smart Contracts that have undergone extensive deep testing to ensure they are secure. Additionally, the customizable support team is there to offer any necessary assistance should a bug develop.                        ",
                        image: "https://blockchaintechs.io/wp-content/uploads/2022/08/Solana-NFT-Smart-Contract-Development.webp"
                    }, {
                        title: "Solana NFT Minting Platform Development",
                        name: "The NFT Minting platform allows customers to mint their digital assets as tokens without having to write smart contracts, which creates a significant additional revenue source. Although the NFT Minting platform is constructed on a number of different blockchains, Solana has seen tremendous growth because the average cost to mint NFTs. We are knowledgeable about Solana NFT Minting Development, the market is more visible.                        ",
                        image: "https://blockchaintechs.io/wp-content/uploads/2022/08/Solana-NFT-Minting-Platform-Development.webp"
                    }, {
                        title: "Solana NFT Marketplace App Development",
                        name: "A mobile application is easier to access at a certain area compared to a web application than it is from any other location, and many industries are constantly using apps as their main form of advertising. It has been observed that all of these factors have contributed to a notable increase in mobile device usage for the newly developed NFT Marketplace in Solana. Applications from the Solana NFT Marketplace have excellent features that meet the needs of both users and enterprises.",
                        image: "https://blockchaintechs.io/wp-content/uploads/2022/08/Solana-NFT-Marketplace-App-Development.webp"
                    }, {
                        title: "Solana NFT Exchange Development",
                        name: "The NFT Exchange and Swap, which enables users to exchange their digital assets for cryptocurrencies and tokens and acts as a trading platform between makers and takers, is a requirement for any successful NFT Marketplace Development plan. On NFT Marketplace Exchanges, merchants typically pay a fee to withdraw funds to their accounts, and a commission fee is assessed during transactions, which serves as the platform owners' source of income.",
                        image: "https://blockchaintechs.io/wp-content/uploads/2022/08/Solana-NFT-Exchange-Development.webp"
                    }
                ].map((e: any, i: number) => {

                    return (<div key={i} className='d-flex align-items-center m-block' style={{marginTop:15,flexFlow:i%2===1 ? 'row-reverse wrap':'row wrap', border: '2px solid lightgrey', borderRadius: "10px" }}>
                        <ResponsiveImage containerSize={width > 700 ? 400 : width - 60} src={e.image} />


                        <div style={{ width: '65%', padding: 16 }} >
                            <b className='text-center'>{e.title}</b>
                            <p>
                              {e.name}
                                 </p>
                        </div>

                    </div>)

                })}

            </div>


<div className='padding_Pc'>

<div className='d-flex align-items-center m-block'>
                    <div style={{ width: "60%" }}>
                        <WhiteBold>How to develop the NFT Marketplace on Solana Blockchain?
</WhiteBold>

                        {`Many individuals are now eager to establish an NFT Marketplace platform on Solana since the rates of transactions over Solana are far higher than those on the Ethereum-based network, which are 3000+ times faster. Because solana has higher transaction capacity. Solana was built by the Rust software and it is popular among developers. It  is more user-friendly and capable of creating a wider range of applications, including games and blockchains. This makes Solana a more approachable platform than Ethereum for releasing apps, which has caused the Solana ecosystem to evolve rapidly.

It is simple to create Solana NFT Marketplaces, and certain NFT Marketplace Development Projects are already based on Solana. It takes a long time to construct an NFT Marketplace project from scratch, but you can employ NFT Marketplace Developers or, even better, an NFT Marketplace Development Company.
`}  </div>
                    <ResponsiveImage containerSize={width > 700 ? 400 : width - 60} src={'https://blockchaintechs.io/wp-content/uploads/2022/08/Why-Choose-Solana-for-the-NFT-Marketplace_.webp'}
                    />
                </div>

<br/><br/>
<WhiteBold>Benefits of Solana NFT Marketplace</WhiteBold>


<div className='d-flex align-items-center m-block justify-content-around' style={{flexFlow:"row-reverse wrap"}}>
                    <div style={{ width: "60%" }}>
                    {[
    "With the help of the scalable Solana blockchain network, you may handle numerous transactions at once",
    "\nTransaction processing speed is significantly faster than that of Ethereum, the most widely used NFT Blockchain",
    "\nA minimal commission because of the idea of PoH",
    "\nUsers are able to stake their governance tokens on the Solana NFT Marketplace",
    "\nUsers can stake by transferring their tokens that grant benefits to token holders for an extended period of time",
    "\nNative token SOL is offered in fractional units known as lamports",
    " This enables investors to stake tokens in the liquidity pool and earn significant prizes and profits",
    ""
].map((e:any,i:number)=>{
                       return (<div key={i} className='d-flex align-items-center' style={{gap:10,padding:16}}>
                        <CheckCircle color='black'/>
                        <span>{e}</span>
                       </div>)
                       })

                    
}
                       </div>
                    <ResponsiveImage containerSize={width > 700 ? 400 : width - 60} src={'https://blockchaintechs.io/wp-content/uploads/2022/07/benefits-wages-salary-advantage-income_53876-125113-1.webp'}
                    />
                </div>
                </div>

            <div style={{ padding: 10 }}>

                <br />
                <div style={{ maxWidth: 1524, margin: "0 auto", width: "90%" }}>
                    <br /><br />
                    <Contacts />
                    <br /><br />

<TechCarousel techImages={techImages}/>
<br/><br/>
                    <div className='d-flex padding-16n justify-content-between' style={{background:"var(--blueText)",padding:25,borderRadius:10}}>
<Bold>Check Our Fractional NFT Marketplace Also!</Bold>
<MDBBtn>
    Click here
</MDBBtn>
</div>

                </div>


            </div>



        </div>


        <br />

        <br /><br />
        <Testimonials />
        <br /><br />
        <ConnectWithUs />
        <br /><br />
    </>)
}


