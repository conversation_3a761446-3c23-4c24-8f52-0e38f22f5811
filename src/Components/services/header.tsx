import PText from '@/utils/PText2';
import WhiteBold from '@/utils/WhiteBold';
import { MDBBtn } from 'mdb-react-ui-kit';
import Image from 'next/image';
import React from 'react'
interface Props{
    title:string,
    text:string,
    image:string,
    button?:any,
    textColor?:string
}
const Header:React.FC<Props>=({title,text,image,button,textColor})=>{
    return (
    <>
    <div className='headerContainer d-flex m-block align-items-center' style={{maxWidth:"1400px",margin:"0 auto"}} >
    <div className='header' style={{width:"60%"}}>
<div style={{color: textColor || 'white'}}>
    <b style={{marginLeft:10,fontSize:35,display:"block",fontWeight:'bold'}}>
{title}
</b>
    <b style={{fontWeight:"bolder",fontSize:'16px',display:'block',marginTop:'10px',marginLeft:10}}>{text}
</b>
</div>
<br/>
{button && {button}}

    </div>

<div style={{width:"40%",minHeight:300}} className='d-flex align-items-center justify-content-center'>
{image ? (
    <Image
        style={{width:"80%",height:"80%",animationName:"fadeInAndOut",animationDuration:"3s",animationIterationCount:'infinite'}}
        src={image}
        width={500}
        height={500}
        alt={title}
        unoptimized={true}
    />
) : (
    <div style={{
        width:"80%",
        height:"80%",
        background:"linear-gradient(135deg, #00d46c, #00a855)",
        borderRadius:"15px",
        display:"flex",
        alignItems:"center",
        justifyContent:"center",
        color:"white",
        fontSize:"24px",
        fontWeight:"bold"
    }}>
        {title.split(' ').slice(0,2).join(' ')}
    </div>
)}
</div>

    </div>
    </>
    )
}

export default Header;