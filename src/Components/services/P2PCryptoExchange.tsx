import React from 'react'
import TopNav from "../fixed/TopNav";
import SideContact from "../fixed/sideContact";
import Testimonials from "../LandingPage/testimonials";
import ConnectWithUs from "../LandingPage/connectWithUs";
import "../LandingPage/style.css";

import { useState } from "react";
import Header from "./header";
import "../Portfolio/style.css"
import "./style.css";
import Contacts from "./contact";
import CertificationApproach from './certificationApproach';
import Items from './items';
import WhiteBold from '@/utils/WhiteBold';
import ResponsiveImage from '@/utils/ResposiveImage';
import Bold from '@/utils/pBolder';
import { CheckBox } from '@mui/icons-material';
import WhyChooseUsItem from './whyChooseItem';

export default function P2PCryptoExchange() {
    const [width, setWidth] = useState(typeof window == 'object' ? window.innerWidth : 0);
    const whyChooseUsItems = [
        {
            name: "How does a P2P exchange work?",
            image: "",
            content: "A P2P exchange works on the fundamental concept of blockchain, i.e. decentralization. It allows buyers and sellers to trade directly, without any third party involvement."
        }, {
            name: "Why are businesses keen on building a P2P crypto exchange?",
            image: "",
            content: "Given that a P2P exchange eliminates third-party involvement, users enjoy better privacy and security. This is the reason why the majority of people prefer trading on a P2P exchange. More number of users opting for P2P trading means more revenue generation opportunities for platform owners."
        }, {
            name: "How much does it cost to build a P2P crypto exchange?",
            image: "",
            content: "The development cost depends on the features that you want to integrate into your exchange. However, the development cost and time required to build a white label exchange are comparatively less than that required to develop a custom exchange from scratch."
        }, {
            name: "What is the future of P2P trading?            ",
            image: "",
            content: "P2P exchange platforms are more reliable as they eliminate third-party dependency. While centralized exchanges have to adhere to stringent regulations and have high operational overheads and transaction fees, P2P crypto exchanges take up a lot of slack in the system and forge the path for next wave of adoption in this space.            "
        }
    ]
    return (<>
        <TopNav />
        <SideContact />

        {/* page start */}

        <div style={{ width: '100%', minWidth: "100vw", background: "white", minHeight: "100vh", height: "auto" }}>

            <br /><br />
            <br /><br />

            <Header image={"/images/exchange.svg"}
                title='P2P Crypto Exchange Development Company'
                text='Tap into peer-to-peer trading network. Embrace more secure and private transactions..'
                textColor='black' />
            <br />
            <div className='padding_Pc'>
                <div className='d-flex justify-content-center'><WhiteBold>P2P Cryptocurrency Exchange –
                    A peer-to-peer approach to trading</WhiteBold></div>

                <p className='text-center'>Utilizing the fundamental concept of blockchain technology, which is decentralization, a P2P (peer-to-peer) exchange executes transactions without any involvement of a central authority while connecting the buyers and sellers directly. The platform is powered by a smart contract-based Escrow system to foster reliable transactions.</p>

                <br /><br />

                <div className='d-flex align-items-center justify-content-between m-block' style={{ gap: 15, flexFlow: "row-reverse nowrap" }}>


                    <div style={{ width: '60%' }} >
                        <h3><b>Assimilate The Power of P2P Trading</b></h3>
                        <br />
                        <p>
                            Tap into the burgeoning peer-to-peer trading network with our mission-driven approach to develop P2P exchange software.

                            Our highly cohesive teams align development processes with your goals to deliver an ecosystem of trust. We combine our deep domain expertise and technology-agnostic approach to build a coherent roadmap that navigates the development of your P2P exchange platform and accelerates time-to-market.
                        </p>
                    </div>


                    <div style={{ width: "40%" }} className='d-flex justify-content-center'>
                        <ResponsiveImage containerSize={300} src='/images/exchange.svg' />
                    </div>


                </div>
                <br /><br />
                <Items title={`P2P Cryptocurrency Exchange Features
`} items={[
                        {
                            title: "Powerful Matching Engine        ",
                            name: "The crypto exchange is fortified with a high-speed engine that efficiently matches buyers' and sellers' orders with minimum latency. It has built-in order types for buy order and sell orders, which serve as one of the cornerstones of a trading system.",
                            image: "/images/chip.svg"
                        }, {
                            title: "Multi-layer Security        ",
                            name: "Our P2P exchange is integrated with top-notch security features like SSL implementation and two-factor authentication, such as e-mail authentication and Google authentication, which add an extra layer of security to the platform.",
                            image: "/images/verification.svg"
                        }, {
                            title: "Instant KYC and AML Verification",
                            name: "An intuitive identity verification system accelerates the KYC process while following the right protocol. This builds a base of lawful and authenticated users (Buyers and Sellers).",
                            image: "/images/certificate.svg"
                        }, {
                            title: "Escrow System        ",
                            name: "A secure and reliable smart contract-based escrow system fuels trading between the platform users.",
                            image: "/images/smartDevices.svg"
                        }, {
                            title: "Atomic Swap        ",
                            name: "A peer-to-peer swap indicates trusted users transactions, eliminating the need for a central authority. Atomic swaps revolve around the fact that the transactions either execute completely or terminate, reducing the likelihood of disputes in a scenario of breach of an agreement.",
                            image: "/images/croseChain.svg"
                        }, {
                            title: "Dispute Management        ",
                            name: "A robust dispute management system enables seamless dispute redressal. Complete details about the users’ transaction history and bank statements help the Admin acumen in dispute management and solidify their decisions.",
                            image: "/images/users.svg"
                        }, {
                            title: "Preferred trader selection        ",
                            name: "Buyers have the flexibility to choose their preferred sellers, and vice versa, for a more confident and trusted trading experience.",
                            image: "/images/trend.svg"
                        }, {
                            title: "Admin Panel",
                            name: "A secure admin panel is underpinned by smooth controls for efficient management of critical elements like Escrow system and dispute management.        ",
                            image: "/images/api.svg"
                        },
                        {
                            title: "Multi-language Support        ",
                            name: "Multi-language support provides global exposure to your P2P exchange. The platform’s UI provides the best user experience for left to right and right to left languages.        ",
                            image: "data:image/png;base64,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"
                        }
                    ]} p={``} />


            </div>
            <br /><br />

            <div style={{ background: "#08ABEA" }}>
                <div className='d-flex align-items-center justify-content-between m-block' style={{ gap: 15, flexFlow: "row-reverse nowrap", padding: 16 }}>


                    <div style={{ width: '60%' }} className='m-block'>
                        <Bold>How P2P Cryptocurrency Exchange works</Bold>


                        {[
                            "Users (Buyers and Sellers) register on a P2P exchange platform",
                            "Users undergo KYC verification process",
                            "Buyer places a buy order",
                            "A wallet address is created for the Buyer",
                            "The matching engine matches the Buyer with the right Seller",
                            "The buyer and seller confirm and agree on the terms of the trade",
                            "Smart contract-based Escrow holds Seller’s crypto assets to be sold",
                            "Buyer makes the payment for crypto assets to be purchased",
                            "The seller confirms the payment",
                            "Escrow releases crypto assets, transferring the same to Buyer’s wallet address",
                            "Buyer can transfer the assets to cold wallet"
                        ].map((e: any, i: number) => (<div key={e} className='d-flex align-items-center' style={{ gap: 15 }}>
                            <CheckBox style={{ color: 'white' }} />
                            <p style={{ color: "white" }}>{e}</p>
                        </div>))}


                    </div>


                    <div style={{ width: "40%" }} className='d-flex justify-content-center'>
                        <ResponsiveImage containerSize={300} src='/images/digital.svg' />
                    </div>


                </div>
            </div>
            <br /><br />
            <br /><br />
            <div className='padding_Pc'>
                <div className='d-flex justify-content-center'><WhiteBold>Benefits of our P2P Exchange Software
                </WhiteBold></div>
<br/><br/>
                <div className='d-flex' style={{flexFlow:"row wrap",gap:10}}>

                    {[
                        {
                            name: "Institutional-grade security",
                            image: "/images/verification.svg"
                        },
                        {
                            name: 'Lightening speed transactions',
                            image: "/images/chip.svg"
                        },
                        {
                            name: "Self-explanatory interface",
                            image: "/images/apps.svg"
                        },
                        {
                            name: "Scalability",
                            image: "/images/trend.svg"
                        },
                        {
                            name: "Quick time-to-market",
                            image: "/images/storeFont.svg"
                        },
                        {
                            name: "Built by blockchain experts",
                            image: "/images/users.svg"
                        }
                    ].map((e, i) => (<div key={i} className='d-flex align-items-center' style={{margin:"0 auto", gap: 10, maxWidth: "31%" }}>
                        <ResponsiveImage containerSize={50} src={e.image} />
                       <p className='text-center'> {e.name}</p>
                    </div>))}



                </div>
            </div>

            <br /><br />
            <div className='d-flex padding_Pc align-items-center justify-content-between m-block' style={{ gap: 15, flexFlow: "row-reverse nowrap", padding: 16 }}>


                <div style={{ width: "40%" }} className='d-flex justify-content-center'>
                    <ResponsiveImage containerSize={300} src='/images/img.svg' />
                </div>


                <div style={{ width: '60%' }} className=''>
                    <Bold>Frequently Asked Questions
                    </Bold>


                    {whyChooseUsItems.map((e, i: number) => <WhyChooseUsItem key={i} {...e} />)}


                </div>




            </div>


            <br /><br />
        



        </div>


        <br />

        <br /><br />
        <Testimonials />
        <br /><br />
        <ConnectWithUs />
        <br /><br />
    </>)
}


