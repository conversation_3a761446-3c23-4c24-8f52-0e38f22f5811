import Image from "next/image";
import React from "react";

export default function Component() {
  return (
    <>
      <div
        className="elementor-testimonial"
        style={{ boxSizing: "border-box", textAlign: "center", cursor: "grab" }}
      >
        <div
          className="elementor-testimonial__content"
          style={{ boxSizing: "border-box" }}
        >
          <div
            className="elementor-testimonial__text"
            style={{
              boxSizing: "border-box",
              fontSize: "1.3em",
              fontStyle: "italic",
              lineHeight: 1.5,
            }}
          >
            <div
              className="box_outer_container"
              style={{
                boxSizing: "border-box",
                padding: "8px",
                borderRadius: "16px",
                background: "rgb(7, 22, 26)",
                display: "flex",
                alignItems: "center",
                boxShadow: "rgb(0, 212, 108) 1px 0.5px 10px 1px",
              }}
            >
              <Image
                height={94}
                width={103}
                alt="NextEvent"
                src="/images/Next-Event-1.webp"
                style={{
                  userSelect: "none",
                  verticalAlign: "top",
                  boxSizing: "border-box",
                  border: "none",
                  borderRadius: "0px",
                  height: "auto",
                  maxWidth: "100%",
                  boxShadow: "none",
                  width: "45%",
                  outline: "none",
                  pointerEvents: "none",
                }}
              />
              <div
                className="inner_container_logo"
                style={{
                  boxSizing: "border-box",
                  display: "flex",
                  marginLeft: "5px",
                  flexDirection: "column",
                  alignItems: "flex-start",
                }}
              >
                <p
                  style={{
                    margin: "0px 0px 18px",
                    padding: "0px",
                    lineHeight: 1.5,
                    boxSizing: "border-box",
                    color: "rgb(0, 212, 108)",
                    fontSize: "18px",
                    fontStyle: "normal",
                    textAlign: "left",
                    fontWeight: 500,
                    marginBottom: "0px",
                    marginLeft: "0px",
                  }}
                >
                  Next Event
                </p>
                <span
                  style={{
                    boxSizing: "border-box",
                    fontSize: "12px",
                    color: "rgb(235, 235, 235)",
                    textAlign: "left",
                    fontStyle: "normal",
                    fontWeight: 300,
                  }}
                >
                  Sports Event App
                </span>
              </div>
            </div>{" "}
          </div>
        </div>
        <div
          className="elementor-testimonial__footer"
          style={{
            boxSizing: "border-box",
            display: "flex",
            WebkitBoxAlign: "center",
            alignItems: "center",
            WebkitBoxPack: "center",
            justifyContent: "center",
            marginTop: "0px",
          }}
        />
      </div>
      <style
        dangerouslySetInnerHTML={{
          __html: `
html {
  overflow: hidden scroll;
  position: relative;
  margin: 0px;
  padding: 0px;
}

body {
  position: relative;
  transition: right 0.3s ease 0s;
  right: 0px;
  display: flex;
  flex-direction: column;
  width: 100%;
  min-height: 100vh;
  margin: 0px;
  padding: 0px;
  font-family: Rubik;
  background: rgb(255, 255, 255);
  font-size: 18px;
  line-height: 27px;
  font-weight: 400;
  color: rgb(105, 102, 135);
  overflow-x: hidden;
  background-color: rgb(255, 255, 255);
}
`,
        }}
      />
    </>
  );
}
