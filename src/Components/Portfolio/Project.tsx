import Image from 'next/image';
import React from 'react'
import CompanyTag from './companyTag';
interface Props{
    image1:string,
    image2:string,
    technologies:string[],
    textContent:any,
    companyName:string,
    companyColor:string,
    companyImage:string
    background?:string,
    flip?:boolean
}
const Project:React.FC<Props>=({
    image1,
    image2,
    technologies,
    textContent,
    companyName,
    companyImage,
    companyColor,
    background,
    flip
})=>{
    return (
        <>
        <div className='projectContainer'>
<div className='topImage'></div>
<div className='projectCtn'>
<CompanyTag name={companyName} color={companyColor} image={companyImage} 
/>

<div className='contentCtn d-flex m-block' style={{flexFlow:flip ? "row-reverse":undefined}}>

<div className='textContainer'>
{textContent}
</div>

<div className='imageContainer'>
<div className='colored' style={{background}}>
<br/>
<div className='images'>

<div className='image1'>
<Image   alt='' fill objectFit='contain' src={image1}/>
</div>
<div className='image2'>
<Image   alt='' fill objectFit='contain'  src={image2}/> 
</div>

</div>
<b style={{color:"var(--yellow)",fontWeight:"bolder"}}>
Technology Stack
</b>
<div className='d-flex ' style={{flexFlow:'row wrap'}}>
{technologies.map((img:string,i)=><Image key={i}  src={img} width={40} alt='' height={40} style={{marginLeft:5,borderRadius:"5px",boxShadow:'1px 1px 10px 0px lightgrey',background:"white"}}/>)}

</div>

<div>

</div>



</div>
</div>

</div>
    </div>


        </div>
        </>
    )
}
export default Project;