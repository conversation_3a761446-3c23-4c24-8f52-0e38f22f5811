import WhiteBold from '@/utils/WhiteBold'
import React from 'react'
import { Layers, Star } from 'react-feather'

interface Props{
    children:any,
    style?:any
}
const H3:React.FC<Props>=({children,style})=>{
    return (
        <div style={{
            textAlign: 'center',
            margin: '60px 0 40px 0',
            position: 'relative'
        }}>
            {/* Decorative background */}
            <div style={{
                position: 'absolute',
                top: '50%',
                left: '50%',
                transform: 'translate(-50%, -50%)',
                width: '200px',
                height: '200px',
                background: 'radial-gradient(circle, rgba(0,212,108,0.1) 0%, transparent 70%)',
                borderRadius: '50%',
                zIndex: 0
            }}></div>

            <div style={{
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                gap: '15px',
                marginBottom: '15px',
                position: 'relative',
                zIndex: 1
            }}>
                <Layers color='#00d46c' size={32} />
                <WhiteBold>
                    <div style={{
                        fontSize: 42,
                        fontWeight: 'bold',
                        background: 'linear-gradient(135deg, #00d46c, #00a855)',
                        WebkitBackgroundClip: 'text',
                        WebkitTextFillColor: 'transparent',
                        backgroundClip: 'text',
                        textAlign: "center",
                        display: "block",
                        ...style
                    }}>
                        {children}
                    </div>
                </WhiteBold>
                <Star color='#FFD700' size={32} />
            </div>

            {/* Subtitle */}
            <div style={{
                color: 'rgba(85,85,85,0.8)',
                fontSize: '18px',
                fontWeight: '500',
                maxWidth: '600px',
                margin: '0 auto',
                lineHeight: '1.6',
                position: 'relative',
                zIndex: 1
            }}>
                Showcasing our expertise in cutting-edge blockchain technology and innovative solutions
            </div>

            {/* Decorative line */}
            <div style={{
                width: '100px',
                height: '3px',
                background: 'linear-gradient(135deg, #00d46c, #00a855)',
                margin: '20px auto',
                borderRadius: '2px',
                position: 'relative',
                zIndex: 1
            }}></div>
        </div>
    )
}

export default H3;