import { MDBBtn } from "mdb-react-ui-kit";
import TopNav from "../fixed/TopNav";
import SideContact from "../fixed/sideContact";
import TakeYourBusiness from "../LandingPage/takeYourBusiness";
import WhyChooseUs from "../LandingPage/whyChooseUs";
import Testimonials from "../LandingPage/testimonials";
import Blogs from "../LandingPage/Blogs";
import ConnectWithUs from "../LandingPage/connectWithUs";
import { Fragment, useState, useEffect } from "react";
import "./style.css";
import "../LandingPage/style.css";
import Header from "./header";
import H3 from "./h3";
import EnhancedProject from "./EnhancedProject";
import PortfolioFilter from "./PortfolioFilter";
import useClientWidth from "@/hooks/useClientWidth";

import WhiteBold from "@/utils/WhiteBold";
import PText from "@/utils/PText2";
import { Divider, ListItem, ListItemText, makeStyles } from "@mui/material";
import { Check, List } from "react-feather";
import { ProjectsList } from "./EnhancedProjectsList";

export default function Index(){
const { width, isClient } = useClientWidth();
const [activeFilter, setActiveFilter] = useState('All');
const [filteredProjects, setFilteredProjects] = useState(ProjectsList);

// Get unique categories from projects
const categories = Array.from(new Set(ProjectsList.map(project => project.category).filter(Boolean)));

// Filter projects based on active filter
useEffect(() => {
    if (activeFilter === 'All') {
        setFilteredProjects(ProjectsList);
    } else {
        setFilteredProjects(ProjectsList.filter(project => project.category === activeFilter));
    }
}, [activeFilter]);


return (<>
<TopNav/>
<SideContact/>

{/* page start */}

<div style={{width:'100%',minWidth:"97vw",background:"linear-gradient(135deg, #020b12 0%, #0a1a2a 100%)",minHeight:"100vh",height:"auto",margin:"0 auto",overflowX:"hidden",overflowY:"auto"}}>
    
<br/><br/>
<br/><br/>
<br/><br/>
<Header/>

<H3>
Our Blockchain Projects
</H3>

{/* Portfolio Filter */}
<PortfolioFilter
    categories={categories}
    activeFilter={activeFilter}
    onFilterChange={setActiveFilter}
/>

{/* Projects Count */}
<div style={{
    textAlign: 'center',
    margin: '20px 0',
    color: '#ffffff',
    fontSize: '16px',
    fontWeight: '500'
}}>
    Showing {filteredProjects.length} of {ProjectsList.length} projects
    {activeFilter !== 'All' && (
        <span style={{ color: '#00d46c', fontWeight: 'bold' }}> in {activeFilter}</span>
    )}
</div>

{/* Projects Grid */}
<div style={{
    display: 'grid',
    gap: '40px',
    marginTop: '40px'
}}>
    {filteredProjects.map((project, i) => (
        <div
            key={i}
            style={{
                opacity: 0,
                animation: `fadeInUp 0.6s ease forwards`,
                animationDelay: `${i * 0.1}s`
            }}
        >
            <EnhancedProject flip={i % 2 === 1} {...project} />
        </div>
    ))}
</div>

{/* No projects message */}
{filteredProjects.length === 0 && (
    <div style={{
        textAlign: 'center',
        padding: '60px 20px',
        color: 'rgba(255,255,255,0.7)',
        fontSize: '18px'
    }}>
        <div style={{ fontSize: '48px', marginBottom: '20px' }}>🔍</div>
        No projects found in the &quot;{activeFilter}&quot; category.
        <br />
        <button
            onClick={() => setActiveFilter('All')}
            style={{
                background: 'linear-gradient(135deg, #00d46c, #00a855)',
                color: '#000',
                border: 'none',
                padding: '10px 20px',
                borderRadius: '20px',
                fontSize: '14px',
                fontWeight: 'bold',
                cursor: 'pointer',
                marginTop: '20px'
            }}
        >
            View All Projects
        </button>
    </div>
)}

{/* Add CSS animation */}
<style jsx>{`
    @keyframes fadeInUp {
        from {
            opacity: 0;
            transform: translateY(30px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }
`}</style>
<br/>
<br/>
<br/>

</div>


<br/>
<div className='Indexbody'>
    {/* <TakeYourBusiness/> */}
<br/><br/>
{/* <IndustriesWeTransformed/> */}
    {/* <WhyChooseUs/> */}
<br/><br/>
        <Testimonials/>
<br/><br/>
    {/* <Blogs/> */}
<br/><br/>
<ConnectWithUs/>
<br/><br/>
</div>

</>)
}

