import React, { useState } from 'react';
import { Filter, Grid, List } from 'react-feather';

interface FilterProps {
    categories: string[];
    activeFilter: string;
    onFilterChange: (filter: string) => void;
}

const PortfolioFilter: React.FC<FilterProps> = ({ categories, activeFilter, onFilterChange }) => {
    const [viewMode, setViewMode] = useState<'grid' | 'list'>('list');

    return (
        <div style={{
            display: 'flex',
            justifyContent: 'space-between',
            alignItems: 'center',
            margin: '40px 0',
            padding: '20px',
            background: 'rgba(255,255,255,0.05)',
            borderRadius: '15px',
            backdropFilter: 'blur(10px)',
            border: '1px solid rgba(255,255,255,0.1)',
            flexWrap: 'wrap',
            gap: '20px'
        }}>
            {/* Filter Buttons */}
            <div style={{
                display: 'flex',
                gap: '10px',
                flexWrap: 'wrap',
                alignItems: 'center'
            }}>
                <div style={{
                    display: 'flex',
                    alignItems: 'center',
                    gap: '8px',
                    color: '#00d46c',
                    fontWeight: 'bold',
                    fontSize: '16px'
                }}>
                    <Filter size={20} />
                    Filter:
                </div>
                
                {['All', ...categories].map((category) => (
                    <button
                        key={category}
                        onClick={() => onFilterChange(category)}
                        style={{
                            background: activeFilter === category 
                                ? 'linear-gradient(135deg, #00d46c, #00a855)' 
                                : 'rgba(255,255,255,0.1)',
                            color: activeFilter === category ? '#000' : '#fff',
                            border: 'none',
                            padding: '8px 16px',
                            borderRadius: '20px',
                            fontSize: '14px',
                            fontWeight: '600',
                            cursor: 'pointer',
                            transition: 'all 0.2s ease',
                            backdropFilter: 'blur(10px)'
                        }}
                        onMouseEnter={(e) => {
                            if (activeFilter !== category) {
                                e.currentTarget.style.background = 'rgba(0,212,108,0.2)';
                            }
                        }}
                        onMouseLeave={(e) => {
                            if (activeFilter !== category) {
                                e.currentTarget.style.background = 'rgba(255,255,255,0.1)';
                            }
                        }}
                    >
                        {category}
                    </button>
                ))}
            </div>

            {/* View Mode Toggle */}
            <div style={{
                display: 'flex',
                gap: '5px',
                background: 'rgba(255,255,255,0.1)',
                borderRadius: '10px',
                padding: '5px'
            }}>
                <button
                    onClick={() => setViewMode('list')}
                    style={{
                        background: viewMode === 'list' ? '#00d46c' : 'transparent',
                        color: viewMode === 'list' ? '#000' : '#fff',
                        border: 'none',
                        padding: '8px 12px',
                        borderRadius: '8px',
                        cursor: 'pointer',
                        transition: 'all 0.2s ease',
                        display: 'flex',
                        alignItems: 'center',
                        gap: '5px'
                    }}
                >
                    <List size={16} />
                    List
                </button>
                <button
                    onClick={() => setViewMode('grid')}
                    style={{
                        background: viewMode === 'grid' ? '#00d46c' : 'transparent',
                        color: viewMode === 'grid' ? '#000' : '#fff',
                        border: 'none',
                        padding: '8px 12px',
                        borderRadius: '8px',
                        cursor: 'pointer',
                        transition: 'all 0.2s ease',
                        display: 'flex',
                        alignItems: 'center',
                        gap: '5px'
                    }}
                >
                    <Grid size={16} />
                    Grid
                </button>
            </div>
        </div>
    );
};

export default PortfolioFilter;
