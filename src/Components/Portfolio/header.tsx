import PText from '@/utils/PText2';
import WhiteBold from '@/utils/WhiteBold';
import { MDBBtn } from 'mdb-react-ui-kit';
import Image from 'next/image';
import React from 'react';
import { Award, Users, Globe, TrendingUp, Star, Zap } from 'react-feather';
import { useRouter } from 'next/router';

const Header:React.FC=()=>{
    const router = useRouter();

    return (
    <>
    <div className='headerContainer d-flex m-block' style={{
        background: 'linear-gradient(135deg, rgba(0,212,108,0.05) 0%, rgba(0,168,85,0.05) 100%)',
        borderRadius: '20px',
        padding: '40px 20px',
        margin: '20px 0',
        backdropFilter: 'blur(10px)',
        border: '1px solid rgba(0,212,108,0.2)'
    }}>
    <div className='header' style={{width:"60%"}}>
<WhiteBold>
    <div style={{
        fontSize: 42,
        display: "block",
        marginLeft: 10,
        background: 'linear-gradient(135deg, #00d46c, #00a855)',
        WebkitBackgroundClip: 'text',
        WebkitTextFillColor: 'transparent',
        backgroundClip: 'text',
        fontWeight: 'bold',
        lineHeight: '1.2',
        marginBottom: '20px'
    }}>
        Built With Passion,<br/>
        Delivered by Professionals
    </div>
</WhiteBold>

{/* Stats Section */}
<div style={{
    display: 'flex',
    gap: '30px',
    marginBottom: '25px',
    flexWrap: 'wrap'
}}>
    <div style={{display: 'flex', alignItems: 'center', gap: '8px'}}>
        <Award color='#00d46c' size={20} />
        <span style={{color: '#ffffff', fontWeight: 'bold', fontSize: '16px'}}>100+ Projects</span>
    </div>
    <div style={{display: 'flex', alignItems: 'center', gap: '8px'}}>
        <Users color='#00d46c' size={20} />
        <span style={{color: '#ffffff', fontWeight: 'bold', fontSize: '16px'}}>50+ Clients</span>
    </div>
    <div style={{display: 'flex', alignItems: 'center', gap: '8px'}}>
        <Globe color='#00d46c' size={20} />
        <span style={{color: '#ffffff', fontWeight: 'bold', fontSize: '16px'}}>25+ Countries</span>
    </div>
</div>

<PText>
<div style={{
    fontWeight: "600",
    fontSize: '18px',
    lineHeight: '1.6',
    color: 'rgba(255,255,255,0.9)',
    marginBottom: '20px'
}}>
For the past five years, we have empowered <strong style={{color: '#00d46c'}}>Global Fortune 500 companies</strong>, innovative startups, and technology pioneers in developing cutting-edge blockchain solutions. Our expertise spans across <strong style={{color: '#00d46c'}}>DeFi, NFTs, Metaverse, and Enterprise Blockchain</strong> with a quality-driven approach using the latest technologies.
</div>
</PText>

{/* Key Highlights */}
<div style={{
    display: 'flex',
    gap: '20px',
    marginBottom: '30px',
    flexWrap: 'wrap'
}}>
    {[
        {icon: <Star color='#FFD700' size={16} />, text: 'Award-winning solutions'},
        {icon: <TrendingUp color='#00d46c' size={16} />, text: '$50M+ in funding raised'},
        {icon: <Zap color='#00d46c' size={16} />, text: '99.9% uptime guarantee'}
    ].map((item, index) => (
        <div key={index} style={{
            display: 'flex',
            alignItems: 'center',
            gap: '8px',
            background: 'rgba(255,255,255,0.1)',
            padding: '8px 15px',
            borderRadius: '20px',
            backdropFilter: 'blur(10px)'
        }}>
            {item.icon}
            <span style={{color: '#ffffff', fontSize: '14px', fontWeight: '500'}}>{item.text}</span>
        </div>
    ))}
</div>

<div style={{display: 'flex', gap: '15px', flexWrap: 'wrap'}}>
    <MDBBtn
        size='lg'
        onClick={() => router.push('/contactUs')}
        style={{
            background: 'linear-gradient(135deg, #00d46c, #00a855)',
            border: 'none',
            borderRadius: '25px',
            padding: '12px 30px',
            fontWeight: 'bold',
            fontSize: '16px',
            boxShadow: '0 10px 30px rgba(0,212,108,0.3)',
            transition: 'transform 0.2s ease',
            cursor: 'pointer'
        }}
        onMouseEnter={(e) => e.currentTarget.style.transform = 'translateY(-2px)'}
        onMouseLeave={(e) => e.currentTarget.style.transform = 'translateY(0)'}
    >
        GET FREE ESTIMATE
    </MDBBtn>
    <MDBBtn
        size='lg'
        outline
        onClick={() => {
            // Scroll to the projects section
            const projectsSection = document.querySelector('.projectContainer');
            if (projectsSection) {
                projectsSection.scrollIntoView({ behavior: 'smooth' });
            }
        }}
        style={{
            border: '2px solid #00d46c',
            color: '#00d46c',
            borderRadius: '25px',
            padding: '12px 30px',
            fontWeight: 'bold',
            fontSize: '16px',
            background: 'transparent',
            transition: 'all 0.2s ease',
            cursor: 'pointer'
        }}
        onMouseEnter={(e) => {
            e.currentTarget.style.background = '#00d46c';
            e.currentTarget.style.color = '#000';
        }}
        onMouseLeave={(e) => {
            e.currentTarget.style.background = 'transparent';
            e.currentTarget.style.color = '#00d46c';
        }}
    >
        VIEW CASE STUDIES
    </MDBBtn>
</div>
    </div>
<div style={{width:"40%",minHeight:300}} className='d-flex align-items-center justify-content-center'>
<Image style={{width:"80%",height:"80%",animationName:"fadeInAndOut",animationDuration:"3s",animationIterationCount:'infinite'}} src='https://blockchaintechs.io/wp-content/uploads/2022/08/Robot.webp'  width={500} height={500} alt='' />
</div>

    </div>
    </>
    )
}

export default Header;