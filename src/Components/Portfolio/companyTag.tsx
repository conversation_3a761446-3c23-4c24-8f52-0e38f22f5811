import { MDBBtn } from 'mdb-react-ui-kit';
import Image from 'next/image';
import React from 'react'
interface Props{
    name:string,
    image:string,
    color?:string
}
const CompanyTag:React.FC<Props>=({name,image,color})=>{
return (<>
<div style={{position:"absolute"}}>
<div className='d-flex align-items-center' style={{position:"relative"}}>
<MDBBtn rounded noRipple  color='dark' 
style={{position:"absolute",zIndex:1,background:color,marginLeft:45,paddingLeft:30}}>
    {name}
     </MDBBtn>

<Image alt=''
 src={image} 
 width={70} height={70} style={{borderRadius:"50",zIndex:2,position:"absolute"}}/>
 
</div>
</div>
</>)
}

export default CompanyTag;