import PText from "@/utils/PText2";
import { Check, Star, TrendingUp, Shield, Zap, Globe, DollarSign, Users, Layers, Database, Settings, BarChart, Lock } from "react-feather";

export const ProjectsList=[
    // BT Swap - Decentralized Exchange (DEX)
    {
    image1:'https://blockchaintechs.io/wp-content/uploads/2022/08/BT-Swap-Yield-Farming.webp',
    image2:'https://blockchaintechs.io/wp-content/uploads/2022/08/BT-Swap-Yield-Farming.webp',
    background:'linear-gradient(135deg, #1a1a2e 0%, #16213e 100%)',
    category: 'DeFi & DEX',
    projectType: 'Decentralized Exchange',
    duration: '10 months',
    teamSize: '15 developers',
    technologies:[
        'https://blockchaintechs.io/wp-content/uploads/2022/08/10-1-Solidity.webp',
        'https://blockchaintechs.io/wp-content/uploads/2022/08/3-Express-JS.webp',
        'https://blockchaintechs.io/wp-content/uploads/2022/08/logoReactNative.webp',
        'https://blockchaintechs.io/wp-content/uploads/2022/08/27.webp',
        'https://blockchaintechs.io/wp-content/uploads/2022/08/5-AWS.webp',
    ],
    textContent:<>
    <br/><br/>
    <div style={{display: 'flex', alignItems: 'center', gap: '10px', marginBottom: '10px'}}>
      <Layers color='#00d46c' size={24} />
      <h2 style={{fontSize: '28px', fontWeight: 'bold', color: '#ffffff', margin: 0}}>BT Swap - Multichain DEX</h2>
    </div>
    <div style={{display: 'flex', gap: '15px', marginBottom: '15px', flexWrap: 'wrap'}}>
      <span style={{background: '#00d46c', color: '#000', padding: '4px 12px', borderRadius: '20px', fontSize: '12px', fontWeight: 'bold'}}>
        Decentralized Exchange
      </span>
      <span style={{background: 'rgba(0,212,108,0.2)', color: '#00d46c', padding: '4px 12px', borderRadius: '20px', fontSize: '12px', fontWeight: 'bold'}}>
        10 months
      </span>
      <span style={{background: 'rgba(0,212,108,0.2)', color: '#00d46c', padding: '4px 12px', borderRadius: '20px', fontSize: '12px', fontWeight: 'bold'}}>
        15 developers
      </span>
    </div>
    <PText style={{fontSize: '16px', lineHeight: '1.6', marginBottom: '20px', color: '#ffffff'}}>
    BT Swap is a hybrid multichain decentralized exchange (DEX) based on Uniswap's constant-product automated market maker (AMM). Liquidity providers deposit token pairs and algorithms automatically make markets for trading pairs with interoperability across multiple blockchains.
    </PText>
    <div style={{marginBottom: '15px'}}>
      <h3 style={{fontSize: '18px', marginBottom: '10px', color: '#ffffff', margin: '0 0 10px 0'}}>🚀 Key Features:</h3>
    </div>
{[
  {icon: <Layers color='#00d46c' size={16} />, text: 'Interoperability with Ethereum, Binance Smart Chain and Polygon'},
  {icon: <TrendingUp color='#00d46c' size={16} />, text: 'P2P Cryptocurrency Exchange Platform'},
  {icon: <DollarSign color='#00d46c' size={16} />, text: 'Swap, Yield Farming, Staking and Liquidity Pools'},
  {icon: <Shield color='#00d46c' size={16} />, text: 'Built-in Crypto wallet with transaction history'},
  {icon: <BarChart color='#00d46c' size={16} />, text: 'Advanced trading analytics and portfolio tracking'}
].map((item, index) => (
  <div key={index} style={{padding:'8px 0', display: 'flex', alignItems: 'center', gap: '10px'}} className="d-flex align-items-center">
    {item.icon} <span style={{fontSize: '14px', color: '#ffffff'}}>{item.text}</span>
  </div>
))}
    </>,
    companyName:'BT Swap',
    companyColor:'rgb(16,59,102)',
    companyImage:'https://blockchaintechs.io/wp-content/uploads/2022/04/Ellipse-1-1.webp',
    liveUrl: 'https://btswap.io',
    caseStudyUrl: '/case-studies/bt-swap'
    },
    // FantomPAD - Crypto Startup Launchpad
    {
        image1:'https://blockchaintechs.io/wp-content/uploads/2022/08/Fantompad-Projects-Listing.webp',
        image2:'https://blockchaintechs.io/wp-content/uploads/2022/08/Fantompad-Projects-Listing.webp',
        background:"linear-gradient(135deg, #1a1a2e 0%, #16213e 100%)",
        category: 'ICO & Launchpad',
        projectType: 'Crypto Launchpad',
        duration: '8 months',
        teamSize: '12 developers',
        technologies:[
            'https://blockchaintechs.io/wp-content/uploads/2022/08/10-1-Solidity.webp',
            'https://blockchaintechs.io/wp-content/uploads/2022/08/3-Express-JS.webp',
            'https://blockchaintechs.io/wp-content/uploads/2022/08/logoReactNative.webp',
            'https://blockchaintechs.io/wp-content/uploads/2022/08/27.webp',
            'https://blockchaintechs.io/wp-content/uploads/2022/08/5-AWS.webp',
        ],
        textContent:<>
        <br/><br/>
        <div style={{display: 'flex', alignItems: 'center', gap: '10px', marginBottom: '10px'}}>
          <Star color='#00d46c' size={24} />
          <h2 style={{fontSize: '28px', fontWeight: 'bold', color: '#ffffff', margin: 0}}>FantomPAD - Crypto Launchpad</h2>
        </div>
        <div style={{display: 'flex', gap: '15px', marginBottom: '15px', flexWrap: 'wrap'}}>
          <span style={{background: '#00d46c', color: '#000', padding: '4px 12px', borderRadius: '20px', fontSize: '12px', fontWeight: 'bold'}}>
            ICO & Launchpad
          </span>
          <span style={{background: 'rgba(0,212,108,0.2)', color: '#00d46c', padding: '4px 12px', borderRadius: '20px', fontSize: '12px', fontWeight: 'bold'}}>
            8 months
          </span>
          <span style={{background: 'rgba(0,212,108,0.2)', color: '#00d46c', padding: '4px 12px', borderRadius: '20px', fontSize: '12px', fontWeight: 'bold'}}>
            12 developers
          </span>
        </div>
        <PText style={{fontSize: '16px', lineHeight: '1.6', marginBottom: '20px', color: '#ffffff'}}>
        FantomPAD is the primary launch and listing platform for the Fantom network. FantomPAD provides a new take on the conventional method of launching projects as well as revolutionizing the safety protocol of a DEX with advanced anti-snipe bot protection.
        </PText>
        <div style={{marginBottom: '15px'}}>
          <h3 style={{fontSize: '18px', marginBottom: '10px', color: '#ffffff', margin: '0 0 10px 0'}}>🚀 Key Features:</h3>
        </div>
    {[
  {icon: <Settings color='#00d46c' size={16} />, text: 'Admin dashboard to manage project listing'},
  {icon: <DollarSign color='#00d46c' size={16} />, text: 'Stake project token to invest in pools'},
  {icon: <Lock color='#00d46c' size={16} />, text: 'Inbuilt vesting platform for investors'},
  {icon: <Shield color='#00d46c' size={16} />, text: 'Anti-Snipe bot protection'},
  {icon: <Users color='#00d46c' size={16} />, text: 'Multisign wallet for fund management'}
].map((item, index) => (
      <div key={index} style={{padding:'8px 0', display: 'flex', alignItems: 'center', gap: '10px'}} className="d-flex align-items-center">
        {item.icon} <span style={{fontSize: '14px', color: '#ffffff'}}>{item.text}</span>
      </div>
    ))}
        </>,
        companyName:'FantomPAD',
        companyColor:'rgb(75,70,231)',
        companyImage:'https://blockchaintechs.io/wp-content/uploads/2022/08/FantomPAD.webp',
        liveUrl: 'https://fantompad.com',
        caseStudyUrl: '/case-studies/fantompad'
        },
    // DOXAZO - Crypto Staking & Swapping Platform
    {
        image1:'https://blockchaintechs.io/wp-content/uploads/2022/08/Doxaza-Dashboard-1024x681.webp',
        image2:'https://blockchaintechs.io/wp-content/uploads/2022/08/Doxaza-Dashboard-1024x681.webp',
        background:"linear-gradient(135deg, #1a1a2e 0%, #16213e 100%)",
        category: 'Staking & DeFi',
        projectType: 'DeFi Platform',
        duration: '7 months',
        teamSize: '10 developers',
        technologies:[
            'https://blockchaintechs.io/wp-content/uploads/2022/08/10-1-Solidity.webp',
            'https://blockchaintechs.io/wp-content/uploads/2022/08/3-Express-JS.webp',
            'https://blockchaintechs.io/wp-content/uploads/2022/08/logoReactNative.webp',
            'https://blockchaintechs.io/wp-content/uploads/2022/08/27.webp',
            'https://blockchaintechs.io/wp-content/uploads/2022/08/5-AWS.webp',
        ],
        textContent:<>
        <br/><br/>
        <div style={{display: 'flex', alignItems: 'center', gap: '10px', marginBottom: '10px'}}>
          <DollarSign color='#00d46c' size={24} />
          <h2 style={{fontSize: '28px', fontWeight: 'bold', color: '#ffffff', margin: 0}}>DOXAZO - Staking & Swapping</h2>
        </div>
        <div style={{display: 'flex', gap: '15px', marginBottom: '15px', flexWrap: 'wrap'}}>
          <span style={{background: '#00d46c', color: '#000', padding: '4px 12px', borderRadius: '20px', fontSize: '12px', fontWeight: 'bold'}}>
            Staking & DeFi
          </span>
          <span style={{background: 'rgba(0,212,108,0.2)', color: '#00d46c', padding: '4px 12px', borderRadius: '20px', fontSize: '12px', fontWeight: 'bold'}}>
            7 months
          </span>
          <span style={{background: 'rgba(0,212,108,0.2)', color: '#00d46c', padding: '4px 12px', borderRadius: '20px', fontSize: '12px', fontWeight: 'bold'}}>
            10 developers
          </span>
        </div>
        <PText style={{fontSize: '16px', lineHeight: '1.6', marginBottom: '20px', color: '#ffffff'}}>
        Earn rewards by staking your AZO to help secure the network. Choose your staking preference and start earning profit with just a few clicks! Features flexible time frames with competitive APY returns and integrated swapping functionality.
        </PText>
        <div style={{marginBottom: '15px'}}>
          <h3 style={{fontSize: '18px', marginBottom: '10px', color: '#ffffff', margin: '0 0 10px 0'}}>💰 Key Features:</h3>
        </div>
    {[
  {icon: <DollarSign color='#00d46c' size={16} />, text: 'Stake AZO with flexible time frame and get X% APY in return'},
  {icon: <BarChart color='#00d46c' size={16} />, text: 'See the total amount being staked for all users for project'},
  {icon: <TrendingUp color='#00d46c' size={16} />, text: 'Swap AZO against ETH straight from dAPP'},
  {icon: <Globe color='#00d46c' size={16} />, text: 'Live rate conversion using UniSwap API'},
  {icon: <Shield color='#00d46c' size={16} />, text: 'Built in Crypto wallet with transaction details'}
].map((item, index) => (
      <div key={index} style={{padding:'8px 0', display: 'flex', alignItems: 'center', gap: '10px'}} className="d-flex align-items-center">
        {item.icon} <span style={{fontSize: '14px', color: '#ffffff'}}>{item.text}</span>
      </div>
    ))}
        </>,
        companyName:'DOXAZO',
        companyColor:'#161926',
        companyImage:'https://blockchaintechs.io/wp-content/uploads/2022/08/DOXAZO.webp',
        liveUrl: 'https://doxazo.com',
        caseStudyUrl: '/case-studies/doxazo'
        },
    // Hive Investments - NFT Auction Platform
    {
        image1:'https://blockchaintechs.io/wp-content/uploads/2022/08/Hive-NFT-Bidding.webp',
        image2:'https://blockchaintechs.io/wp-content/uploads/2022/08/Hive-NFT-Bidding.webp',
        background:"linear-gradient(135deg, #1a1a2e 0%, #16213e 100%)",
        category: 'NFT & Marketplace',
        projectType: 'NFT Auction Platform',
        duration: '6 months',
        teamSize: '8 developers',
        technologies:[
            'https://blockchaintechs.io/wp-content/uploads/2022/08/10-1-Solidity.webp',
            'https://blockchaintechs.io/wp-content/uploads/2022/08/3-Express-JS.webp',
            'https://blockchaintechs.io/wp-content/uploads/2022/08/logoReactNative.webp',
            'https://blockchaintechs.io/wp-content/uploads/2022/08/27.webp',
            'https://blockchaintechs.io/wp-content/uploads/2022/08/5-AWS.webp',
        ],
        textContent:<>
        <br/><br/>
        <div style={{display: 'flex', alignItems: 'center', gap: '10px', marginBottom: '10px'}}>
          <Star color='#00d46c' size={24} />
          <h2 style={{fontSize: '28px', fontWeight: 'bold', color: '#ffffff', margin: 0}}>Hive Investments - NFT Auction</h2>
        </div>
        <div style={{display: 'flex', gap: '15px', marginBottom: '15px', flexWrap: 'wrap'}}>
          <span style={{background: '#00d46c', color: '#000', padding: '4px 12px', borderRadius: '20px', fontSize: '12px', fontWeight: 'bold'}}>
            NFT & Marketplace
          </span>
          <span style={{background: 'rgba(0,212,108,0.2)', color: '#00d46c', padding: '4px 12px', borderRadius: '20px', fontSize: '12px', fontWeight: 'bold'}}>
            6 months
          </span>
          <span style={{background: 'rgba(0,212,108,0.2)', color: '#00d46c', padding: '4px 12px', borderRadius: '20px', fontSize: '12px', fontWeight: 'bold'}}>
            8 developers
          </span>
        </div>
        <PText style={{fontSize: '16px', lineHeight: '1.6', marginBottom: '20px', color: '#ffffff'}}>
        NFT auction platform to raise capital. Top 20 premium NFTs are offered for auction for community to bid to invest on the project. Smart contract with timed auction logic for transparency and ChainLink VRF for randomness.
        </PText>
        <div style={{marginBottom: '15px'}}>
          <h3 style={{fontSize: '18px', marginBottom: '10px', color: '#ffffff', margin: '0 0 10px 0'}}>🎨 Key Features:</h3>
        </div>
    {[
  {icon: <Database color='#00d46c' size={16} />, text: 'Pre-minted NFTs from PINATA IPFS'},
  {icon: <DollarSign color='#00d46c' size={16} />, text: 'Invest in DAI and MATIC'},
  {icon: <Zap color='#00d46c' size={16} />, text: 'ChainLink VRF for Randomness'},
  {icon: <TrendingUp color='#00d46c' size={16} />, text: 'Different tiers of NFTs with APR%'},
  {icon: <BarChart color='#00d46c' size={16} />, text: 'Leader Board and bid history'}
].map((item, index) => (
      <div key={index} style={{padding:'8px 0', display: 'flex', alignItems: 'center', gap: '10px'}} className="d-flex align-items-center">
        {item.icon} <span style={{fontSize: '14px', color: '#ffffff'}}>{item.text}</span>
      </div>
    ))}
        </>,
        companyName:'Hive Investments',
        companyColor:'rgb(34, 139, 34)',
        companyImage:'https://blockchaintechs.io/wp-content/uploads/2022/08/Hive-Investments.webp',
        liveUrl: 'https://hiveinvestments.com',
        caseStudyUrl: '/case-studies/hive-investments'
        },
    // Lendspot - NFT Lending & Borrowing
    {
        image1:'https://blockchaintechs.io/wp-content/uploads/2022/08/Lendspot-NFT-Lending-1.webp',
        image2:'https://blockchaintechs.io/wp-content/uploads/2022/08/Lendspot-NFT-Lending-1.webp',
        background:"linear-gradient(135deg, #1a1a2e 0%, #16213e 100%)",
        category: 'DeFi & Lending',
        projectType: 'NFT Lending Platform',
        duration: '9 months',
        teamSize: '11 developers',
        technologies:[
            'https://blockchaintechs.io/wp-content/uploads/2022/08/10-1-Solidity.webp',
            'https://blockchaintechs.io/wp-content/uploads/2022/08/3-Express-JS.webp',
            'https://blockchaintechs.io/wp-content/uploads/2022/08/logoReactNative.webp',
            'https://blockchaintechs.io/wp-content/uploads/2022/08/27.webp',
            'https://blockchaintechs.io/wp-content/uploads/2022/08/5-AWS.webp',
        ],
        textContent:<>
        <br/><br/>
        <div style={{display: 'flex', alignItems: 'center', gap: '10px', marginBottom: '10px'}}>
          <DollarSign color='#00d46c' size={24} />
          <h2 style={{fontSize: '28px', fontWeight: 'bold', color: '#ffffff', margin: 0}}>Lendspot - NFT Lending</h2>
        </div>
        <div style={{display: 'flex', gap: '15px', marginBottom: '15px', flexWrap: 'wrap'}}>
          <span style={{background: '#00d46c', color: '#000', padding: '4px 12px', borderRadius: '20px', fontSize: '12px', fontWeight: 'bold'}}>
            DeFi & Lending
          </span>
          <span style={{background: 'rgba(0,212,108,0.2)', color: '#00d46c', padding: '4px 12px', borderRadius: '20px', fontSize: '12px', fontWeight: 'bold'}}>
            9 months
          </span>
          <span style={{background: 'rgba(0,212,108,0.2)', color: '#00d46c', padding: '4px 12px', borderRadius: '20px', fontSize: '12px', fontWeight: 'bold'}}>
            11 developers
          </span>
        </div>
        <PText style={{fontSize: '16px', lineHeight: '1.6', marginBottom: '20px', color: '#ffffff'}}>
        Get crypto loans against NFTs and lend crypto to generate additional income. NFTs are screened to ensure authenticity and smart contract with scrutiny logic to avoid duplication and frauds. Features dynamic interest rates based on rarity.
        </PText>
        <div style={{marginBottom: '15px'}}>
          <h3 style={{fontSize: '18px', marginBottom: '10px', color: '#ffffff', margin: '0 0 10px 0'}}>💎 Key Features:</h3>
        </div>
    {[
  {icon: <Layers color='#00d46c' size={16} />, text: 'Supports multichain NFTs'},
  {icon: <BarChart color='#00d46c' size={16} />, text: 'Sales history and rarity analysis'},
  {icon: <TrendingUp color='#00d46c' size={16} />, text: 'Dynamic interest rate based on rarity index'},
  {icon: <Globe color='#00d46c' size={16} />, text: 'Opensea API for sales history'},
  {icon: <Shield color='#00d46c' size={16} />, text: 'Rarity index Algorithm'}
].map((item, index) => (
      <div key={index} style={{padding:'8px 0', display: 'flex', alignItems: 'center', gap: '10px'}} className="d-flex align-items-center">
        {item.icon} <span style={{fontSize: '14px', color: '#ffffff'}}>{item.text}</span>
      </div>
    ))}
        </>,
        companyName:'Lendspot',
        companyColor:'rgb(138, 43, 226)',
        companyImage:'https://blockchaintechs.io/wp-content/uploads/2022/08/Lendspot.webp',
        liveUrl: 'https://lendspot.io',
        caseStudyUrl: '/case-studies/lendspot'
        },
    // MetaBloqs - Metaverse Ecosystem
    {
        image1:'https://blockchaintechs.io/wp-content/uploads/2022/12/Metaverse-Ecosystem-1-1.webp',
        image2:'https://blockchaintechs.io/wp-content/uploads/2022/12/Metaverse-Ecosystem-1-1.webp',
        background:"linear-gradient(135deg, #1a1a2e 0%, #16213e 100%)",
        category: 'Metaverse & Gaming',
        projectType: 'Metaverse Platform',
        duration: '12 months',
        teamSize: '18 developers',
        technologies:[
            'https://blockchaintechs.io/wp-content/uploads/2022/08/10-1-Solidity.webp',
            'https://blockchaintechs.io/wp-content/uploads/2022/08/3-Express-JS.webp',
            'https://blockchaintechs.io/wp-content/uploads/2022/08/logoReactNative.webp',
            'https://blockchaintechs.io/wp-content/uploads/2022/08/27.webp',
            'https://blockchaintechs.io/wp-content/uploads/2022/08/5-AWS.webp',
        ],
        textContent:<>
        <br/><br/>
        <div style={{display: 'flex', alignItems: 'center', gap: '10px', marginBottom: '10px'}}>
          <Globe color='#00d46c' size={24} />
          <h2 style={{fontSize: '28px', fontWeight: 'bold', color: '#ffffff', margin: 0}}>MetaBloqs - Metaverse Ecosystem</h2>
        </div>
        <div style={{display: 'flex', gap: '15px', marginBottom: '15px', flexWrap: 'wrap'}}>
          <span style={{background: '#00d46c', color: '#000', padding: '4px 12px', borderRadius: '20px', fontSize: '12px', fontWeight: 'bold'}}>
            Metaverse & Gaming
          </span>
          <span style={{background: 'rgba(0,212,108,0.2)', color: '#00d46c', padding: '4px 12px', borderRadius: '20px', fontSize: '12px', fontWeight: 'bold'}}>
            12 months
          </span>
          <span style={{background: 'rgba(0,212,108,0.2)', color: '#00d46c', padding: '4px 12px', borderRadius: '20px', fontSize: '12px', fontWeight: 'bold'}}>
            18 developers
          </span>
        </div>
        <PText style={{fontSize: '16px', lineHeight: '1.6', marginBottom: '20px', color: '#ffffff'}}>
        Real People, Real Place, Real Value with realistic graphics, Real-world Locations and true identities. Lifelike Web 3.0 Metaverse for Genuine Interactions. The ecosystem includes Meta cities, Meta Lands, Meta Events, Meta Games.
        </PText>
        <div style={{marginBottom: '15px'}}>
          <h3 style={{fontSize: '18px', marginBottom: '10px', color: '#ffffff', margin: '0 0 10px 0'}}>🌟 Key Features:</h3>
        </div>
    {[
  {icon: <Star color='#FFD700' size={16} />, text: 'Real world experience in 3D'},
  {icon: <Users color='#00d46c' size={16} />, text: 'ReadyPlayerMe SDK for Avatars'},
  {icon: <TrendingUp color='#00d46c' size={16} />, text: 'Inbuild 3D NFT Marketplace with web 3.0'},
  {icon: <Zap color='#00d46c' size={16} />, text: 'Live Events and Games'},
  {icon: <Shield color='#00d46c' size={16} />, text: 'Retail Shop and Business District'}
].map((item, index) => (
      <div key={index} style={{padding:'8px 0', display: 'flex', alignItems: 'center', gap: '10px'}} className="d-flex align-items-center">
        {item.icon} <span style={{fontSize: '14px', color: '#ffffff'}}>{item.text}</span>
      </div>
    ))}
        </>,
        companyName:'MetaBloqs',
        companyColor:'rgb(37, 211, 102)',
        companyImage:'https://blockchaintechs.io/wp-content/uploads/2022/11/BT-Swap.webp',
        liveUrl: 'https://metabloqs.com',
        caseStudyUrl: '/case-studies/metabloqs'
        },
    // CryptoMax - Centralized Exchange (CEX)
    {
        image1:'https://blockchaintechs.io/wp-content/uploads/2022/08/Metaplexar-Investment-Dashboard-Portfolio-Performance-300x237.webp',
        image2:'https://blockchaintechs.io/wp-content/uploads/2022/08/Metaplexar-Investment-Dashboard-Portfolio-Performance-300x237.webp',
        background:"linear-gradient(135deg, #1a1a2e 0%, #16213e 100%)",
        category: 'CEX & Trading',
        projectType: 'Centralized Exchange',
        duration: '14 months',
        teamSize: '22 developers',
        technologies:[
            'https://blockchaintechs.io/wp-content/uploads/2022/08/3-Express-JS.webp',
            'https://blockchaintechs.io/wp-content/uploads/2022/08/5-AWS.webp',
            'https://blockchaintechs.io/wp-content/uploads/2022/08/logoReactNative.webp',
            'https://blockchaintechs.io/wp-content/uploads/2022/08/27.webp',
            'https://blockchaintechs.io/wp-content/uploads/2022/08/10-1-Solidity.webp',
            'https://blockchaintechs.io/wp-content/uploads/2022/08/Java.webp',
        ],
        textContent:<>
        <br/><br/>
        <div style={{display: 'flex', alignItems: 'center', gap: '10px', marginBottom: '10px'}}>
          <BarChart color='#00d46c' size={24} />
          <h2 style={{fontSize: '28px', fontWeight: 'bold', color: '#ffffff', margin: 0}}>CryptoMax - CEX Platform</h2>
        </div>
        <div style={{display: 'flex', gap: '15px', marginBottom: '15px', flexWrap: 'wrap'}}>
          <span style={{background: '#00d46c', color: '#000', padding: '4px 12px', borderRadius: '20px', fontSize: '12px', fontWeight: 'bold'}}>
            CEX & Trading
          </span>
          <span style={{background: 'rgba(0,212,108,0.2)', color: '#00d46c', padding: '4px 12px', borderRadius: '20px', fontSize: '12px', fontWeight: 'bold'}}>
            14 months
          </span>
          <span style={{background: 'rgba(0,212,108,0.2)', color: '#00d46c', padding: '4px 12px', borderRadius: '20px', fontSize: '12px', fontWeight: 'bold'}}>
            22 developers
          </span>
        </div>
        <PText style={{fontSize: '16px', lineHeight: '1.6', marginBottom: '20px', color: '#ffffff'}}>
        Enterprise-grade centralized cryptocurrency exchange platform with advanced trading features, institutional-grade security, and multi-currency support. Features spot trading, futures, options, and margin trading with high-frequency trading capabilities.
        </PText>
        <div style={{marginBottom: '15px'}}>
          <h3 style={{fontSize: '18px', marginBottom: '10px', color: '#ffffff', margin: '0 0 10px 0'}}>🏦 Exchange Features:</h3>
        </div>
    {[
  {icon: <BarChart color='#00d46c' size={16} />, text: 'Advanced trading engine with high-frequency support'},
  {icon: <Lock color='#00d46c' size={16} />, text: 'Multi-signature cold storage security'},
  {icon: <TrendingUp color='#00d46c' size={16} />, text: 'Spot, Futures, Options, and Margin trading'},
  {icon: <Users color='#00d46c' size={16} />, text: 'Institutional and retail trading APIs'},
  {icon: <Shield color='#00d46c' size={16} />, text: 'KYC/AML compliance and regulatory framework'}
].map((item, index) => (
      <div key={index} style={{padding:'8px 0', display: 'flex', alignItems: 'center', gap: '10px'}} className="d-flex align-items-center">
        {item.icon} <span style={{fontSize: '14px', color: '#ffffff'}}>{item.text}</span>
      </div>
    ))}
        </>,
        companyName:'CryptoMax',
        companyColor:'rgb(0, 136, 204)',
        companyImage:'https://blockchaintechs.io/wp-content/uploads/2022/11/BT-Swap.webp',
        liveUrl: 'https://cryptomax.exchange',
        caseStudyUrl: '/case-studies/cryptomax'
        },
    // Additional Professional Projects
    {
        image1:'https://blockchaintechs.io/wp-content/uploads/2022/08/Metaplexar-DeFi-Dashboard.webp',
        image2:'https://blockchaintechs.io/wp-content/uploads/2022/08/Metaplexar-Investing-Dashboard.webp',
        background:"linear-gradient(135deg, #1a1a2e 0%, #16213e 100%)",
        category: 'DeFi & Trading',
        projectType: 'Enterprise Platform',
        duration: '9 months',
        teamSize: '12 developers',
        technologies:[
            'https://blockchaintechs.io/wp-content/uploads/2022/08/3-Express-JS.webp',
            'https://blockchaintechs.io/wp-content/uploads/2022/08/5-AWS.webp',
            'https://blockchaintechs.io/wp-content/uploads/2022/08/logoReactNative.webp',
            'https://blockchaintechs.io/wp-content/uploads/2022/08/27.webp',
            'https://blockchaintechs.io/wp-content/uploads/2022/08/10-1-Solidity.webp',
            'https://blockchaintechs.io/wp-content/uploads/2022/08/Java.webp',
        ],
        textContent:<>
        <br/><br/>
        <div style={{display: 'flex', alignItems: 'center', gap: '10px', marginBottom: '10px'}}>
          <DollarSign color='#00d46c' size={24} />
          <h2 style={{fontSize: '28px', fontWeight: 'bold', color: '#ffffff', margin: 0}}>DeFi Yield Aggregator</h2>
        </div>
        <div style={{display: 'flex', gap: '15px', marginBottom: '15px', flexWrap: 'wrap'}}>
          <span style={{background: '#00d46c', color: '#000', padding: '4px 12px', borderRadius: '20px', fontSize: '12px', fontWeight: 'bold'}}>
            DeFi & Trading
          </span>
          <span style={{background: 'rgba(0,212,108,0.2)', color: '#00d46c', padding: '4px 12px', borderRadius: '20px', fontSize: '12px', fontWeight: 'bold'}}>
            9 months
          </span>
          <span style={{background: 'rgba(0,212,108,0.2)', color: '#00d46c', padding: '4px 12px', borderRadius: '20px', fontSize: '12px', fontWeight: 'bold'}}>
            12 developers
          </span>
        </div>
        <PText style={{fontSize: '16px', lineHeight: '1.6', marginBottom: '20px', color: '#ffffff'}}>
        Advanced DeFi yield aggregator that automatically finds and compounds the best yield farming opportunities across multiple protocols. Features auto-compounding, risk assessment, and portfolio optimization.
        </PText>
        <div style={{marginBottom: '15px'}}>
          <h3 style={{fontSize: '18px', marginBottom: '10px', color: '#ffffff', margin: '0 0 10px 0'}}>💰 DeFi Features:</h3>
        </div>
    {[
  {icon: <Star color='#FFD700' size={16} />, text: 'Multi-protocol yield optimization'},
  {icon: <TrendingUp color='#00d46c' size={16} />, text: 'Automated compounding strategies'},
  {icon: <Shield color='#00d46c' size={16} />, text: 'Smart contract risk assessment'},
  {icon: <BarChart color='#00d46c' size={16} />, text: 'Portfolio performance analytics'},
  {icon: <Zap color='#00d46c' size={16} />, text: 'Gas optimization and batching'}
].map((item, index) => (
      <div key={index} style={{padding:'8px 0', display: 'flex', alignItems: 'center', gap: '10px'}} className="d-flex align-items-center">
        {item.icon} <span style={{fontSize: '14px', color: '#ffffff'}}>{item.text}</span>
      </div>
    ))}
        </>,
        companyName:'YieldMax',
        companyColor:'rgb(255, 193, 7)',
        companyImage:'https://blockchaintechs.io/wp-content/uploads/2022/08/Hive-Investments.webp',
        liveUrl: 'https://yieldmax.finance',
        caseStudyUrl: '/case-studies/yield-aggregator'
        },
    {
        image1:'https://blockchaintechs.io/wp-content/uploads/2022/08/Cobe-User-Dashboard-1024x566.webp',
        image2:'https://blockchaintechs.io/wp-content/uploads/2022/08/Lockness-Escrow-Dashboard-1-1024x739.webp',
        background:"linear-gradient(135deg, #1a1a2e 0%, #16213e 100%)",
        category: 'Supply Chain & IoT',
        projectType: 'Enterprise Solution',
        duration: '11 months',
        teamSize: '15 developers',
        technologies:[
            'https://blockchaintechs.io/wp-content/uploads/2022/08/3-Express-JS.webp',
            'https://blockchaintechs.io/wp-content/uploads/2022/08/5-AWS.webp',
            'https://blockchaintechs.io/wp-content/uploads/2022/08/logoReactNative.webp',
            'https://blockchaintechs.io/wp-content/uploads/2022/08/27.webp',
            'https://blockchaintechs.io/wp-content/uploads/2022/08/10-1-Solidity.webp',
            'https://blockchaintechs.io/wp-content/uploads/2022/08/Java.webp',
        ],
        textContent:<>
        <br/><br/>
        <div style={{display: 'flex', alignItems: 'center', gap: '10px', marginBottom: '10px'}}>
          <Database color='#00d46c' size={24} />
          <h2 style={{fontSize: '28px', fontWeight: 'bold', color: '#ffffff', margin: 0}}>BlockTrace Supply Chain</h2>
        </div>
        <div style={{display: 'flex', gap: '15px', marginBottom: '15px', flexWrap: 'wrap'}}>
          <span style={{background: '#00d46c', color: '#000', padding: '4px 12px', borderRadius: '20px', fontSize: '12px', fontWeight: 'bold'}}>
            Supply Chain & IoT
          </span>
          <span style={{background: 'rgba(0,212,108,0.2)', color: '#00d46c', padding: '4px 12px', borderRadius: '20px', fontSize: '12px', fontWeight: 'bold'}}>
            11 months
          </span>
          <span style={{background: 'rgba(0,212,108,0.2)', color: '#00d46c', padding: '4px 12px', borderRadius: '20px', fontSize: '12px', fontWeight: 'bold'}}>
            15 developers
          </span>
        </div>
        <PText style={{fontSize: '16px', lineHeight: '1.6', marginBottom: '20px', color: '#ffffff'}}>
        Blockchain-based supply chain management system with IoT integration for real-time tracking, authenticity verification, and automated compliance reporting. Serves Fortune 500 companies globally.
        </PText>
        <div style={{marginBottom: '15px'}}>
          <h3 style={{fontSize: '18px', marginBottom: '10px', color: '#ffffff', margin: '0 0 10px 0'}}>🔗 Supply Chain Features:</h3>
        </div>
    {[
  {icon: <Shield color='#00d46c' size={16} />, text: 'End-to-end product traceability'},
  {icon: <Database color='#00d46c' size={16} />, text: 'IoT sensor data integration'},
  {icon: <Check color='#00d46c' size={16} />, text: 'Automated compliance reporting'},
  {icon: <Globe color='#00d46c' size={16} />, text: 'Multi-stakeholder collaboration'},
  {icon: <TrendingUp color='#00d46c' size={16} />, text: 'Predictive analytics and insights'}
].map((item, index) => (
      <div key={index} style={{padding:'8px 0', display: 'flex', alignItems: 'center', gap: '10px'}} className="d-flex align-items-center">
        {item.icon} <span style={{fontSize: '14px', color: '#ffffff'}}>{item.text}</span>
      </div>
    ))}
        </>,
        companyName:'BlockTrace',
        companyColor:'rgb(34, 139, 34)',
        companyImage:'https://blockchaintechs.io/wp-content/uploads/2022/11/BT-Swap.webp',
        liveUrl: 'https://blocktrace.supply',
        caseStudyUrl: '/case-studies/supply-chain'
        }
]
