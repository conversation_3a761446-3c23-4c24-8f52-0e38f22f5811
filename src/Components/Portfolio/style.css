.header{
    width:100%;
    padding:10px;
}
@media (max-width:700px) {
.header button{
 width:100%; 
 text-align: center !important;

}
.headerContainer{
    display: block !important;

}
.headerContainer >*{
width:100% !important;
}
}


*[class*=padding_Pc]{
    padding:16px!important;
    }
@media (min-width:700px) {
    
*[class*=padding_Pc]{
    padding:20px 45px !important;
    }
    .header{    
    padding:10px 40px;
    padding-left: 80px
    }

}
.header *{
    text-align: start !important;
}

.projectContainer{
    width: 100%;
    min-height: 80vh;
    margin-bottom: 60px;
    position: relative;
    overflow: hidden;
}

.projectContainer::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(0,212,108,0.02) 0%, rgba(0,168,85,0.02) 100%);
    border-radius: 20px;
    z-index: 0;
}

.projectContainer .topImage{
    width: 100% !important;
    background: linear-gradient(135deg, #00d46c 0%, #00a855 100%);
    height: 8px;
    background-size: 100% 100%;
    border-radius: 4px;
    margin-bottom: 20px;
    box-shadow: 0 4px 15px rgba(0,212,108,0.3);
}

.projectCtn {
    position: relative;
    z-index: 1;
    background: rgba(255,255,255,0.02);
    border-radius: 20px;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255,255,255,0.1);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.projectContainer:hover .projectCtn {
    transform: translateY(-5px);
    box-shadow: 0 20px 40px rgba(0,0,0,0.2);
}

.contentCtn {
    gap: 40px !important;
    align-items: flex-start !important;
}

.textContainer {
    flex: 1;
    min-width: 300px;
    padding: 20px;
}

.imageContainer {
    flex: 1;
    min-width: 300px;
}

.colored {
    border-radius: 20px !important;
    padding: 30px !important;
    box-shadow: 0 20px 40px rgba(0,0,0,0.3) !important;
    position: relative;
    overflow: hidden;
}

.images {
    position: relative;
    z-index: 2;
}

.image1, .image2 {
    border-radius: 15px !important;
    overflow: hidden !important;
    box-shadow: 0 10px 30px rgba(0,0,0,0.3) !important;
    transition: transform 0.3s ease;
}

.image1:hover, .image2:hover {
    transform: scale(1.02);
}

/* Enhanced responsive design */
@media (max-width: 768px) {
    .contentCtn {
        flex-direction: column !important;
        gap: 20px !important;
    }

    .textContainer, .imageContainer {
        min-width: unset;
        width: 100%;
    }

    .projectContainer {
        margin-bottom: 40px;
    }

    .colored {
        padding: 20px !important;
    }
}
.projectContainer .projectCtn{
    width:90%;
    margin:0 auto;
    min-height:70vh;
    border-radius:20px;
        box-shadow:1px 1px 30px 0px rgb(236, 236, 240);
    }
.contentCtn{
width:100%;
height: 100%;
}
.contentCtn{
min-height: 100%;

}

.contentCtn .imageContainer{
    width:65%;
    min-height:450px;
    border-radius:20px;
    margin-top:16px;
}
.contentCtn .imageContainer .colored{
    background:rgb(225, 0, 255);
   width:100%;
    min-height:400px;
    margin-top:50px;
    border-radius:20px;
    padding:16px;
}

.contentCtn .imageContainer .colored .images{
position: relative;
width:100%;
    min-height:300px;
   
}
.contentCtn .imageContainer .colored .images *[class*=image]{
    width:85%; 
    height: 70%; 
    position: relative;
    display: block;
    position: absolute;
    border-radius: 20px;
    /* box-shadow: 1px 1px 10px 0px rgb(88, 84, 84); */
    display:flex;
    margin: auto; /* This centers the image horizontally */
    top: 30%; /* Position the top of the image at the vertical center */
    left: 50%; /* Position the left side of the image at the horizontal center */
    transform: translate(-50%, -50%); /* Move the image back by half of its width and height */
transition:all 0.6s ease;
cursor:pointer;
}
.contentCtn .imageContainer .colored .images *[class*=image]:hover{
    height: 100% !important;
    width:90% !important;
    z-index: 999;
min-width:300px;
}
.contentCtn .imageContainer .colored .image2{
    margin-left:80px !important;
    margin-top:-80px !important;

}
@media (max-width : 700px) {
    .contentCtn .imageContainer .colored .image2{
        margin-left:30px !important;
        margin-top:-30px !important;
    }

}
.contentCtn .textContainer {
    width:35%;
    padding:6px;    
}
p .sd{
    background:rgb(53, 14, 78);
}
    .projectContainer{
margin-top: 20px;
    }
body{
    overflow-x: hidden !important;
}