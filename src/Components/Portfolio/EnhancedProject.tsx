import Image from 'next/image';
import React from 'react';
import { ExternalLink, FileText, Clock, Users, DollarSign } from 'react-feather';
import CompanyTag from './companyTag';

interface Props {
    image1: string;
    image2: string;
    technologies: string[];
    textContent: any;
    companyName: string;
    companyColor: string;
    companyImage: string;
    background?: string;
    flip?: boolean;
    category?: string;
    projectType?: string;
    duration?: string;
    teamSize?: string;
    budget?: string;
    liveUrl?: string;
    caseStudyUrl?: string;
}

const EnhancedProject: React.FC<Props> = ({
    image1,
    image2,
    technologies,
    textContent,
    companyName,
    companyImage,
    companyColor,
    background,
    flip,
    category,
    projectType,
    duration,
    teamSize,
    budget,
    liveUrl,
    caseStudyUrl
}) => {
    return (
        <>
            <div className='projectContainer' style={{ marginBottom: '60px' }}>
                <div className='topImage'></div>
                <div className='projectCtn' style={{ padding: '40px 20px' }}>
                    <CompanyTag name={companyName} color={companyColor} image={companyImage} />
                    
                    {/* Project Stats Bar */}
                    <div style={{
                        display: 'flex',
                        gap: '20px',
                        marginBottom: '30px',
                        flexWrap: 'wrap',
                        justifyContent: 'center',
                        padding: '15px',
                        background: 'rgba(255,255,255,0.05)',
                        borderRadius: '15px',
                        backdropFilter: 'blur(10px)'
                    }}>
                        {duration && (
                            <div style={{ display: 'flex', alignItems: 'center', gap: '8px', color: '#fff' }}>
                                <Clock size={16} color='#00d46c' />
                                <span style={{ fontSize: '14px', fontWeight: '500' }}>{duration}</span>
                            </div>
                        )}
                        {teamSize && (
                            <div style={{ display: 'flex', alignItems: 'center', gap: '8px', color: '#fff' }}>
                                <Users size={16} color='#00d46c' />
                                <span style={{ fontSize: '14px', fontWeight: '500' }}>{teamSize}</span>
                            </div>
                        )}
                        {budget && (
                            <div style={{ display: 'flex', alignItems: 'center', gap: '8px', color: '#fff' }}>
                                <DollarSign size={16} color='#00d46c' />
                                <span style={{ fontSize: '14px', fontWeight: '500' }}>{budget}</span>
                            </div>
                        )}
                        {projectType && (
                            <div style={{
                                background: '#00d46c',
                                color: '#000',
                                padding: '4px 12px',
                                borderRadius: '20px',
                                fontSize: '12px',
                                fontWeight: 'bold'
                            }}>
                                {projectType}
                            </div>
                        )}
                    </div>

                    <div className='contentCtn d-flex m-block' style={{ 
                        flexFlow: flip ? "row-reverse" : undefined,
                        gap: '40px',
                        alignItems: 'center'
                    }}>
                        <div className='textContainer' style={{ flex: '1', minWidth: '300px' }}>
                            {textContent}
                            
                            {/* Action Buttons */}
                            <div style={{
                                display: 'flex',
                                gap: '15px',
                                marginTop: '30px',
                                flexWrap: 'wrap'
                            }}>
                                {liveUrl && (
                                    <a
                                        href={liveUrl}
                                        target="_blank"
                                        rel="noopener noreferrer"
                                        style={{
                                            display: 'flex',
                                            alignItems: 'center',
                                            gap: '8px',
                                            background: 'linear-gradient(135deg, #00d46c, #00a855)',
                                            color: '#fff',
                                            padding: '12px 20px',
                                            borderRadius: '25px',
                                            textDecoration: 'none',
                                            fontSize: '14px',
                                            fontWeight: '600',
                                            transition: 'transform 0.2s ease',
                                            border: 'none'
                                        }}
                                        onMouseEnter={(e) => e.currentTarget.style.transform = 'translateY(-2px)'}
                                        onMouseLeave={(e) => e.currentTarget.style.transform = 'translateY(0)'}
                                    >
                                        <ExternalLink size={16} />
                                        View Live
                                    </a>
                                )}
                                {caseStudyUrl && (
                                    <a
                                        href={caseStudyUrl}
                                        style={{
                                            display: 'flex',
                                            alignItems: 'center',
                                            gap: '8px',
                                            background: 'rgba(255,255,255,0.1)',
                                            color: '#fff',
                                            padding: '12px 20px',
                                            borderRadius: '25px',
                                            textDecoration: 'none',
                                            fontSize: '14px',
                                            fontWeight: '600',
                                            transition: 'transform 0.2s ease',
                                            border: '1px solid rgba(255,255,255,0.2)'
                                        }}
                                        onMouseEnter={(e) => e.currentTarget.style.transform = 'translateY(-2px)'}
                                        onMouseLeave={(e) => e.currentTarget.style.transform = 'translateY(0)'}
                                    >
                                        <FileText size={16} />
                                        Case Study
                                    </a>
                                )}
                            </div>
                        </div>

                        <div className='imageContainer' style={{ flex: '1', minWidth: '300px' }}>
                            <div className='colored' style={{
                                background,
                                borderRadius: '20px',
                                padding: '30px',
                                boxShadow: '0 20px 40px rgba(0,0,0,0.3)',
                                position: 'relative',
                                overflow: 'hidden'
                            }}>
                                {/* Decorative elements */}
                                <div style={{
                                    position: 'absolute',
                                    top: '-50px',
                                    right: '-50px',
                                    width: '100px',
                                    height: '100px',
                                    background: 'rgba(255,255,255,0.1)',
                                    borderRadius: '50%',
                                    filter: 'blur(20px)'
                                }}></div>
                                
                                <div className='images' style={{ position: 'relative', zIndex: 2 }}>
                                    <div className='image1' style={{
                                        position: 'relative',
                                        width: '100%',
                                        height: '200px',
                                        marginBottom: '20px',
                                        borderRadius: '15px',
                                        overflow: 'hidden',
                                        boxShadow: '0 10px 30px rgba(0,0,0,0.3)'
                                    }}>
                                        <Image alt='' fill style={{ objectFit: 'cover' }} src={image1} />
                                    </div>
                                    <div className='image2' style={{
                                        position: 'relative',
                                        width: '100%',
                                        height: '200px',
                                        borderRadius: '15px',
                                        overflow: 'hidden',
                                        boxShadow: '0 10px 30px rgba(0,0,0,0.3)'
                                    }}>
                                        <Image alt='' fill style={{ objectFit: 'cover' }} src={image2} />
                                    </div>
                                </div>

                                {/* Technology Stack */}
                                <div style={{
                                    marginTop: '25px',
                                    display: 'flex',
                                    flexWrap: 'wrap',
                                    gap: '10px',
                                    justifyContent: 'center'
                                }}>
                                    {technologies.map((tech, index) => (
                                        <div key={index} style={{
                                            width: '40px',
                                            height: '40px',
                                            background: 'rgba(255,255,255,0.9)',
                                            borderRadius: '10px',
                                            display: 'flex',
                                            alignItems: 'center',
                                            justifyContent: 'center',
                                            boxShadow: '0 5px 15px rgba(0,0,0,0.2)',
                                            transition: 'transform 0.2s ease'
                                        }}
                                        onMouseEnter={(e) => e.currentTarget.style.transform = 'scale(1.1)'}
                                        onMouseLeave={(e) => e.currentTarget.style.transform = 'scale(1)'}
                                        >
                                            <Image
                                                src={tech}
                                                alt="Technology"
                                                width={24}
                                                height={24}
                                                style={{ objectFit: 'contain' }}
                                            />
                                        </div>
                                    ))}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </>
    );
};

export default EnhancedProject;
