import PText from "@/utils/PText2";
import WhiteBold from "@/utils/WhiteBold";
import { Check, Star, TrendingUp, Shield, Zap, Globe, DollarSign, Users, Award, ExternalLink } from "react-feather";

export const ProjectsList=[
    {
    image1:'https://blockchaintechs.io/wp-content/uploads/2022/12/Metaverse-Ecosystem-2-1.webp',
    image2:'https://blockchaintechs.io/wp-content/uploads/2022/08/Hives-DeFi-Dashboard.webp',
    background:'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
    category: 'Metaverse & Gaming',
    projectType: 'Enterprise Solution',
    duration: '8 months',
    teamSize: '12 developers',
    technologies:[
        'https://blockchaintechs.io/wp-content/uploads/2022/08/3-Express-JS.webp',
        'https://blockchaintechs.io/wp-content/uploads/2022/08/5-AWS.webp',
        'https://blockchaintechs.io/wp-content/uploads/2022/08/logoReactNative.webp',
        'https://blockchaintechs.io/wp-content/uploads/2022/08/27.webp',
        'https://blockchaintechs.io/wp-content/uploads/2022/08/10-1-Solidity.webp',
        'https://blockchaintechs.io/wp-content/uploads/2022/08/Java.webp',
    ],
    textContent:<>
    <br/><br/>
    <div style={{display: 'flex', alignItems: 'center', gap: '10px', marginBottom: '10px'}}>
      <Globe color='#00d46c' size={24} />
      <WhiteBold style={{fontSize: '28px', fontWeight: 'bold'}}>MetaVerse Ecosystem</WhiteBold>
    </div>
    <div style={{display: 'flex', gap: '15px', marginBottom: '15px', flexWrap: 'wrap'}}>
      <span style={{background: '#00d46c', color: '#000', padding: '4px 12px', borderRadius: '20px', fontSize: '12px', fontWeight: 'bold'}}>
        Metaverse & Gaming
      </span>
      <span style={{background: 'rgba(255,255,255,0.2)', color: '#fff', padding: '4px 12px', borderRadius: '20px', fontSize: '12px'}}>
        8 months
      </span>
      <span style={{background: 'rgba(255,255,255,0.2)', color: '#fff', padding: '4px 12px', borderRadius: '20px', fontSize: '12px'}}>
        12 developers
      </span>
    </div>
    <PText style={{fontSize: '16px', lineHeight: '1.6', marginBottom: '20px'}}>
    Revolutionary Web 3.0 Metaverse platform featuring realistic graphics, real-world locations, and authentic identities. Our comprehensive ecosystem includes Meta Cities, Meta Lands, Meta Events, and Meta Games, providing users with genuine interactions in a lifelike virtual environment.
    </PText>
    <div style={{marginBottom: '15px'}}>
      <WhiteBold style={{fontSize: '18px', marginBottom: '10px'}}>🚀 Key Achievements:</WhiteBold>
    </div>
{[
  {icon: <Star color='#FFD700' size={16} />, text: 'Real world experience in immersive 3D environment'},
  {icon: <Check color='#00d46c' size={16} />, text: 'ReadyPlayerMe SDK integration for custom avatars'},
  {icon: <TrendingUp color='#00d46c' size={16} />, text: 'Built-in 3D NFT Marketplace with Web 3.0 integration'},
  {icon: <Zap color='#00d46c' size={16} />, text: 'Live events and interactive gaming experiences'},
  {icon: <Shield color='#00d46c' size={16} />, text: 'Secure retail shops and business districts'}
].map((item, index) => (
  <div key={index} style={{padding:'8px 0', display: 'flex', alignItems: 'center', gap: '10px'}} className="d-flex align-items-center">
    {item.icon} <span style={{fontSize: '14px'}}>{item.text}</span>
  </div>
))}
    </>,
    companyName:'MetaBloqs',
    companyColor:'rgb(16,59,102)',
    companyImage:'https://blockchaintechs.io/wp-content/uploads/2022/11/BT-Swap.webp',
    liveUrl: 'https://metabloqs.com',
    githubUrl: 'https://github.com/altapplabs/metabloqs',
    caseStudyUrl: '/case-studies/metabloqs'
    },
    {
        image1:'https://blockchaintechs.io/wp-content/uploads/2022/08/Metaplexar-Investment-Dashboard-Portfolio-Performance-300x237.webp',
        image2:'https://blockchaintechs.io/wp-content/uploads/2022/08/Metaplexar-Caste-Your-Vote-300x250.webp',
        technologies:[
            'https://blockchaintechs.io/wp-content/uploads/2022/08/3-Express-JS.webp',
            'https://blockchaintechs.io/wp-content/uploads/2022/08/5-AWS.webp',
            'https://blockchaintechs.io/wp-content/uploads/2022/08/logoReactNative.webp',
            'https://blockchaintechs.io/wp-content/uploads/2022/08/27.webp',
            'https://blockchaintechs.io/wp-content/uploads/2022/08/10-1-Solidity.webp',
            'https://blockchaintechs.io/wp-content/uploads/2022/08/Java.webp',
        ],
        background:"rgb(53, 14, 78)",
        textContent:<>
        <br/><br/><WhiteBold>Move to Earn</WhiteBold>
        <br/>
        <PText>
        Fitmint is a “Move to Earn Crypto” application that tracks Walking, Running, Cycling, and Swimming by integrating a Smartwatch. It’s basically a fitness tracker application. Players can conquer challenges in-game by Participating in Weekly and Monthly challenges
         </PText>
        <br/>
    {[
  "In-build NFT and Healthcare Product Marketplace",
  "Connect Interface with Smart Watch",
  "Activity Live Tracking and Profile with Smart Watch",
  "Redeem Your Rewards Against the Token",
  "Boosting your Physical Fitness and earn Rewards"
].map((experience, index) => (
      <div key={index} style={{padding:6}} className="d-flex align-items-center">
        <Check color='green' /> <span>{experience}</span>
      </div>
    ))}
        </>,
        companyName:'Fitmint        ',
        companyColor:'rgb(75,70,231)',
        companyImage:'https://blockchaintechs.io/wp-content/uploads/2022/12/Fitmint-logo.webp'
        },
        {
            image1:'https://blockchaintechs.io/wp-content/uploads/2022/08/ICO-Vesting-and-Swapping-768x521.webp',
            image2:'https://blockchaintechs.io/wp-content/uploads/2022/08/Hives-DeFi-Dashboard.webp',
            technologies:[
                'https://blockchaintechs.io/wp-content/uploads/2022/08/3-Express-JS.webp',
                'https://blockchaintechs.io/wp-content/uploads/2022/08/5-AWS.webp',
                'https://blockchaintechs.io/wp-content/uploads/2022/08/logoReactNative.webp',
                'https://blockchaintechs.io/wp-content/uploads/2022/08/27.webp',
                'https://blockchaintechs.io/wp-content/uploads/2022/08/10-1-Solidity.webp',
                'https://blockchaintechs.io/wp-content/uploads/2022/08/Java.webp',
            ],
            textContent:<>
            <br/><br/><WhiteBold>NFT Auction Platform</WhiteBold>
            <br/>
            <PText>
            NFT auction platform to raise capital. Top 20 premium NFTs are offered for auction for community to bid to invest on the project. smart contract wth timed auction logic for transparancy.
             </PText>
            <br/>
        {[
      "In-build NFT and Healthcare Product Marketplace",
      "Connect Interface with Smart Watch",
      "Activity Live Tracking and Profile with Smart Watch",
      "Redeem Your Rewards Against the Token",
      "Boosting your Physical Fitness and earn Rewards"
    ].map((experience, index) => (
          <div key={index} style={{padding:6}} className="d-flex align-items-center">
            <Check color='green' />{experience} <span>{experience}</span>
          </div>
        ))}
            </>,
            companyName:'NFT Platform',
            companyColor:'#161926',
        background:"rgb(22,26,39)",
            companyImage:'https://blockchaintechs.io/wp-content/uploads/2022/08/Hive-Investments.webp'
            }
            /**add more... */
]