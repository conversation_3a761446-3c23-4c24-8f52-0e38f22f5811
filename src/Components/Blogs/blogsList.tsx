import React from 'react'
import BlogItem from './BlogItem';
import FilterLinks from './filter';

const BlogList:React.FC=()=>{
    const BlogItemLists=[

        {
            heading:"AR and VR Development Services",
            image:"https://blockchaintechs.io/wp-content/uploads/2022/11/AR-VR-Development-Services-for-Better-User-Experience-Preview.jpg",
            date:"November 10, 2022",
            text:"AR and VR Development Services Overview of AR/VR Development One of the most important technological advancements in recent"
        },{
            heading:"Top Web 3.0 Business Ideas to Make Rich in 2024",
            image:"https://blockchaintechs.io/wp-content/uploads/2022/11/Top-Web-3.0-Business-Ideas-to-Make-You-Rich-in-2023-300x300.jpg",
            date:"November 4, 2022",
            text:"Top Web 3.0 Business Ideas to Make Rich in 2024 Web3 commerce has revolutionized the digital world and created a wide range of new"
        },{
            heading:"Metaverse Empowering the Gaming Industry",
            image:"https://blockchaintechs.io/wp-content/uploads/2022/12/Metaverse-Empowering-the-Gamin-Industry.jpg",
            date:"October 28, 2022",
            text:"Metaverse Empowering the Gaming Industry The majority of players believe that the metaverse will transform the gaming industry. The"
        },{
            heading:"Metaverse Reshaping the Fashion Industry",
            image:"https://blockchaintechs.io/wp-content/uploads/2022/12/Metaverse-Reshaping-the-Fashion-Industry-Preview-300x300.jpg",
            date:"October 13, 2022",
            text:"Metaverse Reshaping the Fashion Industry Many businesses seeking to enter this new digital form market have found inspiration"
        }
        //add more..
    ]
    return (
        <>
        <div className='d-flex m-block'>

          <div className='blogList d-flex m-block' style={{width:"65%",flexFlow:'row wrap',padding:20,paddingLeft:45} }>
              {BlogItemLists.map((e,i)=><BlogItem {...e} key={i}/>)}
          </div>

<div style={{width:"30%"}}>
<FilterLinks/>
</div>
        </div>
        </>
    )
}

export default BlogList;