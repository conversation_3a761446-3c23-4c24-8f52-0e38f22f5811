import React from 'react'
const FilterLinks:React.FC=(prop)=>{
    const blogPosts = [
        { name: "Latest Blog Posts", href: "#" },
        { name: "AR and VR Development Services", href: "#" },
        { name: "Top Web 3.0 Business Ideas to Make Rich in 2024", href: "#" },
        { name: "Metaverse Empowering the Gaming Industry", href: "#" },
        { name: "Metaverse Reshaping the Fashion Industry", href: "#" },
        { name: "Web 3.0 Transform the Future Industry", href: "#" },
        //add more...
      ];
      
return (
    <div className='filterLinks'>
<div>
<b style={{color:"var(--blueText)"}}>
Latest Blog Posts

</b>
<br/>

{blogPosts.map((e,i)=><li style={{marginTop:6}} key={i}>
    <a href='#'>
{`${e.name}`}
    </a>
</li>)}

    </div>

    </div>
)
}
export default FilterLinks
