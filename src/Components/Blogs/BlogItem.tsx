import PText from '@/utils/PText2';
import WhiteBold from '@/utils/WhiteBold';
import Image from 'next/image';
import React from 'react'
interface Props{
    image:string,
    heading:string,
    text:string,
    date:string
}
const BlogItem:React.FC<Props>=(prop)=>{
    return (
        <>
        <div className='blogItem'>
<Image src={prop.image} width={500} style={{maxHeight:300}} height={500} alt=''/>
        <br/>
        <WhiteBold >
        <b style={{fontSize:"medium"}}>{prop.heading}</b>
        </WhiteBold>
        <p>
        {prop.date}
        </p>
        <PText>
        {prop.text}
        </PText>
        <br/>
        </div>
        </>
    )
}

export default BlogItem;