import { MDBBtn } from "mdb-react-ui-kit";
import TopNav from "../fixed/TopNav";
import SideContact from "../fixed/sideContact";
import TakeYourBusiness from "../LandingPage/takeYourBusiness";
import WhyChooseUs from "../LandingPage/whyChooseUs";
import Testimonials from "../LandingPage/testimonials";
import Blogs from "../LandingPage/Blogs";
import ConnectWithUs from "../LandingPage/connectWithUs";
import "./style.css";
import "../LandingPage/style.css";

import H3 from "../Portfolio/h3";
import { useState } from "react";
import BlogList from "./blogsList";


export default function Index(){
const [width,setWidth]=useState(typeof window=='object' ? window.innerWidth :0);


return (<>
<TopNav/>
<SideContact/>

{/* page start */}

<div style={{width:'100%',minWidth:"100vw",background:"white",minHeight:"100vh",height:"auto"}}>
    
<br/><br/>
<br/><br/>
<br/><br/>
<div className='header' style={{padding:"0px 20px"}}>
<H3 style={{textAlign:"start",marginLeft:-9}}>
BLogs
</H3>
<br/>
<div className="d-flex ">
<span>Home {">"} </span><span style={{color:'var(--blue)',padding:'0px 5px'}}> Blogs</span>
</div>
</div>


<br/>
<BlogList/>
<br/><br/><br/>
</div>


<br/>

<br/><br/>
<Testimonials/>
<br/><br/>
<ConnectWithUs/>
<br/><br/>
</>)
}

