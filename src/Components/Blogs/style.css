.header{
    width:100%;
    padding:10px;
}

@media (max-width:700px) {
    .header button{
     width:100%; 
     text-align: center !important;
    
    }
    .blogList{
        padding-left: 20px !important;   
       }
    }
    @media (min-width:700px) {
        .header{    
        padding:10px 40px;
        padding-left: 80px
        }
        .blogItem{
            margin:0px;
            margin-left: -10px !important;
        }
        
        
    }
    .header *{
        text-align: start !important;
    }
    .blogItem{
        width:47%;
        padding:10px;
        border-radius: 20px;
    }
    .blogItem img{
        min-height:200px;
        width:100%;
    }
    @media (min-width:750px) {
    .filterLinks{
    height: 94%;
    width:100%;
    }

    .filterLinks div{
        height: 100%;
        min-height:100vh;
    }
    }
    .filterLinks div{
        min-height: 100px;
        width:80%;
        margin:0 auto !important;
border-radius: 15px;
background:rgb(235, 236, 240);
padding:17px;
    }
    .filterLinks div a{
        color:var(--blueText);
    }
    .filterLinks div a:hover{
        text-decoration: underline;
        color:var(--green);
    }

    
body{
    overflow-x: hidden !important;
}