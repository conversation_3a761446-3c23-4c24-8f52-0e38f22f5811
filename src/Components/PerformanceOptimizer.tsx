'use client'

import { useEffect } from 'react';
import Head from 'next/head';

const PerformanceOptimizer: React.FC = () => {
  useEffect(() => {
    // Preload critical resources
    const preloadCriticalResources = () => {
      // Preload critical images
      const criticalImages = [
        '/images/logo.png',
        '/images/hero-bg.webp',
      ];

      criticalImages.forEach(src => {
        const link = document.createElement('link');
        link.rel = 'preload';
        link.as = 'image';
        link.href = src;
        document.head.appendChild(link);
      });

      // Preload critical fonts
      const criticalFonts = [
        'https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap',
      ];

      criticalFonts.forEach(href => {
        const link = document.createElement('link');
        link.rel = 'preload';
        link.as = 'style';
        link.href = href;
        document.head.appendChild(link);
      });
    };

    // Optimize images after load
    const optimizeImages = () => {
      const images = document.querySelectorAll('img');
      images.forEach(img => {
        // Add loading="lazy" to non-critical images
        if (!img.hasAttribute('priority')) {
          img.loading = 'lazy';
        }
        
        // Add decoding="async" for better performance
        img.decoding = 'async';
      });
    };

    // Remove unused CSS
    const removeUnusedCSS = () => {
      // This is a simplified version - in production, use tools like PurgeCSS
      const stylesheets = document.querySelectorAll('link[rel="stylesheet"]');
      stylesheets.forEach(stylesheet => {
        const linkElement = stylesheet as HTMLLinkElement;
        if (linkElement.href.includes('unused')) {
          linkElement.remove();
        }
      });
    };

    // Optimize third-party scripts
    const optimizeThirdPartyScripts = () => {
      // Defer non-critical scripts
      const scripts = document.querySelectorAll('script[src]');
      scripts.forEach(script => {
        const scriptElement = script as HTMLScriptElement;
        if (!scriptElement.hasAttribute('async') && !scriptElement.hasAttribute('defer')) {
          scriptElement.defer = true;
        }
      });
    };

    // Service Worker registration for caching
    const registerServiceWorker = async () => {
      if ('serviceWorker' in navigator && process.env.NODE_ENV === 'production') {
        try {
          await navigator.serviceWorker.register('/sw.js');
          console.log('Service Worker registered successfully');
        } catch (error) {
          console.log('Service Worker registration failed:', error);
        }
      }
    };

    // Connection-aware loading
    const adaptToConnection = () => {
      if ('connection' in navigator) {
        const connection = (navigator as any).connection;
        const effectiveType = connection?.effectiveType;
        
        // Reduce quality for slow connections
        if (effectiveType === 'slow-2g' || effectiveType === '2g') {
          document.documentElement.classList.add('slow-connection');
          // Reduce image quality, disable animations, etc.
        }
      }
    };

    // Battery-aware optimizations
    const adaptToBattery = async () => {
      if ('getBattery' in navigator) {
        try {
          const battery = await (navigator as any).getBattery();
          if (battery.level < 0.2 || !battery.charging) {
            document.documentElement.classList.add('low-battery');
            // Reduce animations, lower refresh rates, etc.
          }
        } catch (error) {
          // Battery API not supported
        }
      }
    };

    // Memory-aware optimizations
    const adaptToMemory = () => {
      if ('memory' in performance) {
        const memory = (performance as any).memory;
        const usedMemoryMB = memory.usedJSHeapSize / 1048576;
        
        if (usedMemoryMB > 100) {
          document.documentElement.classList.add('high-memory-usage');
          // Reduce cached resources, lazy load more aggressively
        }
      }
    };

    // Run optimizations
    const runOptimizations = () => {
      preloadCriticalResources();
      optimizeImages();
      removeUnusedCSS();
      optimizeThirdPartyScripts();
      registerServiceWorker();
      adaptToConnection();
      adaptToBattery();
      adaptToMemory();
    };

    // Run immediately for critical optimizations
    preloadCriticalResources();
    
    // Run other optimizations after page load
    if (document.readyState === 'loading') {
      document.addEventListener('DOMContentLoaded', runOptimizations);
    } else {
      runOptimizations();
    }

    // Cleanup
    return () => {
      document.removeEventListener('DOMContentLoaded', runOptimizations);
    };
  }, []);

  return (
    <Head>
      {/* Critical CSS inline */}
      <style dangerouslySetInnerHTML={{
        __html: `
          /* Critical above-the-fold styles */
          body { margin: 0; font-family: -apple-system, BlinkMacSystemFont, sans-serif; }
          .loading { opacity: 0.7; }
          .loaded { opacity: 1; transition: opacity 0.3s; }
        `
      }} />
      
      {/* DNS prefetch for external domains */}
      <link rel="dns-prefetch" href="//blockchaintechs.io" />
      <link rel="dns-prefetch" href="//fonts.googleapis.com" />
      
      {/* Preconnect to critical origins */}
      <link rel="preconnect" href="https://fonts.gstatic.com" crossOrigin="" />
      
      {/* Resource hints */}
      <link rel="prefetch" href="/images/testimonials.webp" />
      <link rel="prefetch" href="/images/portfolio.webp" />
    </Head>
  );
};

export default PerformanceOptimizer;
