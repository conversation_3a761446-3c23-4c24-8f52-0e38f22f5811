import React, { useState, useCallback } from 'react';
import Image from 'next/image';

interface SuperOptimizedImageProps {
  src: string;
  alt: string;
  width?: number;
  height?: number;
  className?: string;
  style?: React.CSSProperties;
  priority?: boolean;
  quality?: number;
  fill?: boolean;
  sizes?: string;
  objectFit?: 'contain' | 'cover' | 'fill' | 'none' | 'scale-down';
  loading?: 'lazy' | 'eager';
  placeholder?: 'blur' | 'empty';
}

// Tiny 1x1 transparent pixel as base64
const TRANSPARENT_PIXEL = 'data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7';

// Generate a simple blur placeholder
const generateBlurDataURL = (width: number = 10, height: number = 10) => {
  const canvas = document.createElement('canvas');
  canvas.width = width;
  canvas.height = height;
  const ctx = canvas.getContext('2d');
  if (ctx) {
    ctx.fillStyle = '#1a1a1a';
    ctx.fillRect(0, 0, width, height);
  }
  return canvas.toDataURL();
};

const SuperOptimizedImage: React.FC<SuperOptimizedImageProps> = ({
  src,
  alt,
  width,
  height,
  className = '',
  style = {},
  priority = false,
  quality = 60, // Lower quality for faster loading
  fill = false,
  sizes,
  objectFit = 'cover',
  loading = 'lazy',
  placeholder = 'blur',
  ...props
}) => {
  const [isLoading, setIsLoading] = useState(true);
  const [hasError, setHasError] = useState(false);

  const handleLoad = useCallback(() => {
    setIsLoading(false);
  }, []);

  const handleError = useCallback(() => {
    setHasError(true);
    setIsLoading(false);
  }, []);

  // Use transparent pixel for error state
  const imageSrc = hasError ? TRANSPARENT_PIXEL : src;

  const imageProps: any = {
    src: imageSrc,
    alt,
    className: `super-optimized-image ${className}`,
    style: {
      ...style,
      transition: 'opacity 0.2s ease',
      opacity: isLoading ? 0.3 : 1,
    },
    onLoad: handleLoad,
    onError: handleError,
    quality,
    priority,
    loading: priority ? 'eager' : loading,
    placeholder: placeholder === 'blur' ? 'blur' : 'empty',
    blurDataURL: placeholder === 'blur' ? TRANSPARENT_PIXEL : undefined,
    ...props,
  };

  if (fill) {
    imageProps.fill = true;
    imageProps.style = {
      ...imageProps.style,
      objectFit,
    };
    if (sizes) {
      imageProps.sizes = sizes;
    }
  } else {
    if (width) imageProps.width = width;
    if (height) imageProps.height = height;
    if (sizes) imageProps.sizes = sizes;
  }

  return (
    <div className="super-optimized-image-container" style={{ position: 'relative' }}>
      <Image {...imageProps} />
      {hasError && (
        <div
          style={{
            position: 'absolute',
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            backgroundColor: '#1a1a1a',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            color: '#666',
            fontSize: '12px',
          }}
        >
          Image unavailable
        </div>
      )}
    </div>
  );
};

export default SuperOptimizedImage;
