import { MDBBtn } from 'mdb-react-ui-kit';
import React, { useState } from 'react'
import SubNavContainer from '../fixed/subNavContainer';
import { setConfig } from 'next/config';
import ServicesContent from '../subComponentContent/services';
import NFTs from '../subComponentContent/nft';
import P2E from '../subComponentContent/p2e';
import Meterverse from '../subComponentContent/meterverse';
import { useRouter } from 'next/router';
import WhatsAppIcon from '@/utils/whatsApp';
import TelegramIcon from '@/utils/telegram';

const NavButtons:React.FC=()=>{
    const [showSubNav,setShowSubNav]=useState(false);
    
    const route=useRouter();
    const [currentSubNav,setCurrentSubNav]=useState(<ServicesContent/>)
    const switchSubNav=()=>{
        setShowSubNav(false);
        //put new content
        setTimeout(() => {
            setShowSubNav(true);
        },100);
    }
    return (<div style={{position:'relative'}}>
    <div className='navButtons d-flex align-items-center align-items-center'>


    {window.location.pathname!=='/' && <MDBBtn  onClick={()=>{
    route.push('/')
}} color='link'>Home</MDBBtn>}


<MDBBtn color='link' onMouseOver={()=>{
    //set content
    setCurrentSubNav(<ServicesContent/>)
    switchSubNav() 
 }}

>Services</MDBBtn>
<MDBBtn color='link'  onMouseOver={()=>{
    //set content
    setCurrentSubNav(<NFTs/>)
    switchSubNav() 
 }}
>NFT</MDBBtn>
<MDBBtn color='link' onMouseOver={()=>{
    //set content
    setCurrentSubNav(<P2E/>)
    switchSubNav() 
 }}>P2E</MDBBtn>
{/* <MDBBtn color='link'onMouseOver={()=>{
    //set content
    setCurrentSubNav(<Meterverse/>)
    switchSubNav() 
 }} >METAVERSE</MDBBtn> */}

<MDBBtn color='link' onClick={()=>{
    route.push('/About')
}}>ABOUT</MDBBtn>
<MDBBtn color='link' onClick={()=>{
    route.push('/Portfolio')
}}>PORTFOLIO</MDBBtn>
<MDBBtn  onClick={()=>{
    route.push('/Blogs')
}} color='link'>BLOG</MDBBtn>

    </div>
{showSubNav && <SubNavContainer  onMouseLeave={()=>{
     setShowSubNav(false)
}}
>{currentSubNav}</SubNavContainer>}

    </div>)
}

export default NavButtons;