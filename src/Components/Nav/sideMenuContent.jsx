import React, { useEffect, useState } from 'react';
import { MDBBtn } from 'mdb-react-ui-kit';
import { useRouter } from 'next/router';
const SideMenuContent = () => {
    const blockChainList = "Blockchain Services,Blockchain App Development,Blockchain Digital Certificate Services,Private Blockchain Development Services,Smart Contract Development,Blockchain POC Development,Blockchain Consulting Services,Blockchain DApps Development,Smart Contract Audit Services,Blockchain Airdrop Services,Hyperledger Blockchain Development Company,Supplychain Management Services,Cardano Blockchain Development Services";

    const [activeMenu, setActiveMenu] = useState(null);
    const route = useRouter();
    const nav = {
        Services: [
            {
                button: 'blockChain', 
                links: [
                    { name: "Blockchain Services", href: "/blockchainAppDevelopment" },
                    { name: "Blockchain App Development", href: "/blockchainAppDevelopment" },
                    { name: "Blockchain Digital Certificate Services", href: "/blockchainDigitalCertificationServices" },
                    { name: "Private Blockchain Development Services", href: "/privateServices" },
                    { name: "Smart Contract Development", href: "/smartContract" },
                    { name: "Blockchain POC Development", href: "/pocDevelopment" },
                    { name: "Blockchain DApps Development", href: "/DAppsDevelopment" },
                    { name: "Smart Contract Audit Services", href: "/smartAudit" },
                    { name: "Blockchain Airdrop Services", href: "/Airdrops" },
                    { name: "Hyperledger Blockchain Development Company", href: "/HyperledgerBlockchain" },

                ]
            },
            {
                button: 'Crypto',
                links: [
                    { name: "Crypto Wallet Development", href: "/CryptoWalletDev" },
                    { name: "Crypto Payment Gateway Development", href: "/PayementGateway" },
                    { name: "Crypto Exchange Platform Development", href: "/cryptoExchange" },
                    { name: "Crypto Cross Border Payment Services", href: "/CryptoBorder" },
                    { name: "Crypto Portfolio Management Services", href: "/CryptoPortfolio" },
                    { name: "Crypto Digital Marketing Services", href: "/CryptoDigital" },
                    { name: "Cryptocurrency Development Services", href: "/CryptoDevelopment" },
                    { name: "Crypto P2P Payment Development", href: "/CryptopP2P" },
                    { name: "Best Platforms For Launching An ICO", href: "/IOCDevelopment" },
                    { name: "ICO Development Services", href: "/IOCDevelopment" },

                ]
            },
            {
                button: 'DEFI',
                links: [
                    { name: "DeFi App Development Services", href: "/DefiAppDevelopement" },
                    { name: "DeFi Development Services", href: "/DefiBorrowing" },
                    // { name: "DeFi Lending and Borrowing Platform Development", href: "#" }
                ]
            },
            {
                button: "Exchange",
                links: [
                    { name: "Crypto currency exchange", href: "/cryptoExchange" },
                    { name: "P2P Crypto Exchange", href: "/P2PCryptoExchange" },
                    { name: "Derivatives exchange", href: "/DerivativesExchange" },
                    { name: "White label exchange ", href: "/WhiteLabelExchange" },
                    { name: "Decentralized exchange", href: "/DecentralizedExchange" },
                    { name: "Centralized exchange", href: "/CentralizedExchange" },
                    { name: "Margin Trading", href: "/MarginTrading" },
                    { name: "Crypto Market Making", href: "/CryptoMarketMaking" },

                ]
            }
        ],
        NFT: [
            { name: "NFT Token Development", href: '/NftToken' },
            { name: "NFT Ticketing Services", href: '/NFTTicketingServices' },
            { name: "NFT Game Development", href: '/NFTGame' },
            { name: "NFT Marketplace Development", href: '/NftMarket' },
            { name: "NFTs in DeFi Services", href: '/NftDeFiServices' },
            { name: "NFT Minting Platform Development", href: '/NftMinting' },
            { name: "Solana NFT Marketplace Development", href: '/SolalanNftDevelopment' },
            { name: "Fractional NFT Marketplace Development", href: '/FractionalNFT' },
            { name: "Crypto Dynasty Like NFT Game Development", href: '/NftCryptoDynesty' },
            { name: "NFT Marketplace Development", href: '/NftMarket' },

        ],
        //  Meterverse: [
        //      { name: "NFT Ticketing Services", href: '#' },
        //      { name: "NFT Marketplace Development", href: '/NftMarket' },
        //      { name: "NFT Miniting Platform Development", href: '#' },
        //      { name: "Fractional NFT Marketplace Development", href: '#' },
        //      { name: "NFT Token Development", href: '#' },
        //      { name: "NFT Game Development", href: '/NFTGame' },
        //      { name: "NFT Marketplace Development", href: '/NftMarket' },
        //      { name: "NFTs in DeFi Services", href: '#' },
        //      { name: "NFT Minting Platform Development", href: '#' },
        //      { name: "Solana NFT Marketplace Development", href: '#' },

        //  ],
        P2E: [
            { name: 'Sorare Like NFT Marketplace Development', href: '/SorereLikeDevelopment' },
            { name: 'Play to Earn Game Like Zed Run', href: '/ZedRun' },
            { name: "NFT Gaming Platform Like Axie Infinity", href: '/NFTGamePlatform' }
        ],
       "Bot Development":[

        { name: "Crypto Arbitrage Bot", href: "/CryptoArbitrageBot" },
        { name: "Crypto Trading Bots Development", href: "/CryptoTrading" },
        { name: "Bot Development", href: "/BotDevelopment" },
       ]
    
    }

    const [activeServiceMenu, setActiveServiceMenu] = useState("");
    const handleClick = (menu, e) => {
        if (activeMenu === menu && e.target.classList.contains('close')) {
            setActiveMenu(null);
        } else {
            setActiveMenu(menu);
        }
    };

    return (
        <div className='sideMenuContent'>
            <ul>

            <li style={{ listStyle: 'none' }}>
                        <MDBBtn color='tertiary' style={{ marginTop: 3 }} onClick={()=>route.push('/')} className='close'>{'Home'}</MDBBtn>
                     </li>

                {Object.keys(nav).map(key => (
                    <li key={key} style={{ listStyle: 'none' }}>
                        <MDBBtn color='tertiary' style={{ marginTop: 3 }} onClick={(e) => handleClick(key, e)} className='close'>{key}</MDBBtn>
                        {activeMenu === key && (
                            <ul>
                                {key == 'Services' ? nav.Services.map(service => (
                                    <li key={service.button} style={{ listStyle: 'none' }}>
                                        <MDBBtn color='tertiary' style={{ marginTop: 3 }} onClick={() => {
                                            if (service.button == activeServiceMenu) setActiveServiceMenu('');
                                            setActiveServiceMenu(service.button);
                                        }}>{service.button}</MDBBtn>
                                        {activeServiceMenu === service.button && (
                                            <ul>
                                                {service.links.map((link, index) => (
                                                    <MDBBtn onClick={() => {
                                                        route.push(link.href)
                                                    }} color='tertiary' style={{ marginTop: 3 }} key={index} >
                                                        <a >{link?.name || ""}</a>
                                                    </MDBBtn>
                                                ))}
                                            </ul>
                                        )}
                                    </li>
                                )) : nav[key].map((item, index) => (
                                    <MDBBtn onClick={() => {
                                        route.push(item.href)
                                    }} color='tertiary' key={index}>
                                        <a href={item.href}>{item?.name || ""}</a>
                                    </MDBBtn>
                                ))}
                            </ul>
                        )}
                    </li>
                ))}
                <li style={{ listStyle: 'none' }}>
                        <MDBBtn color='tertiary' style={{ marginTop: 3 }} onClick={()=>route.push('/About')} className='close'>{'About'}</MDBBtn>
                     </li>
                <li style={{ listStyle: 'none' }}>
                        <MDBBtn color='tertiary' style={{ marginTop: 3 }} onClick={()=>route.push('/Portfolio')} className='close'>{'Portfolio'}</MDBBtn>
                     </li>
                <li style={{ listStyle: 'none' }}>
                        <MDBBtn color='tertiary' style={{ marginTop: 3 }} onClick={()=>route.push('/Blogs')} className='close'>{'Blogs'}</MDBBtn>
                     </li>
                     <br/><br/>
                        <MDBBtn color='warning' rounded style={{textAlign:'center',borderRadius:10, marginTop: 3,width:'100%',border:'none',background:'var(--yellow)' }} onClick={()=>route.push('/contactUs')} className='close'>{'Get free demo'}</MDBBtn>
                    

            </ul>
        </div>
    );
};

export default SideMenuContent;

