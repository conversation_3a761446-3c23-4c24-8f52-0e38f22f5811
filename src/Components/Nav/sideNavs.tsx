import LogoWithText from '@/utils/LogoWithText';
import React, { useState } from 'react'
import SideMenuContent from './sideMenuContent';
interface Props{
    setMenuOpen:(prop:boolean)=>void;
}
const SideNav:React.FC<Props>=({setMenuOpen})=>{
    const [animation,setAnimation]=useState('openMenu');
    return (
        <>
        <div className='sideMenuContainer closeable' onClick={(e)=>{
if((e.target as HTMLDivElement).classList.contains('closeable')){
    setAnimation("closeMenu");
    setTimeout(()=>{
    setMenuOpen(false);
},800)
}
        }}>

<div className={`sideMenu`} style={{animationName:animation}}>
<LogoWithText text='ALTAPPLAP'/>
<SideMenuContent/>
    </div>


        </div>
        </>
    )
}

export default SideNav;