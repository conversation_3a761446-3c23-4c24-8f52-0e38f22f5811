import { IconButton } from '@mui/material'
import { MDBBtn } from 'mdb-react-ui-kit'
import React, { useState } from 'react'
import { Menu } from 'react-feather'
import SideNav from './sideNavs'
import WhatsAppIcon from '@/utils/whatsApp'
import TelegramIcon from '@/utils/telegram'
import { useRouter } from 'next/router'
import useClientWidth from '@/hooks/useClientWidth'


const MenuButton:React.FC=()=>{
    const { width, isClient } = useClientWidth();
    const [openMenu,setMenuOpen]=useState<boolean>(false)
    const route=useRouter()
    return (
        <div className='d-flex align-items-center'>

<div  style={{padding:10}}>
<TelegramIcon/>
</div>            
<div style={{padding:10}}><WhatsAppIcon/>
</div>

        <div>
            {isClient && width > 750 ? <MDBBtn size='lg' style={{background:"var(--blue)"}}  onClick={()=>{
        route.push('/contactUs')
    }}

    rounded>GET FREE DEMO</MDBBtn>:
<IconButton onClick={()=>{
    setMenuOpen(true);
}} style={{background:"white",borderRadius:6}}>
<Menu size={20} style={{color:"var(--bgColor)"}}/>
</IconButton>}
            </div>
           {openMenu && <SideNav setMenuOpen={setMenuOpen}/>}
            </div>
    )
}


export default MenuButton;