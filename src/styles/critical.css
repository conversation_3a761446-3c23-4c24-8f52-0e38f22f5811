/* Critical CSS for above-the-fold content */
/* This CSS will be inlined for fastest loading */

/* Reset and base styles */
* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

html {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  line-height: 1.6;
  -webkit-text-size-adjust: 100%;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

body {
  background-color: #020b12;
  color: #ffffff;
  overflow-x: hidden;
}

/* Navigation critical styles */
.topNav {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  background: rgba(2, 11, 18, 0.95);
  backdrop-filter: blur(10px);
  padding: 1rem 2rem;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

/* Logo styles */
.logo-container {
  display: flex;
  align-items: center;
  font-size: 1.5rem;
  font-weight: bold;
  color: #00d46c;
}

/* Button critical styles */
.btn-primary {
  background: linear-gradient(135deg, #00d46c, #00a855);
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 8px;
  color: white;
  font-weight: 600;
  cursor: pointer;
  transition: transform 0.2s ease;
  text-decoration: none;
  display: inline-block;
}

.btn-primary:hover {
  transform: translateY(-2px);
}

/* Hero section critical styles */
.hero-section {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 2rem;
  background: linear-gradient(135deg, #020b12 0%, #0a1a2a 100%);
}

/* Loading spinner */
.loading-spinner {
  width: 20px;
  height: 20px;
  border: 2px solid #00d46c;
  border-top: 2px solid transparent;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Utility classes */
.d-flex {
  display: flex;
}

.align-items-center {
  align-items: center;
}

.justify-content-center {
  justify-content: center;
}

.justify-content-between {
  justify-content: space-between;
}

.text-center {
  text-align: center;
}

/* Image optimization */
.optimized-image {
  transition: opacity 0.3s ease;
}

.optimized-image.loading {
  opacity: 0.7;
}

.optimized-image.loaded {
  opacity: 1;
}

/* Responsive utilities */
@media (max-width: 768px) {
  .topNav {
    padding: 0.5rem 1rem;
  }
  
  .hero-section {
    padding: 1rem;
  }
  
  .btn-primary {
    padding: 0.5rem 1rem;
    font-size: 0.9rem;
  }
}

/* Preload critical fonts */
@font-face {
  font-family: 'System';
  src: local('-apple-system'), local('BlinkMacSystemFont'), local('Segoe UI'), local('Roboto');
  font-display: swap;
}

/* Critical layout styles */
.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1rem;
}

/* Performance optimizations */
img {
  max-width: 100%;
  height: auto;
}

/* Reduce layout shift */
.aspect-ratio-16-9 {
  aspect-ratio: 16 / 9;
}

.aspect-ratio-1-1 {
  aspect-ratio: 1 / 1;
}

/* Critical color variables */
:root {
  --primary-color: #00d46c;
  --secondary-color: #00a855;
  --background-color: #020b12;
  --text-color: #ffffff;
  --border-color: rgba(255, 255, 255, 0.1);
}
