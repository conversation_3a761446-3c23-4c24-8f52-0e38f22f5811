#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

// Simple bundle analyzer to identify large files
function analyzeBundleSize() {
  const buildDir = path.join(process.cwd(), '.next');
  
  if (!fs.existsSync(buildDir)) {
    console.log('❌ Build directory not found. Run "npm run build" first.');
    return;
  }

  console.log('🔍 Analyzing bundle size...\n');

  // Analyze static files
  const staticDir = path.join(buildDir, 'static');
  if (fs.existsSync(staticDir)) {
    analyzeDirectory(staticDir, 'Static Files');
  }

  // Analyze server files
  const serverDir = path.join(buildDir, 'server');
  if (fs.existsSync(serverDir)) {
    analyzeDirectory(serverDir, 'Server Files');
  }

  console.log('\n✅ Bundle analysis complete!');
  console.log('\n💡 Tips for optimization:');
  console.log('- Files > 100KB should be code-split');
  console.log('- Images > 50KB should be optimized');
  console.log('- Consider lazy loading for non-critical components');
}

function analyzeDirectory(dir, title) {
  console.log(`\n📁 ${title}:`);
  console.log('─'.repeat(50));

  const files = getAllFiles(dir);
  const fileSizes = files.map(file => ({
    path: path.relative(process.cwd(), file),
    size: fs.statSync(file).size,
    type: path.extname(file)
  }));

  // Sort by size (largest first)
  fileSizes.sort((a, b) => b.size - a.size);

  // Show top 10 largest files
  fileSizes.slice(0, 10).forEach(file => {
    const sizeKB = (file.size / 1024).toFixed(2);
    const sizeIndicator = file.size > 100000 ? '🔴' : file.size > 50000 ? '🟡' : '🟢';
    console.log(`${sizeIndicator} ${sizeKB}KB - ${file.path}`);
  });

  // Summary by file type
  const typeStats = {};
  fileSizes.forEach(file => {
    if (!typeStats[file.type]) {
      typeStats[file.type] = { count: 0, totalSize: 0 };
    }
    typeStats[file.type].count++;
    typeStats[file.type].totalSize += file.size;
  });

  console.log('\n📊 Summary by file type:');
  Object.entries(typeStats).forEach(([type, stats]) => {
    const avgSize = (stats.totalSize / stats.count / 1024).toFixed(2);
    const totalSize = (stats.totalSize / 1024).toFixed(2);
    console.log(`${type || 'no-ext'}: ${stats.count} files, ${totalSize}KB total, ${avgSize}KB avg`);
  });
}

function getAllFiles(dir) {
  let files = [];
  
  try {
    const items = fs.readdirSync(dir);
    
    items.forEach(item => {
      const fullPath = path.join(dir, item);
      const stat = fs.statSync(fullPath);
      
      if (stat.isDirectory()) {
        files = files.concat(getAllFiles(fullPath));
      } else {
        files.push(fullPath);
      }
    });
  } catch (error) {
    // Ignore errors (permission issues, etc.)
  }
  
  return files;
}

// Performance recommendations
function showPerformanceRecommendations() {
  console.log('\n🚀 Performance Optimization Checklist:');
  console.log('─'.repeat(50));
  
  const recommendations = [
    '✅ Enable gzip/brotli compression',
    '✅ Optimize images (WebP/AVIF format)',
    '✅ Implement lazy loading for images',
    '✅ Code split large components',
    '✅ Use dynamic imports for non-critical code',
    '✅ Minimize CSS and JavaScript',
    '✅ Enable browser caching',
    '✅ Use CDN for static assets',
    '✅ Preload critical resources',
    '✅ Remove unused dependencies'
  ];

  recommendations.forEach(rec => console.log(rec));
}

// Check for common performance issues
function checkPerformanceIssues() {
  console.log('\n⚠️  Checking for common performance issues...');
  
  const packageJson = path.join(process.cwd(), 'package.json');
  if (fs.existsSync(packageJson)) {
    const pkg = JSON.parse(fs.readFileSync(packageJson, 'utf8'));
    const deps = { ...pkg.dependencies, ...pkg.devDependencies };
    
    // Check for heavy dependencies
    const heavyDeps = [
      'moment', 'lodash', 'jquery', 'bootstrap', 'material-ui'
    ];
    
    const foundHeavyDeps = heavyDeps.filter(dep => deps[dep]);
    if (foundHeavyDeps.length > 0) {
      console.log('🟡 Heavy dependencies found:', foundHeavyDeps.join(', '));
      console.log('   Consider lighter alternatives or tree shaking');
    }
    
    // Check for duplicate dependencies
    const reactDeps = Object.keys(deps).filter(dep => dep.includes('react'));
    if (reactDeps.length > 5) {
      console.log('🟡 Many React-related dependencies found');
      console.log('   Consider consolidating or removing unused ones');
    }
  }
}

// Main execution
if (require.main === module) {
  analyzeBundleSize();
  showPerformanceRecommendations();
  checkPerformanceIssues();
}
